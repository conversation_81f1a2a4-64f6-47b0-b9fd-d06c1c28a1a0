from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q, F
from .models import Warehouse, Location, Stock
from .serializers import WarehouseSerializer, LocationSerializer, StockSerializer


class WarehouseViewSet(viewsets.ModelViewSet):
    """API ViewSet for warehouses"""
    queryset = Warehouse.objects.filter(is_active=True)
    serializer_class = WarehouseSerializer


class LocationViewSet(viewsets.ModelViewSet):
    """API ViewSet for locations"""
    queryset = Location.objects.filter(is_active=True)
    serializer_class = LocationSerializer


class StockViewSet(viewsets.ModelViewSet):
    """API ViewSet for stock"""
    queryset = Stock.objects.all()
    serializer_class = StockSerializer


class LowStockAPIView(APIView):
    """API for low stock items"""
    
    def get(self, request):
        low_stock = Stock.objects.filter(
            quantity__lte=F('product__min_stock_level'),
            quantity__gt=0
        ).select_related('product', 'warehouse')
        
        serializer = StockSerializer(low_stock, many=True)
        return Response(serializer.data)


class StockByWarehouseAPIView(APIView):
    """API for stock by warehouse"""
    
    def get(self, request, warehouse_id):
        stock = Stock.objects.filter(
            warehouse_id=warehouse_id,
            quantity__gt=0
        ).select_related('product')
        
        serializer = StockSerializer(stock, many=True)
        return Response(serializer.data)


class LocationsByWarehouseAPIView(APIView):
    """API for locations by warehouse"""
    
    def get(self, request, warehouse_id):
        locations = Location.objects.filter(
            warehouse_id=warehouse_id,
            is_active=True
        )
        
        serializer = LocationSerializer(locations, many=True)
        return Response(serializer.data)
