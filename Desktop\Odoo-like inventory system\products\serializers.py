from rest_framework import serializers
from .models import Product, Category, Brand, ProductVariant, ProductImage


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'description', 'parent', 'is_active']


class BrandSerializer(serializers.ModelSerializer):
    class Meta:
        model = Brand
        fields = ['id', 'name', 'description', 'logo', 'website', 'is_active']


class ProductImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductImage
        fields = ['id', 'image', 'alt_text', 'is_primary', 'order']


class ProductVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = [
            'id', 'name', 'sku', 'barcode', 'cost_price', 'selling_price',
            'color', 'size', 'material', 'image', 'is_active'
        ]


class ProductSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    brand_name = serializers.CharField(source='brand.name', read_only=True)
    variants = ProductVariantSerializer(many=True, read_only=True)
    images = ProductImageSerializer(many=True, read_only=True)
    total_stock = serializers.IntegerField(read_only=True)
    profit_margin = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'sku', 'barcode', 'description', 'short_description',
            'category', 'category_name', 'brand', 'brand_name', 'product_type',
            'cost_price', 'selling_price', 'profit_margin', 'weight',
            'dimensions_length', 'dimensions_width', 'dimensions_height', 'unit',
            'track_inventory', 'min_stock_level', 'max_stock_level', 'total_stock',
            'image', 'barcode_image', 'is_active', 'is_featured',
            'variants', 'images', 'created_at', 'updated_at'
        ]
