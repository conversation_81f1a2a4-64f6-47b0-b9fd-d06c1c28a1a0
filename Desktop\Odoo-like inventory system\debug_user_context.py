#!/usr/bin/env python
"""
Debug user context across different apps and views
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.test import RequestFactory, Client
from django.contrib.auth import get_user
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware
from users.models import User
from django.http import HttpRequest

def debug_user_context():
    """Debug user context across different views"""
    print("🔍 Debugging User Context Across Different Apps...\n")
    
    try:
        # Get kenny user
        kenny = User.objects.get(username='kenny')
        print(f"✅ Kenny user found:")
        print(f"   Username: {kenny.username}")
        print(f"   ID: {kenny.id}")
        print(f"   Email: {kenny.email}")
        print(f"   Role: {kenny.get_role_display()}")
        
        # Check if there are any middleware or context processors that might affect user context
        print(f"\n🔧 Checking Django Settings:")
        
        from django.conf import settings
        
        print(f"📋 Middleware:")
        for middleware in settings.MIDDLEWARE:
            print(f"   - {middleware}")
        
        print(f"\n📋 Context Processors:")
        for template_setting in settings.TEMPLATES:
            if 'context_processors' in template_setting.get('OPTIONS', {}):
                for processor in template_setting['OPTIONS']['context_processors']:
                    print(f"   - {processor}")
        
        # Check if there are any custom authentication backends
        print(f"\n🔐 Authentication Backends:")
        for backend in settings.AUTHENTICATION_BACKENDS:
            print(f"   - {backend}")
        
        # Check session configuration
        print(f"\n🍪 Session Configuration:")
        print(f"   SESSION_ENGINE: {getattr(settings, 'SESSION_ENGINE', 'default')}")
        print(f"   SESSION_COOKIE_NAME: {getattr(settings, 'SESSION_COOKIE_NAME', 'sessionid')}")
        print(f"   SESSION_COOKIE_AGE: {getattr(settings, 'SESSION_COOKIE_AGE', 1209600)}")
        
        # Test different view imports to see if there are any issues
        print(f"\n📦 Testing View Imports:")
        
        try:
            from inventory.views import dashboard
            print(f"   ✅ inventory.views.dashboard imported successfully")
        except Exception as e:
            print(f"   ❌ inventory.views.dashboard import error: {e}")
        
        try:
            from products.views import ProductListView
            print(f"   ✅ products.views.ProductListView imported successfully")
        except Exception as e:
            print(f"   ❌ products.views.ProductListView import error: {e}")
        
        try:
            from warehouses.views import WarehouseListView
            print(f"   ✅ warehouses.views.WarehouseListView imported successfully")
        except Exception as e:
            print(f"   ❌ warehouses.views.WarehouseListView import error: {e}")
        
        try:
            from sales.views import SalesOrderListView
            print(f"   ✅ sales.views.SalesOrderListView imported successfully")
        except Exception as e:
            print(f"   ❌ sales.views.SalesOrderListView import error: {e}")
        
        # Check if there are any decorators or mixins that might affect user context
        print(f"\n🎭 Checking View Decorators and Mixins:")
        
        # Check inventory views
        import inspect
        from inventory import views as inventory_views
        
        dashboard_func = getattr(inventory_views, 'dashboard', None)
        if dashboard_func:
            print(f"   📊 Dashboard view:")
            print(f"      Function: {dashboard_func.__name__}")
            if hasattr(dashboard_func, '__wrapped__'):
                print(f"      Decorated: Yes")
            else:
                print(f"      Decorated: No")
        
        # Check products views
        from products import views as products_views
        
        product_list_view = getattr(products_views, 'ProductListView', None)
        if product_list_view:
            print(f"   📦 ProductListView:")
            print(f"      Class: {product_list_view.__name__}")
            print(f"      MRO: {[cls.__name__ for cls in product_list_view.__mro__]}")
        
        # Check for any custom user context in views
        print(f"\n👤 Checking for Custom User Context:")
        
        # Create a mock request to test user context
        factory = RequestFactory()
        request = factory.get('/')
        
        # Add session middleware
        middleware = SessionMiddleware(lambda req: None)
        middleware.process_request(request)
        request.session.save()
        
        # Add authentication middleware
        auth_middleware = AuthenticationMiddleware(lambda req: None)
        request.user = kenny
        
        print(f"   Mock request user: {request.user.username}")
        print(f"   Mock request user ID: {request.user.id}")
        
        return True
        
    except User.DoesNotExist:
        print("❌ Kenny user not found!")
        return False
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_template_context():
    """Check if there are any template context issues"""
    print(f"\n🎨 Checking Template Context Issues:")
    
    try:
        # Check if base template has any hardcoded user references
        base_template_path = r"Desktop\Odoo-like inventory system\templates\base.html"
        
        with open(base_template_path, 'r', encoding='utf-8') as f:
            base_content = f.read()
        
        # Look for any hardcoded user references
        if 'admin' in base_content.lower() and '{{ user.username }}' not in base_content:
            print(f"   ⚠️  Base template might have hardcoded 'admin' references")
        else:
            print(f"   ✅ Base template uses {{ user.username }} correctly")
        
        # Check specific app templates
        templates_to_check = [
            ('Dashboard', r'Desktop\Odoo-like inventory system\templates\inventory\dashboard.html'),
            ('Products', r'Desktop\Odoo-like inventory system\templates\products\product_list.html'),
            ('Warehouses', r'Desktop\Odoo-like inventory system\templates\warehouses\warehouse_list.html'),
        ]
        
        for template_name, template_path in templates_to_check:
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for hardcoded user references
                if 'admin' in content.lower() and 'admin panel' not in content.lower():
                    print(f"   ⚠️  {template_name} template might have hardcoded 'admin'")
                elif 'kenny' in content.lower():
                    print(f"   ⚠️  {template_name} template might have hardcoded 'kenny'")
                else:
                    print(f"   ✅ {template_name} template looks clean")
                    
            except FileNotFoundError:
                print(f"   ❌ {template_name} template not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking template context: {e}")
        return False

def provide_specific_solution():
    """Provide specific solution for the dashboard vs other pages issue"""
    print(f"\n💡 Specific Solution for Dashboard vs Other Pages Issue:")
    print(f"")
    print(f"🎯 The Issue:")
    print(f"   - Dashboard shows: kenny")
    print(f"   - Other pages show: admin")
    print(f"   - This suggests session/context inconsistency")
    print(f"")
    print(f"🔧 Immediate Fix Steps:")
    print(f"   1. Completely close your browser")
    print(f"   2. Clear all browser data (cache, cookies, sessions)")
    print(f"   3. Open browser in incognito/private mode")
    print(f"   4. Go to: http://127.0.0.1:8000/login/")
    print(f"   5. Login ONLY with kenny credentials")
    print(f"   6. Test all pages in the same browser session")
    print(f"")
    print(f"🔍 If still inconsistent:")
    print(f"   - There might be cached authentication in different apps")
    print(f"   - Try restarting the Django server")
    print(f"   - Use a completely different browser")
    print(f"")
    print(f"⚡ Quick Test:")
    print(f"   After fresh login, check these URLs in order:")
    print(f"   1. http://127.0.0.1:8000/dashboard/ (should show kenny)")
    print(f"   2. http://127.0.0.1:8000/products/ (should show kenny)")
    print(f"   3. http://127.0.0.1:8000/warehouses/list/ (should show kenny)")
    print(f"   4. http://127.0.0.1:8000/sales/orders/ (should show kenny)")

if __name__ == "__main__":
    print("🚀 User Context Debugging\n")
    
    debug_success = debug_user_context()
    template_success = check_template_context()
    provide_specific_solution()
    
    print(f"\n📋 Debug Results:")
    print(f"   Context Debug: {'✅ COMPLETED' if debug_success else '❌ FAILED'}")
    print(f"   Template Check: {'✅ COMPLETED' if template_success else '❌ FAILED'}")
    
    if debug_success and template_success:
        print(f"\n🎉 Debugging completed!")
        print(f"✅ Follow the specific solution steps above")
        print(f"✅ The issue is likely browser session caching")
    else:
        print(f"\n⚠️  Debugging encountered issues")
