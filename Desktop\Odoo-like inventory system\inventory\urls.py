from django.urls import path
from . import views

app_name = 'inventory'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),
    
    # Stock Movements
    path('movements/', views.StockMovementListView.as_view(), name='movement_list'),
    path('movements/create/', views.stock_movement_create, name='movement_create'),
    path('movements/<int:pk>/', views.StockMovementDetailView.as_view(), name='movement_detail'),
    path('movements/<int:pk>/process/', views.process_movement, name='process_movement'),
    
    # Quick Operations
    path('quick-in/', views.quick_stock_in, name='quick_stock_in'),
    path('quick-out/', views.quick_stock_out, name='quick_stock_out'),
    path('transfer/', views.stock_transfer, name='stock_transfer'),
    
    # Stock Adjustments
    path('adjustments/', views.StockAdjustmentListView.as_view(), name='adjustment_list'),
    path('adjustments/create/', views.stock_adjustment_create, name='adjustment_create'),
    path('adjustments/<int:pk>/', views.StockAdjustmentDetailView.as_view(), name='adjustment_detail'),
    path('adjustments/<int:pk>/approve/', views.approve_adjustment, name='approve_adjustment'),
    
    # Reports
    path('reports/stock/', views.stock_report, name='stock_report'),
    
    # AJAX
    path('ajax/product-stock/', views.get_product_stock_ajax, name='product_stock_ajax'),
    path('ajax/movement-stats/', views.movement_stats_ajax, name='movement_stats_ajax'),
]
