from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import api_views

router = DefaultRouter()
router.register(r'users', api_views.UserViewSet)
router.register(r'profiles', api_views.UserProfileViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('auth/login/', api_views.LoginAPIView.as_view(), name='api_login'),
    path('auth/logout/', api_views.LogoutAPIView.as_view(), name='api_logout'),
    path('auth/profile/', api_views.ProfileAPIView.as_view(), name='api_profile'),
]
