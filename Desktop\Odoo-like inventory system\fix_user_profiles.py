#!/usr/bin/env python
"""
Fix duplicate UserProfile issues
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from users.models import User, UserProfile

def fix_user_profiles():
    """Fix duplicate UserProfile issues"""
    print("🔧 Fixing UserProfile Issues...\n")
    
    try:
        # Check current state
        total_users = User.objects.count()
        total_profiles = UserProfile.objects.count()
        
        print(f"📊 Current State:")
        print(f"   Total Users: {total_users}")
        print(f"   Total Profiles: {total_profiles}")
        
        # Find users without profiles
        users_without_profiles = []
        for user in User.objects.all():
            try:
                profile = user.profile
            except UserProfile.DoesNotExist:
                users_without_profiles.append(user)
        
        print(f"   Users without profiles: {len(users_without_profiles)}")
        
        # Find duplicate profiles (shouldn't happen with OneToOneField, but let's check)
        duplicate_profiles = []
        user_ids_with_profiles = []
        
        for profile in UserProfile.objects.all():
            if profile.user_id in user_ids_with_profiles:
                duplicate_profiles.append(profile)
            else:
                user_ids_with_profiles.append(profile.user_id)
        
        print(f"   Duplicate profiles: {len(duplicate_profiles)}")
        
        # Fix issues
        fixes_applied = []
        
        # Create missing profiles
        if users_without_profiles:
            print(f"\n🔨 Creating missing profiles:")
            for user in users_without_profiles:
                try:
                    profile, created = UserProfile.objects.get_or_create(user=user)
                    if created:
                        print(f"   ✅ Created profile for: {user.username}")
                        fixes_applied.append(f"Created profile for {user.username}")
                    else:
                        print(f"   ℹ️  Profile already exists for: {user.username}")
                except Exception as e:
                    print(f"   ❌ Error creating profile for {user.username}: {e}")
        
        # Remove duplicate profiles
        if duplicate_profiles:
            print(f"\n🗑️  Removing duplicate profiles:")
            for profile in duplicate_profiles:
                try:
                    username = profile.user.username if profile.user else "Unknown"
                    profile.delete()
                    print(f"   ✅ Removed duplicate profile for: {username}")
                    fixes_applied.append(f"Removed duplicate profile for {username}")
                except Exception as e:
                    print(f"   ❌ Error removing duplicate profile: {e}")
        
        # Final state check
        print(f"\n📊 Final State:")
        final_users = User.objects.count()
        final_profiles = UserProfile.objects.count()
        print(f"   Total Users: {final_users}")
        print(f"   Total Profiles: {final_profiles}")
        
        # Verify all users have profiles
        users_still_without_profiles = []
        for user in User.objects.all():
            try:
                profile = user.profile
            except UserProfile.DoesNotExist:
                users_still_without_profiles.append(user)
        
        print(f"   Users without profiles: {len(users_still_without_profiles)}")
        
        if len(users_still_without_profiles) == 0 and final_users == final_profiles:
            print(f"\n🎉 SUCCESS: All users now have exactly one profile!")
        else:
            print(f"\n⚠️  Some issues remain:")
            for user in users_still_without_profiles:
                print(f"      - {user.username} still missing profile")
        
        # Summary
        print(f"\n📋 Fixes Applied:")
        if fixes_applied:
            for fix in fixes_applied:
                print(f"   ✅ {fix}")
        else:
            print(f"   ℹ️  No fixes needed - everything was already correct")
        
        return len(users_still_without_profiles) == 0
        
    except Exception as e:
        print(f"❌ Error fixing user profiles: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_admin_user_creation():
    """Test that we can now create users through admin without errors"""
    print(f"\n🧪 Testing User Creation Process:")
    
    try:
        # Test creating a user programmatically (simulates admin creation)
        test_username = "test_admin_user"
        
        # Clean up any existing test user
        try:
            existing_user = User.objects.get(username=test_username)
            existing_user.delete()
            print(f"   🗑️  Cleaned up existing test user")
        except User.DoesNotExist:
            pass
        
        # Create new user
        test_user = User.objects.create_user(
            username=test_username,
            email="<EMAIL>",
            password="testpass123",
            role="employee"
        )
        
        print(f"   ✅ Created test user: {test_user.username}")
        
        # Check if profile was created automatically
        try:
            profile = test_user.profile
            print(f"   ✅ Profile automatically created for test user")
        except UserProfile.DoesNotExist:
            print(f"   ❌ Profile NOT created for test user")
            return False
        
        # Clean up test user
        test_user.delete()
        print(f"   🗑️  Cleaned up test user")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing user creation: {e}")
        return False

if __name__ == "__main__":
    print("🚀 UserProfile Fix Script\n")
    
    fix_success = fix_user_profiles()
    test_success = test_admin_user_creation()
    
    print(f"\n📋 Final Results:")
    print(f"   Profile Fix: {'✅ SUCCESS' if fix_success else '❌ FAILED'}")
    print(f"   Creation Test: {'✅ SUCCESS' if test_success else '❌ FAILED'}")
    
    if fix_success and test_success:
        print(f"\n🎉 UserProfile issues fixed!")
        print(f"✅ You can now add users through the admin panel")
        print(f"✅ No more UNIQUE constraint errors")
        print(f"✅ All users have exactly one profile")
        print(f"\n🌐 Test adding a user: http://127.0.0.1:8000/admin/users/user/add/")
    else:
        print(f"\n⚠️  Some issues remain")
        print(f"🔧 Review the errors above")
