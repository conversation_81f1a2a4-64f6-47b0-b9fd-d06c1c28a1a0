import os
import barcode
import qrcode
from barcode.writer import ImageWriter
from django.conf import settings
from django.core.files.storage import default_storage
from PIL import Image, ImageDraw, ImageFont
import uuid


def generate_barcode(barcode_data, filename_prefix="barcode"):
    """
    Generate barcode image for a product
    """
    try:
        # Create barcode directory if it doesn't exist
        barcode_dir = os.path.join(settings.MEDIA_ROOT, 'barcodes')
        os.makedirs(barcode_dir, exist_ok=True)
        
        # Generate barcode
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(barcode_data, writer=ImageWriter())
        
        # Save barcode image
        filename = f"{filename_prefix}_{uuid.uuid4().hex[:8]}"
        filepath = os.path.join(barcode_dir, filename)
        barcode_instance.save(filepath)
        
        # Return relative path for database storage
        return f"barcodes/{filename}.png"
        
    except Exception as e:
        raise Exception(f"Error generating barcode: {str(e)}")


def generate_qr_code(data, filename_prefix="qr"):
    """
    Generate QR code for product information
    """
    try:
        # Create QR code directory if it doesn't exist
        qr_dir = os.path.join(settings.MEDIA_ROOT, 'qrcodes')
        os.makedirs(qr_dir, exist_ok=True)
        
        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        
        # Create QR code image
        qr_image = qr.make_image(fill_color="black", back_color="white")
        
        # Save QR code image
        filename = f"{filename_prefix}_{uuid.uuid4().hex[:8]}.png"
        filepath = os.path.join(qr_dir, filename)
        qr_image.save(filepath)
        
        # Return relative path for database storage
        return f"qrcodes/{filename}"
        
    except Exception as e:
        raise Exception(f"Error generating QR code: {str(e)}")


def generate_product_label(product, include_barcode=True, include_qr=False):
    """
    Generate a product label with barcode/QR code
    """
    try:
        # Create label directory if it doesn't exist
        label_dir = os.path.join(settings.MEDIA_ROOT, 'labels')
        os.makedirs(label_dir, exist_ok=True)
        
        # Label dimensions (in pixels)
        label_width = 400
        label_height = 200
        
        # Create new image
        label = Image.new('RGB', (label_width, label_height), 'white')
        draw = ImageDraw.Draw(label)
        
        # Try to load a font, fallback to default if not available
        try:
            font_large = ImageFont.truetype("arial.ttf", 16)
            font_medium = ImageFont.truetype("arial.ttf", 12)
            font_small = ImageFont.truetype("arial.ttf", 10)
        except:
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # Draw product information
        y_position = 10
        
        # Product name
        draw.text((10, y_position), product.name[:30], fill='black', font=font_large)
        y_position += 25
        
        # SKU
        draw.text((10, y_position), f"SKU: {product.sku}", fill='black', font=font_medium)
        y_position += 20
        
        # Price
        draw.text((10, y_position), f"Price: ${product.selling_price}", fill='black', font=font_medium)
        y_position += 20
        
        # Barcode
        if include_barcode and product.barcode:
            try:
                # Generate small barcode
                code128 = barcode.get_barcode_class('code128')
                barcode_instance = code128(product.barcode, writer=ImageWriter())
                
                # Create temporary barcode image
                temp_barcode_path = os.path.join(label_dir, f"temp_barcode_{uuid.uuid4().hex[:8]}.png")
                barcode_instance.save(temp_barcode_path)
                
                # Open and resize barcode
                barcode_img = Image.open(f"{temp_barcode_path}.png")
                barcode_img = barcode_img.resize((200, 50))
                
                # Paste barcode onto label
                label.paste(barcode_img, (10, y_position))
                
                # Clean up temporary file
                os.remove(f"{temp_barcode_path}.png")
                
            except Exception as e:
                print(f"Error adding barcode to label: {e}")
        
        # Save label
        filename = f"label_{product.sku}_{uuid.uuid4().hex[:8]}.png"
        filepath = os.path.join(label_dir, filename)
        label.save(filepath)
        
        return f"labels/{filename}"
        
    except Exception as e:
        raise Exception(f"Error generating product label: {str(e)}")


def validate_sku(sku):
    """
    Validate SKU format and uniqueness
    """
    from .models import Product, ProductVariant
    
    # Check format (alphanumeric, hyphens, underscores allowed)
    import re
    if not re.match(r'^[A-Za-z0-9_-]+$', sku):
        return False, "SKU can only contain letters, numbers, hyphens, and underscores"
    
    # Check length
    if len(sku) < 3 or len(sku) > 50:
        return False, "SKU must be between 3 and 50 characters"
    
    # Check uniqueness in products
    if Product.objects.filter(sku=sku).exists():
        return False, "SKU already exists in products"
    
    # Check uniqueness in product variants
    if ProductVariant.objects.filter(sku=sku).exists():
        return False, "SKU already exists in product variants"
    
    return True, "SKU is valid"


def generate_unique_sku(prefix="PRD"):
    """
    Generate a unique SKU with given prefix
    """
    from .models import Product, ProductVariant
    import random
    import string
    
    max_attempts = 100
    for _ in range(max_attempts):
        # Generate random suffix
        suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))
        sku = f"{prefix}-{suffix}"
        
        # Check if unique
        if not Product.objects.filter(sku=sku).exists() and not ProductVariant.objects.filter(sku=sku).exists():
            return sku
    
    # If we couldn't generate a unique SKU, use timestamp
    import time
    timestamp = str(int(time.time()))
    return f"{prefix}-{timestamp}"


def calculate_product_metrics(product):
    """
    Calculate various metrics for a product
    """
    from warehouses.models import Stock
    from inventory.models import StockMovement
    from django.db.models import Sum, Avg
    from datetime import datetime, timedelta
    
    metrics = {}
    
    # Stock metrics
    stock_records = Stock.objects.filter(product=product)
    metrics['total_stock'] = stock_records.aggregate(total=Sum('quantity'))['total'] or 0
    metrics['total_reserved'] = stock_records.aggregate(total=Sum('reserved_quantity'))['total'] or 0
    metrics['available_stock'] = metrics['total_stock'] - metrics['total_reserved']
    
    # Value metrics
    metrics['total_stock_value'] = metrics['total_stock'] * product.cost_price
    metrics['profit_margin'] = product.profit_margin
    
    # Movement metrics (last 30 days)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    recent_movements = StockMovement.objects.filter(
        product=product,
        created_at__gte=thirty_days_ago
    )
    
    metrics['movements_in'] = recent_movements.filter(movement_type='in').aggregate(
        total=Sum('quantity')
    )['total'] or 0
    
    metrics['movements_out'] = recent_movements.filter(movement_type='out').aggregate(
        total=Sum('quantity')
    )['total'] or 0
    
    # Turnover rate (simplified)
    if metrics['total_stock'] > 0:
        metrics['turnover_rate'] = metrics['movements_out'] / metrics['total_stock']
    else:
        metrics['turnover_rate'] = 0
    
    return metrics


def import_products_from_csv(csv_file, update_existing=False):
    """
    Import products from CSV file
    """
    import csv
    import io
    from .models import Product, Category, Brand
    
    results = {
        'success': 0,
        'errors': 0,
        'updated': 0,
        'messages': []
    }
    
    try:
        # Read CSV file
        csv_data = csv_file.read().decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(csv_data))
        
        for row_num, row in enumerate(csv_reader, start=2):
            try:
                # Required fields
                name = row.get('name', '').strip()
                sku = row.get('sku', '').strip()
                cost_price = row.get('cost_price', '').strip()
                selling_price = row.get('selling_price', '').strip()
                
                if not all([name, sku, cost_price, selling_price]):
                    results['messages'].append(f"Row {row_num}: Missing required fields")
                    results['errors'] += 1
                    continue
                
                # Check if product exists
                existing_product = None
                try:
                    existing_product = Product.objects.get(sku=sku)
                except Product.DoesNotExist:
                    pass
                
                if existing_product and not update_existing:
                    results['messages'].append(f"Row {row_num}: Product with SKU {sku} already exists")
                    results['errors'] += 1
                    continue
                
                # Get or create category
                category = None
                category_name = row.get('category', '').strip()
                if category_name:
                    category, _ = Category.objects.get_or_create(
                        name=category_name,
                        defaults={'is_active': True}
                    )
                
                # Get or create brand
                brand = None
                brand_name = row.get('brand', '').strip()
                if brand_name:
                    brand, _ = Brand.objects.get_or_create(
                        name=brand_name,
                        defaults={'is_active': True}
                    )
                
                # Create or update product
                product_data = {
                    'name': name,
                    'sku': sku,
                    'cost_price': float(cost_price),
                    'selling_price': float(selling_price),
                    'category': category,
                    'brand': brand,
                    'description': row.get('description', '').strip(),
                    'barcode': row.get('barcode', '').strip(),
                    'unit': row.get('unit', 'pcs').strip(),
                    'min_stock_level': int(row.get('min_stock_level', 0) or 0),
                    'is_active': row.get('is_active', 'true').lower() == 'true',
                }
                
                if existing_product:
                    # Update existing product
                    for field, value in product_data.items():
                        setattr(existing_product, field, value)
                    existing_product.save()
                    results['updated'] += 1
                else:
                    # Create new product
                    Product.objects.create(**product_data)
                    results['success'] += 1
                
            except Exception as e:
                results['messages'].append(f"Row {row_num}: {str(e)}")
                results['errors'] += 1
    
    except Exception as e:
        results['messages'].append(f"Error reading CSV file: {str(e)}")
        results['errors'] += 1
    
    return results
