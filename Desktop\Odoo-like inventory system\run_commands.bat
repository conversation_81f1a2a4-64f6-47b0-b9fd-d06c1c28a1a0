@echo off
REM Inventory Management System - Command Runner
REM This batch file helps run Django commands without PowerShell issues

echo ========================================
echo  Inventory Management System Commands
echo ========================================
echo.

if "%1"=="server" goto server
if "%1"=="migrate" goto migrate
if "%1"=="static" goto static
if "%1"=="superuser" goto superuser
if "%1"=="test" goto test
if "%1"=="shell" goto shell
if "%1"=="help" goto help
if "%1"=="" goto help

:help
echo Available commands:
echo.
echo   run_commands.bat server     - Start Django development server
echo   run_commands.bat migrate    - Run database migrations
echo   run_commands.bat static     - Collect static files
echo   run_commands.bat superuser  - Create superuser account
echo   run_commands.bat test       - Run browser compatibility tests
echo   run_commands.bat shell      - Open Django shell
echo   run_commands.bat help       - Show this help message
echo.
echo Examples:
echo   run_commands.bat server
echo   run_commands.bat migrate
echo   run_commands.bat static
echo.
goto end

:server
echo Starting Django development server...
echo Server will be available at: http://127.0.0.1:8000/
echo Press Ctrl+C to stop the server
echo.
.\odoo_venv\Scripts\python.exe manage.py runserver 127.0.0.1:8000
goto end

:migrate
echo Running database migrations...
.\odoo_venv\Scripts\python.exe manage.py makemigrations
.\odoo_venv\Scripts\python.exe manage.py migrate
echo Migrations completed!
goto end

:static
echo Collecting static files...
.\odoo_venv\Scripts\python.exe manage.py collectstatic --noinput
echo Static files collected!
goto end

:superuser
echo Creating superuser account...
.\odoo_venv\Scripts\python.exe manage.py createsuperuser
goto end

:test
echo Running browser compatibility tests...
.\odoo_venv\Scripts\python.exe test_browser_compatibility.py
echo.
echo Running export functionality tests...
.\odoo_venv\Scripts\python.exe test_export_functionality.py
echo.
echo Running static files tests...
.\odoo_venv\Scripts\python.exe test_static_files.py
echo.
echo All tests completed!
goto end

:shell
echo Opening Django shell...
.\odoo_venv\Scripts\python.exe manage.py shell
goto end

:end
echo.
echo ========================================
echo  Command completed!
echo ========================================
