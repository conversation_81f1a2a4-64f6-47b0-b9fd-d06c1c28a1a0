from django import forms
from django.core.exceptions import ValidationError
from .models import Warehouse, Location, Stock
from users.models import User
from utils.widgets import PhoneNumberField, CountryField


class WarehouseForm(forms.ModelForm):
    """Form for creating and updating warehouses"""

    # Custom fields with enhanced widgets
    phone = PhoneNumberField(required=False, label="Phone Number")
    country = CountryField(label="Country")

    class Meta:
        model = Warehouse
        fields = [
            'name', 'warehouse_type', 'address_line1', 'address_line2',
            'city', 'state', 'postal_code', 'country', 'phone', 'email',
            'manager', 'capacity', 'is_active', 'is_default'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'warehouse_type': forms.Select(attrs={'class': 'form-control'}),
            'address_line1': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line2': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'state': forms.TextInput(attrs={'class': 'form-control'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'manager': forms.Select(attrs={'class': 'form-control'}),
            'capacity': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_default': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter managers to only show users who can manage inventory
        self.fields['manager'].queryset = User.objects.filter(
            role__in=['admin', 'manager', 'warehouse_staff'],
            is_active_employee=True
        )
        self.fields['manager'].empty_label = "Select Manager"

        # Set default country
        if not self.instance.pk:
            self.fields['country'].initial = 'US'
    
    def clean(self):
        cleaned_data = super().clean()
        is_default = cleaned_data.get('is_default')
        
        # Ensure only one default warehouse
        if is_default:
            existing_default = Warehouse.objects.filter(is_default=True)
            if self.instance.pk:
                existing_default = existing_default.exclude(pk=self.instance.pk)
            
            if existing_default.exists():
                # Auto-unset other default warehouses
                existing_default.update(is_default=False)
        
        return cleaned_data


class LocationForm(forms.ModelForm):
    """Form for creating and updating locations"""
    
    class Meta:
        model = Location
        fields = [
            'name', 'code', 'location_type', 'parent', 'capacity',
            'length', 'width', 'height', 'is_active', 'is_pickable', 'is_receivable'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'style': 'text-transform: uppercase;'}),
            'location_type': forms.Select(attrs={'class': 'form-control'}),
            'parent': forms.Select(attrs={'class': 'form-control'}),
            'capacity': forms.NumberInput(attrs={'class': 'form-control'}),
            'length': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'width': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'height': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_pickable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_receivable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        self.warehouse = kwargs.pop('warehouse', None)
        super().__init__(*args, **kwargs)
        
        if self.warehouse:
            # Filter parent locations to only show locations in the same warehouse
            self.fields['parent'].queryset = Location.objects.filter(
                warehouse=self.warehouse,
                is_active=True
            )
            
            # Exclude self from parent choices when editing
            if self.instance.pk:
                self.fields['parent'].queryset = self.fields['parent'].queryset.exclude(pk=self.instance.pk)
        
        self.fields['parent'].empty_label = "No Parent Location"
    
    def clean_code(self):
        code = self.cleaned_data.get('code', '').upper()
        
        if not self.warehouse:
            raise ValidationError("Warehouse is required.")
        
        # Check if code already exists in the same warehouse (excluding current instance)
        query = Location.objects.filter(warehouse=self.warehouse, code=code)
        if self.instance.pk:
            query = query.exclude(pk=self.instance.pk)
        
        if query.exists():
            raise ValidationError("A location with this code already exists in this warehouse.")
        
        # Validate code format
        import re
        if not re.match(r'^[A-Z0-9-]+$', code):
            raise ValidationError("Code must contain only uppercase letters, numbers, and hyphens.")
        
        if len(code) < 1 or len(code) > 20:
            raise ValidationError("Code must be between 1 and 20 characters.")
        
        return code
    
    def clean_parent(self):
        parent = self.cleaned_data.get('parent')
        
        if parent and parent == self.instance:
            raise ValidationError("A location cannot be its own parent.")
        
        # Check for circular references
        if parent and self.instance.pk:
            current = parent
            while current:
                if current == self.instance:
                    raise ValidationError("This would create a circular reference.")
                current = current.parent
        
        return parent
    
    def save(self, commit=True):
        location = super().save(commit=False)
        if self.warehouse:
            location.warehouse = self.warehouse
        if commit:
            location.save()
        return location


class StockForm(forms.ModelForm):
    """Form for creating and updating stock records"""
    
    class Meta:
        model = Stock
        fields = ['product', 'product_variant', 'location', 'quantity', 'reserved_quantity']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-control'}),
            'product_variant': forms.Select(attrs={'class': 'form-control'}),
            'location': forms.Select(attrs={'class': 'form-control'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control'}),
            'reserved_quantity': forms.NumberInput(attrs={'class': 'form-control'}),
        }
    
    def __init__(self, *args, **kwargs):
        self.warehouse = kwargs.pop('warehouse', None)
        super().__init__(*args, **kwargs)
        
        if self.warehouse:
            # Filter locations to only show locations in the same warehouse
            self.fields['location'].queryset = Location.objects.filter(
                warehouse=self.warehouse,
                is_active=True,
                is_receivable=True
            )
        
        self.fields['location'].empty_label = "Select Location"
        self.fields['product_variant'].empty_label = "No Variant"
    
    def clean(self):
        cleaned_data = super().clean()
        quantity = cleaned_data.get('quantity', 0)
        reserved_quantity = cleaned_data.get('reserved_quantity', 0)
        
        if reserved_quantity > quantity:
            raise ValidationError("Reserved quantity cannot be greater than total quantity.")
        
        return cleaned_data


class WarehouseSearchForm(forms.Form):
    """Form for searching and filtering warehouses"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name, code, or city...'
        })
    )
    type = forms.ChoiceField(
        choices=[('', 'All Types')] + Warehouse.WAREHOUSE_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[
            ('', 'All Warehouses'),
            ('active', 'Active'),
            ('inactive', 'Inactive'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class LocationSearchForm(forms.Form):
    """Form for searching and filtering locations"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name or code...'
        })
    )
    type = forms.ChoiceField(
        choices=[('', 'All Types')] + Location.LOCATION_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[
            ('', 'All Locations'),
            ('active', 'Active'),
            ('inactive', 'Inactive'),
            ('pickable', 'Pickable'),
            ('receivable', 'Receivable'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class StockSearchForm(forms.Form):
    """Form for searching and filtering stock"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by product name, SKU, or barcode...'
        })
    )
    location = forms.ModelChoiceField(
        queryset=Location.objects.none(),
        required=False,
        empty_label="All Locations",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[
            ('', 'All Stock'),
            ('in_stock', 'In Stock'),
            ('low_stock', 'Low Stock'),
            ('out_of_stock', 'Out of Stock'),
            ('reserved', 'Has Reserved'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    def __init__(self, *args, **kwargs):
        warehouse = kwargs.pop('warehouse', None)
        super().__init__(*args, **kwargs)
        
        if warehouse:
            self.fields['location'].queryset = Location.objects.filter(
                warehouse=warehouse,
                is_active=True
            )


class StockTransferForm(forms.Form):
    """Form for transferring stock between locations"""
    from_location = forms.ModelChoiceField(
        queryset=Location.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="From Location"
    )
    to_location = forms.ModelChoiceField(
        queryset=Location.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="To Location"
    )
    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'}),
        label="Quantity to Transfer"
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        label="Transfer Notes"
    )
    
    def __init__(self, *args, **kwargs):
        self.warehouse = kwargs.pop('warehouse', None)
        self.stock = kwargs.pop('stock', None)
        super().__init__(*args, **kwargs)
        
        if self.warehouse:
            # Filter locations to only show locations in the same warehouse
            pickable_locations = Location.objects.filter(
                warehouse=self.warehouse,
                is_active=True,
                is_pickable=True
            )
            receivable_locations = Location.objects.filter(
                warehouse=self.warehouse,
                is_active=True,
                is_receivable=True
            )
            
            self.fields['from_location'].queryset = pickable_locations
            self.fields['to_location'].queryset = receivable_locations
            
            # Set initial from_location if stock is provided
            if self.stock and self.stock.location:
                self.fields['from_location'].initial = self.stock.location
    
    def clean(self):
        cleaned_data = super().clean()
        from_location = cleaned_data.get('from_location')
        to_location = cleaned_data.get('to_location')
        quantity = cleaned_data.get('quantity')
        
        if from_location == to_location:
            raise ValidationError("From and To locations cannot be the same.")
        
        # Check if sufficient stock is available in from_location
        if self.stock and quantity:
            if from_location != self.stock.location:
                # Check if there's stock in the from_location
                try:
                    from_stock = Stock.objects.get(
                        product=self.stock.product,
                        product_variant=self.stock.product_variant,
                        warehouse=self.warehouse,
                        location=from_location
                    )
                    if from_stock.available_quantity < quantity:
                        raise ValidationError(
                            f"Insufficient stock in {from_location.name}. "
                            f"Available: {from_stock.available_quantity}"
                        )
                except Stock.DoesNotExist:
                    raise ValidationError(f"No stock found in {from_location.name}")
            else:
                if self.stock.available_quantity < quantity:
                    raise ValidationError(
                        f"Insufficient stock. Available: {self.stock.available_quantity}"
                    )
        
        return cleaned_data
