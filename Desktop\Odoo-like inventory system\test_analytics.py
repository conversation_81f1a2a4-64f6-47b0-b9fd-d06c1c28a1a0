#!/usr/bin/env python
"""
Test analytics and reports functionality
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.test import Client
from users.models import User

def test_analytics_functionality():
    """Test analytics and reports functionality"""
    print("📊 Testing Analytics & Reports Functionality...\n")
    
    try:
        # Create test client
        client = Client()
        
        # Get admin user
        user = User.objects.filter(is_active=True, role='admin').first()
        if not user:
            print("❌ No admin user found for testing")
            return False
        
        # Login the user
        client.force_login(user)
        print(f"✅ Logged in as: {user.username}")
        
        # Test dashboard access
        print("\n🏠 Testing Dashboard Access:")
        dashboard_response = client.get('/dashboard/')
        if dashboard_response.status_code == 200:
            print("✅ Dashboard loads successfully")
            
            # Check for analytics link
            content = dashboard_response.content.decode('utf-8')
            if 'Reports & Analytics' in content:
                print("✅ Analytics link found in dashboard")
            else:
                print("⚠️  Analytics link not found in dashboard")
        else:
            print(f"❌ Dashboard failed to load (Status: {dashboard_response.status_code})")
        
        # Test reports page access
        print("\n📈 Testing Reports Page Access:")
        reports_response = client.get('/dashboard/reports/')
        if reports_response.status_code == 200:
            print("✅ Reports page loads successfully")
            
            # Check for key analytics elements
            content = reports_response.content.decode('utf-8')
            
            analytics_checks = [
                ('Page Title', 'Reports & Analytics' in content),
                ('Date Range Filter', 'From Date' in content and 'To Date' in content),
                ('Export Options', 'Export Reports' in content),
                ('Revenue Metrics', 'revenue' in content.lower()),
                ('Inventory Metrics', 'inventory' in content.lower()),
                ('Charts Section', 'chart' in content.lower()),
                ('Top Products', 'top' in content.lower()),
            ]
            
            print("\n📋 Analytics Content Check:")
            all_passed = True
            for check_name, result in analytics_checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}: {'Present' if result else 'Missing'}")
                if not result:
                    all_passed = False
            
            if all_passed:
                print("\n🎉 All analytics features are present!")
            else:
                print("\n⚠️  Some analytics features are missing")
                
        else:
            print(f"❌ Reports page failed to load (Status: {reports_response.status_code})")
            return False
        
        # Test with date range parameters
        print("\n📅 Testing Date Range Filtering:")
        filtered_response = client.get('/dashboard/reports/?date_from=2025-01-01&date_to=2025-07-18')
        if filtered_response.status_code == 200:
            print("✅ Date range filtering works")
        else:
            print("❌ Date range filtering failed")
        
        # Test different report types
        print("\n📊 Testing Report Types:")
        report_types = ['overview', 'inventory', 'sales', 'financial']
        
        for report_type in report_types:
            response = client.get(f'/dashboard/reports/?report_type={report_type}')
            if response.status_code == 200:
                print(f"✅ {report_type.title()} report loads successfully")
            else:
                print(f"❌ {report_type.title()} report failed to load")
        
        print("\n🎉 Analytics functionality testing completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during analytics testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_analytics_urls():
    """Check if analytics URLs are properly configured"""
    print("\n🔗 Checking Analytics URL Configuration:")
    
    try:
        from django.urls import reverse
        
        # Test URL reversing
        dashboard_url = reverse('inventory:dashboard')
        reports_url = reverse('inventory:reports')
        
        print(f"✅ Dashboard URL: {dashboard_url}")
        print(f"✅ Reports URL: {reports_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL configuration error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Analytics & Reports Testing\n")
    
    urls_ok = check_analytics_urls()
    functionality_ok = test_analytics_functionality()
    
    print(f"\n📋 Final Results:")
    print(f"   URL Configuration: {'✅ PASS' if urls_ok else '❌ FAIL'}")
    print(f"   Analytics Functionality: {'✅ PASS' if functionality_ok else '❌ FAIL'}")
    
    if urls_ok and functionality_ok:
        print(f"\n🎉 Analytics system is fully functional!")
        print(f"📊 Access analytics at: http://127.0.0.1:8000/dashboard/reports/")
        print(f"🏠 Dashboard with analytics link: http://127.0.0.1:8000/dashboard/")
    else:
        print(f"\n⚠️  Analytics system needs attention")
