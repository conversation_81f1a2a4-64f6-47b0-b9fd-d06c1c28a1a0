#!/usr/bin/env python
"""
Test that the Stock import fix is working
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

def test_stock_import():
    """Test that Stock can be imported correctly"""
    print("🔍 Testing Stock Model Import Fix...\n")
    
    try:
        # Test the correct import
        from warehouses.models import Stock
        print("✅ Stock model imported successfully from warehouses.models")
        
        # Test that Stock model has expected fields
        stock_fields = [field.name for field in Stock._meta.fields]
        print(f"📋 Stock model fields: {stock_fields}")
        
        # Test basic Stock model functionality
        stock_count = Stock.objects.count()
        print(f"📊 Current stock records in database: {stock_count}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False

def test_products_view_import():
    """Test that the products view can import Stock correctly"""
    print(f"\n🔍 Testing Products View Import:")
    
    try:
        # Test importing the ProductListView
        from products.views import ProductListView
        print("✅ ProductListView imported successfully")
        
        # Test creating an instance
        view = ProductListView()
        print("✅ ProductListView instance created successfully")
        
        # Test that the view can access Stock model
        from warehouses.models import Stock
        print("✅ Stock model accessible in products view context")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error in products view: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error in products view: {e}")
        return False

def test_product_statistics():
    """Test that product statistics can be calculated"""
    print(f"\n📊 Testing Product Statistics Calculation:")
    
    try:
        from products.models import Product, Category, Brand
        from warehouses.models import Stock
        from django.db.models import Sum
        
        # Test basic counts
        total_products = Product.objects.count()
        active_products = Product.objects.filter(is_active=True).count()
        total_categories = Category.objects.filter(is_active=True).count()
        total_brands = Brand.objects.filter(is_active=True).count()
        
        print(f"   📦 Total Products: {total_products}")
        print(f"   ✅ Active Products: {active_products}")
        print(f"   🏷️  Categories: {total_categories}")
        print(f"   🏢 Brands: {total_brands}")
        
        # Test stock calculation
        total_stock_records = Stock.objects.count()
        print(f"   📊 Stock Records: {total_stock_records}")
        
        # Test low stock calculation
        low_stock_count = 0
        for product in Product.objects.filter(is_active=True)[:5]:  # Test first 5
            total_stock = Stock.objects.filter(product=product).aggregate(
                total=Sum('quantity')
            )['total'] or 0
            
            if total_stock <= product.min_stock_level:
                low_stock_count += 1
        
        print(f"   ⚠️  Low Stock Products (sample): {low_stock_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error calculating statistics: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_product_queryset():
    """Test that product queryset with stock annotation works"""
    print(f"\n🔍 Testing Product Queryset with Stock Annotation:")
    
    try:
        from products.models import Product
        from django.db.models import Sum
        
        # Test the annotated queryset
        products = Product.objects.select_related('category', 'brand').annotate(
            total_stock=Sum('stock__quantity')
        )[:3]  # Test first 3 products
        
        print(f"   📋 Testing annotated queryset:")
        for product in products:
            stock_value = product.total_stock or 0
            print(f"      - {product.name[:30]:<30} | Stock: {stock_value}")
        
        print(f"   ✅ Queryset annotation working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error with queryset annotation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Stock Import Fix Verification\n")
    
    import_success = test_stock_import()
    view_success = test_products_view_import()
    stats_success = test_product_statistics()
    queryset_success = test_product_queryset()
    
    print(f"\n📋 Test Results:")
    print(f"   Stock Import: {'✅ PASSED' if import_success else '❌ FAILED'}")
    print(f"   Products View: {'✅ PASSED' if view_success else '❌ FAILED'}")
    print(f"   Statistics: {'✅ PASSED' if stats_success else '❌ FAILED'}")
    print(f"   Queryset: {'✅ PASSED' if queryset_success else '❌ FAILED'}")
    
    if all([import_success, view_success, stats_success, queryset_success]):
        print(f"\n🎉 All tests passed!")
        print(f"✅ Stock import fix is working correctly")
        print(f"✅ Products page should load without ImportError")
        print(f"✅ Statistics will display actual numbers")
        print(f"✅ Stock quantities will show properly")
        print(f"\n🌐 Test the products page: http://127.0.0.1:8000/products/")
    else:
        print(f"\n⚠️  Some tests failed")
        print(f"🔧 Review the issues above")
