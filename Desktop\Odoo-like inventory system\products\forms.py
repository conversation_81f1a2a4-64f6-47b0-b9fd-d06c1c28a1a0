from django import forms
from django.core.exceptions import ValidationError
from .models import Product, Category, Brand, ProductVariant, ProductImage


class ProductForm(forms.ModelForm):
    """Form for creating and updating products"""
    
    class Meta:
        model = Product
        fields = [
            'name', 'sku', 'barcode', 'description', 'short_description',
            'category', 'brand', 'product_type', 'cost_price', 'selling_price',
            'weight', 'dimensions_length', 'dimensions_width', 'dimensions_height',
            'unit', 'track_inventory', 'min_stock_level', 'max_stock_level',
            'image', 'is_active', 'is_featured'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'sku': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Auto-generated if empty'}),
            'barcode': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'short_description': forms.TextInput(attrs={'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'brand': forms.Select(attrs={'class': 'form-control'}),
            'product_type': forms.Select(attrs={'class': 'form-control'}),
            'cost_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'selling_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'weight': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.001'}),
            'dimensions_length': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'dimensions_width': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'dimensions_height': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'unit': forms.Select(attrs={'class': 'form-control'}),
            'min_stock_level': forms.NumberInput(attrs={'class': 'form-control'}),
            'max_stock_level': forms.NumberInput(attrs={'class': 'form-control'}),
            'image': forms.FileInput(attrs={'class': 'form-control'}),
            'track_inventory': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_featured': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = Category.objects.filter(is_active=True)
        self.fields['brand'].queryset = Brand.objects.filter(is_active=True)
        self.fields['category'].empty_label = "Select Category"
        self.fields['brand'].empty_label = "Select Brand"
    
    def clean_sku(self):
        sku = self.cleaned_data.get('sku')
        if sku:
            # Check if SKU already exists (excluding current instance)
            query = Product.objects.filter(sku=sku)
            if self.instance.pk:
                query = query.exclude(pk=self.instance.pk)
            
            if query.exists():
                raise ValidationError("A product with this SKU already exists.")
        
        return sku
    
    def clean_barcode(self):
        barcode = self.cleaned_data.get('barcode')
        if barcode:
            # Check if barcode already exists (excluding current instance)
            query = Product.objects.filter(barcode=barcode)
            if self.instance.pk:
                query = query.exclude(pk=self.instance.pk)
            
            if query.exists():
                raise ValidationError("A product with this barcode already exists.")
        
        return barcode
    
    def clean(self):
        cleaned_data = super().clean()
        cost_price = cleaned_data.get('cost_price')
        selling_price = cleaned_data.get('selling_price')
        min_stock = cleaned_data.get('min_stock_level')
        max_stock = cleaned_data.get('max_stock_level')
        
        # Validate pricing
        if cost_price and selling_price and cost_price > selling_price:
            raise ValidationError("Cost price cannot be higher than selling price.")
        
        # Validate stock levels
        if min_stock and max_stock and min_stock > max_stock:
            raise ValidationError("Minimum stock level cannot be higher than maximum stock level.")
        
        return cleaned_data
    
    def save(self, commit=True):
        product = super().save(commit=False)
        
        # Auto-generate SKU if not provided
        if not product.sku:
            import uuid
            product.sku = f"PRD-{uuid.uuid4().hex[:8].upper()}"
        
        if commit:
            product.save()
        return product


class CategoryForm(forms.ModelForm):
    """Form for creating and updating categories"""
    
    class Meta:
        model = Category
        fields = ['name', 'description', 'parent', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'parent': forms.Select(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['parent'].queryset = Category.objects.filter(is_active=True)
        self.fields['parent'].empty_label = "No Parent Category"
        
        # Exclude self from parent choices when editing
        if self.instance.pk:
            self.fields['parent'].queryset = self.fields['parent'].queryset.exclude(pk=self.instance.pk)
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        query = Category.objects.filter(name=name)
        if self.instance.pk:
            query = query.exclude(pk=self.instance.pk)
        
        if query.exists():
            raise ValidationError("A category with this name already exists.")
        
        return name


class BrandForm(forms.ModelForm):
    """Form for creating and updating brands"""
    
    class Meta:
        model = Brand
        fields = ['name', 'description', 'logo', 'website', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'logo': forms.FileInput(attrs={'class': 'form-control'}),
            'website': forms.URLInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        query = Brand.objects.filter(name=name)
        if self.instance.pk:
            query = query.exclude(pk=self.instance.pk)
        
        if query.exists():
            raise ValidationError("A brand with this name already exists.")
        
        return name


class ProductVariantForm(forms.ModelForm):
    """Form for creating and updating product variants"""
    
    class Meta:
        model = ProductVariant
        fields = [
            'name', 'sku', 'barcode', 'cost_price', 'selling_price',
            'color', 'size', 'material', 'image', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'sku': forms.TextInput(attrs={'class': 'form-control'}),
            'barcode': forms.TextInput(attrs={'class': 'form-control'}),
            'cost_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'selling_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'color': forms.TextInput(attrs={'class': 'form-control'}),
            'size': forms.TextInput(attrs={'class': 'form-control'}),
            'material': forms.TextInput(attrs={'class': 'form-control'}),
            'image': forms.FileInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def clean_sku(self):
        sku = self.cleaned_data.get('sku')
        if sku:
            # Check if SKU already exists in products or variants
            product_exists = Product.objects.filter(sku=sku).exists()
            variant_query = ProductVariant.objects.filter(sku=sku)
            
            if self.instance.pk:
                variant_query = variant_query.exclude(pk=self.instance.pk)
            
            variant_exists = variant_query.exists()
            
            if product_exists or variant_exists:
                raise ValidationError("A product or variant with this SKU already exists.")
        
        return sku


class ProductImageForm(forms.ModelForm):
    """Form for uploading additional product images"""
    
    class Meta:
        model = ProductImage
        fields = ['image', 'alt_text', 'is_primary', 'order']
        widgets = {
            'image': forms.FileInput(attrs={'class': 'form-control'}),
            'alt_text': forms.TextInput(attrs={'class': 'form-control'}),
            'is_primary': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'order': forms.NumberInput(attrs={'class': 'form-control'}),
        }


class ProductSearchForm(forms.Form):
    """Form for searching and filtering products"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name, SKU, or barcode...'
        })
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        empty_label="All Categories",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    brand = forms.ModelChoiceField(
        queryset=Brand.objects.filter(is_active=True),
        required=False,
        empty_label="All Brands",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    status = forms.ChoiceField(
        choices=[
            ('', 'All Products'),
            ('active', 'Active'),
            ('inactive', 'Inactive'),
            ('low_stock', 'Low Stock'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    sort = forms.ChoiceField(
        choices=[
            ('name', 'Name'),
            ('sku', 'SKU'),
            ('created_at', 'Date Created'),
            ('selling_price', 'Price'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    order = forms.ChoiceField(
        choices=[
            ('asc', 'Ascending'),
            ('desc', 'Descending'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class BulkProductUpdateForm(forms.Form):
    """Form for bulk updating products"""
    ACTIONS = [
        ('', 'Select Action'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
        ('delete', 'Delete'),
        ('update_category', 'Update Category'),
        ('update_brand', 'Update Brand'),
    ]
    
    action = forms.ChoiceField(
        choices=ACTIONS,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    brand = forms.ModelChoiceField(
        queryset=Brand.objects.filter(is_active=True),
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class ProductImportForm(forms.Form):
    """Form for importing products from CSV"""
    csv_file = forms.FileField(
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': '.csv'})
    )
    update_existing = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text="Update existing products if SKU matches"
    )
