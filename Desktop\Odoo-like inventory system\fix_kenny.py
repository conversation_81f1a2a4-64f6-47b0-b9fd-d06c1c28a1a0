import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from users.models import User

try:
    kenny = User.objects.get(username='kenny')
    print(f"Before: <PERSON> role = {kenny.role}")
    kenny.role = 'admin'
    kenny.save()
    print(f"After: <PERSON> role = {kenny.role}")
    print(f"Can manage inventory: {kenny.can_manage_inventory}")
    print(f"Can manage sales: {kenny.can_manage_sales}")
    print("✅ <PERSON> upgraded successfully!")
except Exception as e:
    print(f"Error: {e}")
