{% extends 'base.html' %}

{% block title %}Users - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">User Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if user.is_admin %}
        <a href="{% url 'users:create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add User
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-md-12">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <input type="text" name="search" class="form-control" placeholder="Search users..." value="{{ current_search }}">
            </div>
            <div class="col-md-3">
                <select name="role" class="form-control">
                    <option value="">All Roles</option>
                    {% for value, label in roles %}
                        <option value="{{ value }}" {% if current_role == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-secondary">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
            <div class="col-md-2">
                <a href="{% url 'users:list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Users ({{ users|length }})</h5>
    </div>
    <div class="card-body">
        {% if users %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Department</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user_obj in users %}
                            <tr>
                                <td>
                                    <strong>{{ user_obj.username }}</strong>
                                    {% if user_obj.profile_picture %}
                                        <img src="{{ user_obj.profile_picture.url }}" alt="Profile" class="rounded-circle ms-2" width="24" height="24">
                                    {% endif %}
                                </td>
                                <td>{{ user_obj.get_full_name|default:"-" }}</td>
                                <td>{{ user_obj.email }}</td>
                                <td>
                                    <span class="badge bg-{% if user_obj.role == 'admin' %}danger{% elif user_obj.role == 'manager' %}warning{% elif user_obj.role == 'warehouse_staff' %}info{% elif user_obj.role == 'sales_staff' %}success{% else %}secondary{% endif %}">
                                        {{ user_obj.get_role_display }}
                                    </span>
                                </td>
                                <td>{{ user_obj.profile.department|default:"-" }}</td>
                                <td>
                                    {% if user_obj.is_active_employee %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user_obj.last_login %}
                                        {{ user_obj.last_login|date:"M d, Y H:i" }}
                                    {% else %}
                                        <span class="text-muted">Never</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'users:detail' user_obj.pk %}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if user.is_admin %}
                                            <a href="{% url 'users:edit' user_obj.pk %}" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'users:toggle_status' user_obj.pk %}" 
                                               class="btn btn-outline-{% if user_obj.is_active_employee %}danger{% else %}success{% endif %}" 
                                               title="{% if user_obj.is_active_employee %}Deactivate{% else %}Activate{% endif %}"
                                               onclick="return confirm('Are you sure you want to {% if user_obj.is_active_employee %}deactivate{% else %}activate{% endif %} this user?')">
                                                <i class="fas fa-{% if user_obj.is_active_employee %}ban{% else %}check{% endif %}"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="Users pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.role %}&role={{ request.GET.role }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.role %}&role={{ request.GET.role }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.role %}&role={{ request.GET.role }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.role %}&role={{ request.GET.role }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No users found</h5>
                <p class="text-muted">No users match your search criteria.</p>
                {% if user.is_admin %}
                    <a href="{% url 'users:create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First User
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- User Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">{{ users|length }}</h5>
                <p class="card-text text-muted">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">{{ users|length }}</h5>
                <p class="card-text text-muted">Active Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">-</h5>
                <p class="card-text text-muted">Admins</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">-</h5>
                <p class="card-text text-muted">Online Now</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}
