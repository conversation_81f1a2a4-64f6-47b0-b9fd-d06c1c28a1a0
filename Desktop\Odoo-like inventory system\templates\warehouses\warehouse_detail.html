{% extends 'base.html' %}

{% block title %}{{ warehouse.name }} - Warehouse Details{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ warehouse.name }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'warehouses:list' %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Warehouses
        </a>
        {% if user.is_manager %}
        <a href="{% url 'warehouses:edit' warehouse.pk %}" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit Warehouse
        </a>
        {% endif %}
    </div>
</div>

<!-- Warehouse Info Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Products</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_products }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Stock Value</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ total_stock_value|floatformat:2 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Locations</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ locations|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Low Stock</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_items }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Warehouse Information -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Warehouse Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Code:</th>
                                <td><code>{{ warehouse.code }}</code></td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td>{{ warehouse.get_warehouse_type_display }}</td>
                            </tr>
                            <tr>
                                <th>Manager:</th>
                                <td>{{ warehouse.manager.get_full_name|default:warehouse.manager.username|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Phone:</th>
                                <td>{{ warehouse.phone|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td>{{ warehouse.email|default:"-" }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Address:</th>
                                <td>
                                    {{ warehouse.address_line1 }}<br>
                                    {% if warehouse.address_line2 %}{{ warehouse.address_line2 }}<br>{% endif %}
                                    {{ warehouse.city }}, {{ warehouse.state }} {{ warehouse.postal_code }}<br>
                                    {{ warehouse.country }}
                                </td>
                            </tr>
                            <tr>
                                <th>Capacity:</th>
                                <td>{{ warehouse.capacity|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    {% if warehouse.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                    {% if warehouse.is_default %}
                                        <span class="badge bg-primary ms-1">Default</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Stock Movements -->
        {% if recent_movements %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Stock Movements</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Product</th>
                                <th>Type</th>
                                <th>Quantity</th>
                                <th>Reference</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in recent_movements %}
                                <tr>
                                    <td>{{ movement.created_at|date:"M d, H:i" }}</td>
                                    <td>{{ movement.product.name|truncatechars:30 }}</td>
                                    <td>
                                        <span class="badge bg-{% if movement.movement_type == 'in' %}success{% elif movement.movement_type == 'out' %}danger{% else %}info{% endif %}">
                                            {{ movement.get_movement_type_display }}
                                        </span>
                                    </td>
                                    <td>{{ movement.quantity }}</td>
                                    <td><code>{{ movement.reference }}</code></td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Quick Actions and Locations -->
    <div class="col-md-4">
        {% if user.can_manage_inventory %}
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'warehouses:location_create' warehouse.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-plus"></i> Add Location
                    </a>
                    <a href="{% url 'warehouses:stock_list' warehouse.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-boxes"></i> View Stock
                    </a>
                    <a href="{% url 'inventory:quick_stock_in' %}?warehouse={{ warehouse.pk }}" class="btn btn-outline-success">
                        <i class="fas fa-plus-circle"></i> Add Stock
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Locations -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Locations</h5>
            </div>
            <div class="card-body">
                {% if locations %}
                    {% for location in locations %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>{{ location.name }}</strong>
                                <br><small class="text-muted">{{ location.code }}</small>
                            </div>
                            <span class="badge bg-secondary">{{ location.get_location_type_display }}</span>
                        </div>
                    {% endfor %}
                    <a href="{% url 'warehouses:location_list' warehouse.pk %}" class="btn btn-sm btn-outline-primary">View All</a>
                {% else %}
                    <p class="text-muted">No locations configured</p>
                    {% if user.can_manage_inventory %}
                        <a href="{% url 'warehouses:location_create' warehouse.pk %}" class="btn btn-sm btn-primary">Add Location</a>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}
