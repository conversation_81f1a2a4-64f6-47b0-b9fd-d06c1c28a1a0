#!/usr/bin/env python
"""
Validate dashboard template syntax
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.template import Template, Context
from django.template.loader import get_template

def validate_template():
    """Validate the dashboard template syntax"""
    print("🔍 Validating Dashboard Template Syntax...\n")
    
    try:
        # Read the template file
        template_path = r"Desktop\Odoo-like inventory system\templates\inventory\dashboard.html"
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        print("📋 Template Analysis:")
        print(f"   File size: {len(template_content)} characters")
        print(f"   Lines: {len(template_content.splitlines())}")
        
        # Count template tags
        if_count = template_content.count('{% if')
        elif_count = template_content.count('{% elif')
        else_count = template_content.count('{% else')
        endif_count = template_content.count('{% endif')
        
        block_start_count = template_content.count('{% block')
        endblock_count = template_content.count('{% endblock')
        
        print(f"\n🏷️  Template Tags Count:")
        print(f"   {% if %}:      {if_count}")
        print(f"   {% elif %}:    {elif_count}")
        print(f"   {% else %}:    {else_count}")
        print(f"   {% endif %}:   {endif_count}")
        print(f"   {% block %}:   {block_start_count}")
        print(f"   {% endblock %}: {endblock_count}")
        
        # Validate balance
        print(f"\n⚖️  Balance Check:")
        if_balance = if_count == endif_count
        block_balance = block_start_count == endblock_count
        
        print(f"   If statements: {'✅ Balanced' if if_balance else '❌ Unbalanced'} ({if_count} if, {endif_count} endif)")
        print(f"   Block statements: {'✅ Balanced' if block_balance else '❌ Unbalanced'} ({block_start_count} block, {endblock_count} endblock)")
        
        # Try to create Template object
        print(f"\n🧪 Template Compilation Test:")
        template = Template(template_content)
        print("✅ Template compiles successfully")
        
        # Try to render with mock context
        print(f"\n🎨 Template Rendering Test:")
        context = Context({
            'total_products': 10,
            'total_warehouses': 2,
            'total_stock_value': 5000.00,
            'total_stock_items': 50,
            'low_stock_items': [],
            'low_stock_count': 3,
            'recent_movements': [],
            'movements_in': 25,
            'movements_out': 15,
            'top_products': [],
            'pending_adjustments': 2,
            'days': 30,
            'dashboard_loaded': True,
        })
        
        rendered = template.render(context)
        print("✅ Template renders successfully")
        print(f"   Rendered HTML length: {len(rendered)} characters")
        
        # Check for key content
        key_checks = [
            ('Title', 'Dashboard - Inventory Management System' in rendered),
            ('Success message', 'Dashboard Loaded Successfully' in rendered),
            ('CSS styles', 'border-left-primary' in rendered),
            ('Content structure', 'container-fluid' in rendered),
        ]
        
        print(f"\n📊 Content Validation:")
        all_good = True
        for check_name, result in key_checks:
            status = "✅" if result else "❌"
            print(f"   {status} {check_name}: {'Present' if result else 'Missing'}")
            if not result:
                all_good = False
        
        if all_good and if_balance and block_balance:
            print(f"\n🎉 TEMPLATE VALIDATION SUCCESSFUL!")
            print(f"✅ All syntax checks passed")
            print(f"✅ Template compiles without errors")
            print(f"✅ Template renders correctly")
            print(f"✅ All required content present")
            return True
        else:
            print(f"\n⚠️  Template has issues that need to be addressed")
            return False
            
    except Exception as e:
        print(f"❌ Template validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = validate_template()
    
    if success:
        print(f"\n🚀 The dashboard template is ready to use!")
        print(f"🌐 Access it at: http://127.0.0.1:8000/dashboard/")
    else:
        print(f"\n🔧 Template needs further fixes before it can be used")
