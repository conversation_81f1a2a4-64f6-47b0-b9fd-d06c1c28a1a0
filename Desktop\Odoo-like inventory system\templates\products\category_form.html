{% extends 'base.html' %}

{% block title %}{% if object %}Edit Category{% else %}Add Category{% endif %} - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{% if object %}Edit Category{% else %}Add Category{% endif %}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'products:category_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Categories
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Category Information</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Category Name *</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger small">{{ form.name.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.parent.id_for_label }}" class="form-label">Parent Category</label>
                        {{ form.parent }}
                        {% if form.parent.errors %}
                            <div class="text-danger small">{{ form.parent.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">Select a parent category to create a subcategory</small>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small">{{ form.description.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3 form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            Active
                        </label>
                        {% if form.is_active.errors %}
                            <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'products:category_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {% if object %}Update Category{% else %}Create Category{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>Category Guidelines:</h6>
                    <ul class="mb-0">
                        <li>Use clear, descriptive names</li>
                        <li>Create hierarchical structure with parent categories</li>
                        <li>Keep category names consistent</li>
                        <li>Use categories to organize and filter products</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
