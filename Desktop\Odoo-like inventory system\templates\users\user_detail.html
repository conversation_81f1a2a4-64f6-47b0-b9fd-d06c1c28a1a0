{% extends 'base.html' %}

{% block title %}{{ user_obj.get_full_name|default:user_obj.username }} - User Details{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">User Details</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'users:list' %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
        {% if user.is_admin %}
        <a href="{% url 'users:edit' user_obj.pk %}" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit User
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <!-- User Information -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">User Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Username:</th>
                                <td>{{ user_obj.username }}</td>
                            </tr>
                            <tr>
                                <th>Full Name:</th>
                                <td>{{ user_obj.get_full_name|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td>{{ user_obj.email }}</td>
                            </tr>
                            <tr>
                                <th>Phone:</th>
                                <td>{{ user_obj.phone|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Role:</th>
                                <td>
                                    <span class="badge bg-{% if user_obj.role == 'admin' %}danger{% elif user_obj.role == 'manager' %}warning{% elif user_obj.role == 'warehouse_staff' %}info{% elif user_obj.role == 'sales_staff' %}success{% else %}secondary{% endif %}">
                                        {{ user_obj.get_role_display }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Status:</th>
                                <td>
                                    {% if user_obj.is_active_employee %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Date Joined:</th>
                                <td>{{ user_obj.date_joined|date:"M d, Y H:i" }}</td>
                            </tr>
                            <tr>
                                <th>Last Login:</th>
                                <td>
                                    {% if user_obj.last_login %}
                                        {{ user_obj.last_login|date:"M d, Y H:i" }}
                                    {% else %}
                                        <span class="text-muted">Never</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Last Login IP:</th>
                                <td>{{ user_obj.last_login_ip|default:"-" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if user_obj.address %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Address:</h6>
                        <p class="text-muted">{{ user_obj.address }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Profile Information -->
        {% if user_obj.profile %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Profile Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Employee ID:</th>
                                <td>{{ user_obj.profile.employee_id|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Department:</th>
                                <td>{{ user_obj.profile.department|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Hire Date:</th>
                                <td>
                                    {% if user_obj.profile.hire_date %}
                                        {{ user_obj.profile.hire_date|date:"M d, Y" }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Emergency Contact:</th>
                                <td>{{ user_obj.profile.emergency_contact|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Emergency Phone:</th>
                                <td>{{ user_obj.profile.emergency_phone|default:"-" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if user_obj.profile.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Notes:</h6>
                        <p class="text-muted">{{ user_obj.profile.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Profile Picture and Actions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Profile Picture</h5>
            </div>
            <div class="card-body text-center">
                {% if user_obj.profile_picture %}
                    <img src="{{ user_obj.profile_picture.url }}" alt="Profile Picture" class="img-fluid rounded-circle mb-3" style="max-width: 150px;">
                {% else %}
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
                        <i class="fas fa-user fa-4x text-muted"></i>
                    </div>
                {% endif %}
                <h5>{{ user_obj.get_full_name|default:user_obj.username }}</h5>
                <p class="text-muted">{{ user_obj.get_role_display }}</p>
            </div>
        </div>

        <!-- Quick Actions -->
        {% if user.is_admin %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'users:edit' user_obj.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Edit User
                    </a>
                    <a href="{% url 'users:toggle_status' user_obj.pk %}" 
                       class="btn btn-outline-{% if user_obj.is_active_employee %}danger{% else %}success{% endif %}"
                       onclick="return confirm('Are you sure you want to {% if user_obj.is_active_employee %}deactivate{% else %}activate{% endif %} this user?')">
                        <i class="fas fa-{% if user_obj.is_active_employee %}ban{% else %}check{% endif %}"></i> 
                        {% if user_obj.is_active_employee %}Deactivate{% else %}Activate{% endif %}
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Permissions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Permissions</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Admin Access
                        {% if user_obj.is_admin %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-secondary">No</span>
                        {% endif %}
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Manager Access
                        {% if user_obj.is_manager %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-secondary">No</span>
                        {% endif %}
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Inventory Management
                        {% if user_obj.can_manage_inventory %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-secondary">No</span>
                        {% endif %}
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Sales Management
                        {% if user_obj.can_manage_sales %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-secondary">No</span>
                        {% endif %}
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Accounting Access
                        {% if user_obj.can_view_accounting %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-secondary">No</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
