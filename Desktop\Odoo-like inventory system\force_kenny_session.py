#!/usr/bin/env python
"""
Force clean session state and ensure kenny is the only active user
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.contrib.sessions.models import Session
from users.models import User
from django.utils import timezone

def force_kenny_session():
    """Force clean session state with only kenny active"""
    print("🔧 Forcing Clean Kenny Session...\n")
    
    try:
        # Get kenny user
        kenny = User.objects.get(username='kenny')
        print(f"✅ Kenny user found:")
        print(f"   Username: {kenny.username}")
        print(f"   ID: {kenny.id}")
        print(f"   Role: {kenny.get_role_display()}")
        
        # Step 1: Delete ALL existing sessions
        print(f"\n🗑️  Deleting ALL existing sessions:")
        all_sessions = Session.objects.all()
        session_count = all_sessions.count()
        
        if session_count > 0:
            all_sessions.delete()
            print(f"   ✅ Deleted {session_count} sessions")
        else:
            print(f"   ✅ No sessions to delete")
        
        # Step 2: Verify no sessions remain
        remaining_sessions = Session.objects.all().count()
        print(f"   Remaining sessions: {remaining_sessions}")
        
        # Step 3: Ensure kenny is properly configured
        print(f"\n👤 Ensuring Kenny Configuration:")
        
        changes_made = []
        
        if not kenny.is_active:
            kenny.is_active = True
            changes_made.append("Activated kenny")
        
        if not kenny.is_staff:
            kenny.is_staff = True
            changes_made.append("Granted staff status")
        
        if kenny.role != 'admin':
            kenny.role = 'admin'
            changes_made.append("Set role to admin")
        
        if changes_made:
            kenny.save()
            print(f"   ✅ Updated kenny:")
            for change in changes_made:
                print(f"      - {change}")
        else:
            print(f"   ✅ Kenny is properly configured")
        
        # Step 4: Check for any other admin users that might cause confusion
        print(f"\n🔍 Checking Other Users:")
        
        other_users = User.objects.exclude(username='kenny')
        for user in other_users:
            print(f"   - {user.username} (ID: {user.id}, Active: {user.is_active})")
            
            # Optionally deactivate other admin users to avoid confusion
            if user.username == 'admin' and user.is_active:
                print(f"     ⚠️  Consider deactivating 'admin' user to avoid confusion")
                # Uncomment the next lines if you want to deactivate the admin user
                # user.is_active = False
                # user.save()
                # print(f"     ✅ Deactivated admin user")
        
        print(f"\n🎯 Final State:")
        print(f"   Active sessions: {Session.objects.all().count()}")
        print(f"   Kenny status: Active={kenny.is_active}, Staff={kenny.is_staff}, Role={kenny.role}")
        
        return True
        
    except User.DoesNotExist:
        print("❌ Kenny user not found!")
        return False
    except Exception as e:
        print(f"❌ Error forcing kenny session: {e}")
        import traceback
        traceback.print_exc()
        return False

def restart_server_instructions():
    """Provide instructions for restarting the server"""
    print(f"\n🔄 Server Restart Instructions:")
    print(f"")
    print(f"1. Stop the current Django server:")
    print(f"   - Press Ctrl+C in the terminal running the server")
    print(f"   - Or close the terminal window")
    print(f"")
    print(f"2. Start the server fresh:")
    print(f"   - Open a new terminal/command prompt")
    print(f"   - Navigate to your project directory")
    print(f"   - Run: .\\odoo_venv\\Scripts\\python.exe manage.py runserver 127.0.0.1:8000")
    print(f"")
    print(f"3. Fresh browser session:")
    print(f"   - Close ALL browser windows/tabs")
    print(f"   - Clear browser cache and cookies")
    print(f"   - Open browser in incognito/private mode")
    print(f"   - Go to: http://127.0.0.1:8000/login/")
    print(f"   - Login with: kenny / InventoryAdmin2025!")

def test_consistency_urls():
    """Provide URLs to test consistency"""
    print(f"\n🧪 Test These URLs After Fresh Login:")
    print(f"")
    print(f"Test in this exact order to verify kenny appears consistently:")
    print(f"")
    print(f"1. 📊 Dashboard:")
    print(f"   http://127.0.0.1:8000/dashboard/")
    print(f"   Should show: kenny in top-right corner")
    print(f"")
    print(f"2. 📦 Products:")
    print(f"   http://127.0.0.1:8000/products/")
    print(f"   Should show: kenny in navigation bar")
    print(f"")
    print(f"3. 🏢 Warehouses:")
    print(f"   http://127.0.0.1:8000/warehouses/list/")
    print(f"   Should show: kenny in navigation bar")
    print(f"")
    print(f"4. 🛒 Sales Orders:")
    print(f"   http://127.0.0.1:8000/sales/orders/")
    print(f"   Should show: kenny in navigation bar")
    print(f"")
    print(f"5. 👥 Customers:")
    print(f"   http://127.0.0.1:8000/sales/customers/")
    print(f"   Should show: kenny in navigation bar")
    print(f"")
    print(f"6. 📈 Reports:")
    print(f"   http://127.0.0.1:8000/dashboard/reports/")
    print(f"   Should show: kenny in navigation bar")
    print(f"")
    print(f"✅ If ALL pages show 'kenny' → Problem solved!")
    print(f"❌ If any page shows 'admin' → Try different browser or contact support")

if __name__ == "__main__":
    print("🚀 Force Kenny Session Fix\n")
    
    success = force_kenny_session()
    restart_server_instructions()
    test_consistency_urls()
    
    print(f"\n📋 Final Results:")
    if success:
        print(f"🎉 Session cleanup completed!")
        print(f"✅ All sessions deleted")
        print(f"✅ Kenny properly configured")
        print(f"✅ Ready for fresh login")
        print(f"")
        print(f"🎯 Next Steps:")
        print(f"   1. Restart Django server (see instructions above)")
        print(f"   2. Use fresh browser session (incognito mode)")
        print(f"   3. Login with kenny credentials")
        print(f"   4. Test all URLs listed above")
        print(f"   5. Verify 'kenny' appears on all pages")
    else:
        print(f"⚠️  Session cleanup failed")
        print(f"🔧 Manual intervention may be needed")
