from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import api_views

router = DefaultRouter()
router.register(r'movements', api_views.StockMovementViewSet)
router.register(r'adjustments', api_views.StockAdjustmentViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('dashboard-stats/', api_views.DashboardStatsAPIView.as_view(), name='dashboard_stats'),
    path('movement-stats/', api_views.MovementStatsAPIView.as_view(), name='movement_stats'),
    path('low-stock-alerts/', api_views.LowStockAlertsAPIView.as_view(), name='low_stock_alerts'),
    path('quick-stock-in/', api_views.QuickStockInAPIView.as_view(), name='quick_stock_in'),
    path('quick-stock-out/', api_views.QuickStockOutAPIView.as_view(), name='quick_stock_out'),
    path('stock-transfer/', api_views.StockTransferAPIView.as_view(), name='stock_transfer'),
]
