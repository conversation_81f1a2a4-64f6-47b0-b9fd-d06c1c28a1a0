#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from users.models import User

# Update admin user role
try:
    admin_user = User.objects.get(username='admin')
    admin_user.role = 'admin'
    admin_user.save()
    print(f"Updated admin user role to: {admin_user.role}")
except User.DoesNotExist:
    print("Admin user not found")
except Exception as e:
    print(f"Error updating admin user: {e}")

# List all users
print("All users:")
for user in User.objects.all():
    print(f"- {user.username} ({user.role}) - Active: {user.is_active_employee}")
