{% extends 'base.html' %}

{% block title %}{% if object %}Edit User{% else %}Add User{% endif %} - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{% if object %}Edit User{% else %}Add User{% endif %}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'users:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">User Information</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">Username *</label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger small">{{ form.username.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email *</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name *</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small">{{ form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name *</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small">{{ form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.role.id_for_label }}" class="form-label">Role *</label>
                                {{ form.role }}
                                {% if form.role.errors %}
                                    <div class="text-danger small">{{ form.role.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">Phone</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if not object %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">Password *</label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="text-danger small">{{ form.password1.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password *</label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="text-danger small">{{ form.password2.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger small">{{ form.address.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.profile_picture.id_for_label }}" class="form-label">Profile Picture</label>
                        {{ form.profile_picture }}
                        {% if form.profile_picture.errors %}
                            <div class="text-danger small">{{ form.profile_picture.errors.0 }}</div>
                        {% endif %}
                        {% if object and object.profile_picture %}
                            <div class="mt-2">
                                <img src="{{ object.profile_picture.url }}" alt="Current profile picture" class="img-thumbnail" style="max-width: 100px;">
                                <small class="text-muted d-block">Current profile picture</small>
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3 form-check">
                        {{ form.is_active_employee }}
                        <label class="form-check-label" for="{{ form.is_active_employee.id_for_label }}">
                            Active Employee
                        </label>
                        {% if form.is_active_employee.errors %}
                            <div class="text-danger small">{{ form.is_active_employee.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'users:list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {% if object %}Update User{% else %}Create User{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Role Permissions</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>Role Descriptions:</h6>
                    <ul class="mb-0">
                        <li><strong>Administrator:</strong> Full system access</li>
                        <li><strong>Manager:</strong> Management access to all modules</li>
                        <li><strong>Warehouse Staff:</strong> Inventory management access</li>
                        <li><strong>Sales Staff:</strong> Sales and customer management</li>
                        <li><strong>Accountant:</strong> Financial and accounting access</li>
                        <li><strong>Viewer:</strong> Read-only access</li>
                    </ul>
                </div>
            </div>
        </div>

        {% if object %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">User Statistics</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td>Date Joined:</td>
                        <td>{{ object.date_joined|date:"M d, Y" }}</td>
                    </tr>
                    <tr>
                        <td>Last Login:</td>
                        <td>
                            {% if object.last_login %}
                                {{ object.last_login|date:"M d, Y" }}
                            {% else %}
                                Never
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>Status:</td>
                        <td>
                            {% if object.is_active_employee %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}
