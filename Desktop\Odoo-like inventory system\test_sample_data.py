#!/usr/bin/env python
"""
Simple test script to create basic sample data
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

try:
    from products.models import Category, Brand, Product
    from warehouses.models import Warehouse
    from sales.models import Customer
    print("✅ All models imported successfully!")
    
    # Test creating a simple category
    category, created = Category.objects.get_or_create(
        name="Test Electronics",
        defaults={'description': "Test category", 'is_active': True}
    )
    print(f"✅ Category created: {category.name} (Created: {created})")
    
    # Test creating a simple brand
    brand, created = Brand.objects.get_or_create(
        name="Test Brand",
        defaults={'description': "Test brand", 'is_active': True}
    )
    print(f"✅ Brand created: {brand.name} (Created: {created})")
    
    # Test creating a simple product
    product, created = Product.objects.get_or_create(
        sku="TEST-001",
        defaults={
            'name': "Test Product",
            'description': "Test product description",
            'category': category,
            'brand': brand,
            'selling_price': Decimal('99.99'),
            'cost_price': Decimal('50.00')
        }
    )
    print(f"✅ Product created: {product.name} (Created: {created})")
    
    print("\n🎉 Basic sample data creation successful!")
    print("Your system is ready for full data population!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
