from django import forms
from django.core.exceptions import ValidationError
from .models import StockMovement, StockAdjustment, StockAdjustmentLine
from warehouses.models import Warehouse, Location, Stock
from products.models import Product, ProductVariant


class StockMovementForm(forms.ModelForm):
    """Form for creating stock movements"""
    
    class Meta:
        model = StockMovement
        fields = [
            'product', 'product_variant', 'movement_type', 'reason', 'quantity',
            'from_warehouse', 'from_location', 'to_warehouse', 'to_location',
            'unit_cost', 'notes'
        ]
        widgets = {
            'product': forms.Select(attrs={'class': 'form-control'}),
            'product_variant': forms.Select(attrs={'class': 'form-control'}),
            'movement_type': forms.Select(attrs={'class': 'form-control'}),
            'reason': forms.Select(attrs={'class': 'form-control'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'from_warehouse': forms.Select(attrs={'class': 'form-control'}),
            'from_location': forms.Select(attrs={'class': 'form-control'}),
            'to_warehouse': forms.Select(attrs={'class': 'form-control'}),
            'to_location': forms.Select(attrs={'class': 'form-control'}),
            'unit_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['product'].queryset = Product.objects.filter(is_active=True)
        self.fields['product_variant'].queryset = ProductVariant.objects.filter(is_active=True)
        self.fields['from_warehouse'].queryset = Warehouse.objects.filter(is_active=True)
        self.fields['to_warehouse'].queryset = Warehouse.objects.filter(is_active=True)
        self.fields['from_location'].queryset = Location.objects.filter(is_active=True)
        self.fields['to_location'].queryset = Location.objects.filter(is_active=True)
        
        # Set empty labels
        self.fields['product_variant'].empty_label = "No Variant"
        self.fields['from_warehouse'].empty_label = "Select Warehouse"
        self.fields['to_warehouse'].empty_label = "Select Warehouse"
        self.fields['from_location'].empty_label = "No Location"
        self.fields['to_location'].empty_label = "No Location"
    
    def clean(self):
        cleaned_data = super().clean()
        movement_type = cleaned_data.get('movement_type')
        from_warehouse = cleaned_data.get('from_warehouse')
        to_warehouse = cleaned_data.get('to_warehouse')
        quantity = cleaned_data.get('quantity')
        product = cleaned_data.get('product')
        product_variant = cleaned_data.get('product_variant')
        from_location = cleaned_data.get('from_location')
        
        # Validate required fields based on movement type
        if movement_type == 'in' and not to_warehouse:
            raise ValidationError("To warehouse is required for stock in movements.")
        
        if movement_type == 'out' and not from_warehouse:
            raise ValidationError("From warehouse is required for stock out movements.")
        
        if movement_type == 'transfer':
            if not from_warehouse or not to_warehouse:
                raise ValidationError("Both from and to warehouses are required for transfers.")
            if from_warehouse == to_warehouse and from_location == cleaned_data.get('to_location'):
                raise ValidationError("Cannot transfer to the same location.")
        
        # Check stock availability for out movements
        if movement_type in ['out', 'transfer'] and from_warehouse and product and quantity:
            try:
                stock = Stock.objects.get(
                    product=product,
                    product_variant=product_variant,
                    warehouse=from_warehouse,
                    location=from_location
                )
                if stock.available_quantity < quantity:
                    raise ValidationError(
                        f"Insufficient stock. Available: {stock.available_quantity}, Required: {quantity}"
                    )
            except Stock.DoesNotExist:
                raise ValidationError("No stock found in the specified location.")
        
        return cleaned_data


class QuickStockInForm(forms.Form):
    """Quick form for adding stock"""
    product = forms.ModelChoiceField(
        queryset=Product.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    product_variant = forms.ModelChoiceField(
        queryset=ProductVariant.objects.filter(is_active=True),
        required=False,
        empty_label="No Variant",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    location = forms.ModelChoiceField(
        queryset=Location.objects.filter(is_active=True, is_receivable=True),
        required=False,
        empty_label="No Location",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    unit_cost = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 2})
    )


class QuickStockOutForm(forms.Form):
    """Quick form for removing stock"""
    product = forms.ModelChoiceField(
        queryset=Product.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    product_variant = forms.ModelChoiceField(
        queryset=ProductVariant.objects.filter(is_active=True),
        required=False,
        empty_label="No Variant",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    location = forms.ModelChoiceField(
        queryset=Location.objects.filter(is_active=True, is_pickable=True),
        required=False,
        empty_label="No Location",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    reason = forms.ChoiceField(
        choices=[
            ('sale', 'Sale'),
            ('damaged', 'Damaged'),
            ('expired', 'Expired'),
            ('sample', 'Sample'),
            ('theft', 'Theft/Loss'),
            ('other', 'Other'),
        ],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 2})
    )
    
    def clean(self):
        cleaned_data = super().clean()
        product = cleaned_data.get('product')
        product_variant = cleaned_data.get('product_variant')
        warehouse = cleaned_data.get('warehouse')
        location = cleaned_data.get('location')
        quantity = cleaned_data.get('quantity')
        
        if product and warehouse and quantity:
            try:
                stock = Stock.objects.get(
                    product=product,
                    product_variant=product_variant,
                    warehouse=warehouse,
                    location=location
                )
                if stock.available_quantity < quantity:
                    raise ValidationError(
                        f"Insufficient stock. Available: {stock.available_quantity}, Required: {quantity}"
                    )
            except Stock.DoesNotExist:
                raise ValidationError("No stock found in the specified location.")
        
        return cleaned_data


class StockTransferForm(forms.Form):
    """Form for transferring stock between warehouses/locations"""
    product = forms.ModelChoiceField(
        queryset=Product.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    product_variant = forms.ModelChoiceField(
        queryset=ProductVariant.objects.filter(is_active=True),
        required=False,
        empty_label="No Variant",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    from_warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="From Warehouse"
    )
    from_location = forms.ModelChoiceField(
        queryset=Location.objects.filter(is_active=True, is_pickable=True),
        required=False,
        empty_label="No Location",
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="From Location"
    )
    to_warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="To Warehouse"
    )
    to_location = forms.ModelChoiceField(
        queryset=Location.objects.filter(is_active=True, is_receivable=True),
        required=False,
        empty_label="No Location",
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="To Location"
    )
    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 2})
    )
    
    def clean(self):
        cleaned_data = super().clean()
        product = cleaned_data.get('product')
        product_variant = cleaned_data.get('product_variant')
        from_warehouse = cleaned_data.get('from_warehouse')
        from_location = cleaned_data.get('from_location')
        to_warehouse = cleaned_data.get('to_warehouse')
        to_location = cleaned_data.get('to_location')
        quantity = cleaned_data.get('quantity')
        
        # Check if transferring to same location
        if (from_warehouse == to_warehouse and from_location == to_location):
            raise ValidationError("Cannot transfer to the same location.")
        
        # Check stock availability
        if product and from_warehouse and quantity:
            try:
                stock = Stock.objects.get(
                    product=product,
                    product_variant=product_variant,
                    warehouse=from_warehouse,
                    location=from_location
                )
                if stock.available_quantity < quantity:
                    raise ValidationError(
                        f"Insufficient stock. Available: {stock.available_quantity}, Required: {quantity}"
                    )
            except Stock.DoesNotExist:
                raise ValidationError("No stock found in the source location.")
        
        return cleaned_data


class StockAdjustmentForm(forms.ModelForm):
    """Form for creating stock adjustments"""
    
    class Meta:
        model = StockAdjustment
        fields = ['adjustment_type', 'warehouse', 'reason', 'notes']
        widgets = {
            'adjustment_type': forms.Select(attrs={'class': 'form-control'}),
            'warehouse': forms.Select(attrs={'class': 'form-control'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['warehouse'].queryset = Warehouse.objects.filter(is_active=True)


class StockAdjustmentLineForm(forms.ModelForm):
    """Form for stock adjustment line items"""
    
    class Meta:
        model = StockAdjustmentLine
        fields = [
            'product', 'product_variant', 'location', 'expected_quantity',
            'actual_quantity', 'unit_cost', 'notes'
        ]
        widgets = {
            'product': forms.Select(attrs={'class': 'form-control'}),
            'product_variant': forms.Select(attrs={'class': 'form-control'}),
            'location': forms.Select(attrs={'class': 'form-control'}),
            'expected_quantity': forms.NumberInput(attrs={'class': 'form-control'}),
            'actual_quantity': forms.NumberInput(attrs={'class': 'form-control'}),
            'unit_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
        }
    
    def __init__(self, *args, **kwargs):
        warehouse = kwargs.pop('warehouse', None)
        super().__init__(*args, **kwargs)
        
        self.fields['product'].queryset = Product.objects.filter(is_active=True)
        self.fields['product_variant'].queryset = ProductVariant.objects.filter(is_active=True)
        
        if warehouse:
            self.fields['location'].queryset = Location.objects.filter(
                warehouse=warehouse,
                is_active=True
            )
        
        self.fields['product_variant'].empty_label = "No Variant"
        self.fields['location'].empty_label = "No Location"


class MovementSearchForm(forms.Form):
    """Form for searching and filtering stock movements"""
    search = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by reference or product...'
        })
    )
    movement_type = forms.ChoiceField(
        choices=[('', 'All Types')] + StockMovement.MOVEMENT_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    warehouse = forms.ModelChoiceField(
        queryset=Warehouse.objects.filter(is_active=True),
        required=False,
        empty_label="All Warehouses",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    status = forms.ChoiceField(
        choices=[
            ('', 'All'),
            ('processed', 'Processed'),
            ('pending', 'Pending'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
