#!/usr/bin/env python
"""
Verify that all system status messages have been removed from all templates
"""

import os
import glob

def scan_templates_for_status_messages():
    """Scan all templates for system status messages"""
    print("🧹 Scanning All Templates for Status Messages...\n")
    
    # Define patterns that indicate status messages
    status_patterns = [
        'Dashboard Loaded Successfully',
        'Server is Working',
        'All systems operational',
        'Successfully loaded',
        'Rendering correctly',
        'Loading properly',
        'System Status',
        'PowerShell Issue Resolved',
        'New Features Successfully Implemented',
        'All Features Are Live',
        'Complete Page Inventory',
        'Pages that load successfully',
        'Template System',
        'Static Files',
        'Bootstrap.*Loaded',
        'Font Awesome.*Loaded',
    ]
    
    # Get all HTML templates
    template_dir = r"Desktop\Odoo-like inventory system\templates"
    template_files = []
    
    # Recursively find all HTML files
    for root, dirs, files in os.walk(template_dir):
        for file in files:
            if file.endswith('.html'):
                template_files.append(os.path.join(root, file))
    
    print(f"📄 Found {len(template_files)} template files to scan")
    
    issues_found = []
    clean_files = []
    
    for template_file in template_files:
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_issues = []
            
            # Check for each status pattern
            for pattern in status_patterns:
                if pattern.lower() in content.lower():
                    file_issues.append(pattern)
            
            if file_issues:
                issues_found.append({
                    'file': template_file,
                    'issues': file_issues
                })
            else:
                clean_files.append(template_file)
                
        except Exception as e:
            print(f"❌ Error reading {template_file}: {e}")
    
    # Report results
    print(f"\n📊 Scan Results:")
    print(f"   Clean files: {len(clean_files)}")
    print(f"   Files with status messages: {len(issues_found)}")
    
    if issues_found:
        print(f"\n⚠️  Files Still Containing Status Messages:")
        for issue in issues_found:
            file_name = os.path.basename(issue['file'])
            print(f"   ❌ {file_name}:")
            for pattern in issue['issues']:
                print(f"      - Contains: '{pattern}'")
    else:
        print(f"\n🎉 All templates are clean!")
        print(f"✅ No system status messages found")
    
    # Show some clean files as examples
    if clean_files:
        print(f"\n✅ Examples of Clean Templates:")
        for i, clean_file in enumerate(clean_files[:5]):
            file_name = os.path.basename(clean_file)
            print(f"   ✅ {file_name}")
        if len(clean_files) > 5:
            print(f"   ... and {len(clean_files) - 5} more")
    
    return len(issues_found) == 0

def check_key_templates():
    """Check specific key templates that were cleaned"""
    print(f"\n🔍 Checking Key Templates That Were Cleaned:")
    
    key_templates = [
        'dashboard.html',
        'system_status.html', 
        'test_simple.html',
        'features_demo.html',
        'sitemap.html'
    ]
    
    template_dir = r"Desktop\Odoo-like inventory system\templates"
    
    all_clean = True
    
    for template_name in key_templates:
        # Find the template file
        template_found = False
        
        for root, dirs, files in os.walk(template_dir):
            if template_name in files:
                template_path = os.path.join(root, template_name)
                template_found = True
                
                try:
                    with open(template_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for common status message indicators
                    status_indicators = [
                        'Dashboard Loaded Successfully',
                        'Server is Working',
                        'All systems operational',
                        'New Features Successfully Implemented',
                        'All Features Are Live',
                        'PowerShell Issue Resolved'
                    ]
                    
                    found_issues = []
                    for indicator in status_indicators:
                        if indicator in content:
                            found_issues.append(indicator)
                    
                    if found_issues:
                        print(f"   ❌ {template_name}: Still has status messages")
                        for issue in found_issues:
                            print(f"      - '{issue}'")
                        all_clean = False
                    else:
                        print(f"   ✅ {template_name}: Clean")
                        
                except Exception as e:
                    print(f"   ❌ {template_name}: Error reading - {e}")
                    all_clean = False
                
                break
        
        if not template_found:
            print(f"   ⚠️  {template_name}: Not found")
    
    return all_clean

if __name__ == "__main__":
    print("🚀 Complete Template Status Message Cleanup Verification\n")
    
    # Scan all templates
    all_templates_clean = scan_templates_for_status_messages()
    
    # Check key templates specifically
    key_templates_clean = check_key_templates()
    
    print(f"\n📋 Final Results:")
    print(f"   All Templates: {'✅ CLEAN' if all_templates_clean else '❌ NEEDS CLEANUP'}")
    print(f"   Key Templates: {'✅ CLEAN' if key_templates_clean else '❌ NEEDS CLEANUP'}")
    
    if all_templates_clean and key_templates_clean:
        print(f"\n🎉 SUCCESS: All system status messages removed!")
        print(f"✅ Your inventory system now shows only functional content")
        print(f"✅ No more distracting status messages")
        print(f"✅ Professional appearance across all pages")
        print(f"🌐 System ready for business use")
    else:
        print(f"\n⚠️  Some templates still need cleanup")
        print(f"🔧 Review the files listed above and remove remaining status messages")
