<!-- Export and Print Buttons Component -->
<!-- Usage: {% include 'components/export_buttons.html' with export_url='your_export_url' page_title='Page Title' %} -->

<div class="export-buttons no-print">
    <div class="btn-group">
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-download"></i> Export & Print
        </button>
        <ul class="dropdown-menu">
            <li>
                <h6 class="dropdown-header">
                    <i class="fas fa-print"></i> Print Options
                </h6>
            </li>
            <li>
                <a class="dropdown-item" href="javascript:void(0)" onclick="printPage()">
                    <i class="fas fa-print"></i> Print Current Page
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <h6 class="dropdown-header">
                    <i class="fas fa-file-export"></i> Export Options
                </h6>
            </li>
            {% if export_url %}
                <li>
                    <a class="dropdown-item" href="{{ export_url }}?format=pdf" target="_blank">
                        <i class="fas fa-file-pdf text-danger"></i> Export as PDF
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="{{ export_url }}?format=excel" target="_blank">
                        <i class="fas fa-file-excel text-success"></i> Export as Excel
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="{{ export_url }}?format=csv" target="_blank">
                        <i class="fas fa-file-csv text-info"></i> Export as CSV
                    </a>
                </li>
            {% else %}
                <li>
                    <a class="dropdown-item" href="javascript:void(0)" onclick="exportPage('pdf')">
                        <i class="fas fa-file-pdf text-danger"></i> Export as PDF
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="javascript:void(0)" onclick="exportPage('excel')">
                        <i class="fas fa-file-excel text-success"></i> Export as Excel
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="javascript:void(0)" onclick="exportPage('csv')">
                        <i class="fas fa-file-csv text-info"></i> Export as CSV
                    </a>
                </li>
            {% endif %}
            <li><hr class="dropdown-divider"></li>
            <li>
                <h6 class="dropdown-header">
                    <i class="fas fa-share"></i> Share Options
                </h6>
            </li>
            <li>
                <a class="dropdown-item" href="javascript:void(0)" onclick="copyPageUrl()">
                    <i class="fas fa-link"></i> Copy Page Link
                </a>
            </li>
            <li>
                <a class="dropdown-item" href="javascript:void(0)" onclick="emailPage()">
                    <i class="fas fa-envelope"></i> Email Page
                </a>
            </li>
        </ul>
    </div>
</div>

<!-- Print Header (only visible when printing) -->
<div class="print-header print-only" style="display: none;">
    <h1>{{ page_title|default:"Inventory Management System" }}</h1>
    <div class="company-info">
        <p>Generated on {{ "now"|date:"F d, Y \a\t g:i A" }}</p>
        {% if user.is_authenticated %}
            <p>Prepared by: {{ user.get_full_name|default:user.username }}</p>
        {% endif %}
    </div>
</div>

<!-- Print Footer (only visible when printing) -->
<div class="print-footer print-only" style="display: none;">
    <p>
        Page <span class="page-number"></span> | 
        {{ page_title|default:"Inventory Management System" }} | 
        Confidential
    </p>
</div>

<script>
// Additional export functionality
function copyPageUrl() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        // Show success message
        showToast('Page URL copied to clipboard!', 'success');
    }, function(err) {
        console.error('Could not copy text: ', err);
        showToast('Failed to copy URL', 'error');
    });
}

function emailPage() {
    const subject = encodeURIComponent('{{ page_title|default:"Inventory Management System" }}');
    const body = encodeURIComponent('Please find the link to the page: ' + window.location.href);
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
}

function showToast(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// Print page numbering
window.addEventListener('beforeprint', function() {
    // Add page numbers for print
    const style = document.createElement('style');
    style.innerHTML = `
        @media print {
            .page-number:after {
                counter-increment: page;
                content: counter(page);
            }
        }
    `;
    document.head.appendChild(style);
});
</script>

<style>
/* Export buttons styling */
.export-buttons {
    margin-bottom: 1rem;
}

.export-buttons .dropdown-menu {
    min-width: 200px;
}

.export-buttons .dropdown-header {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6c757d;
    padding: 0.5rem 1rem 0.25rem;
}

.export-buttons .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.export-buttons .dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

.export-buttons .dropdown-item:hover {
    background-color: #f8f9fa;
}

/* Print-specific styles */
@media print {
    .print-header {
        margin-bottom: 2rem;
        text-align: center;
        border-bottom: 2px solid #000;
        padding-bottom: 1rem;
    }
    
    .print-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 10pt;
        border-top: 1px solid #000;
        padding-top: 0.5rem;
        background: white;
    }
}
</style>
