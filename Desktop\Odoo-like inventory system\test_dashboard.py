#!/usr/bin/env python
"""
Test dashboard loading performance
"""

import os
import sys
import django
import time

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from users.models import User

def test_dashboard_loading():
    """Test dashboard loading speed and functionality"""
    print("🚀 Testing Dashboard Loading Performance...\n")
    
    try:
        # Create test client
        client = Client()
        
        # Get a user to login with
        user = User.objects.filter(is_active=True, role='admin').first()
        if not user:
            print("❌ No admin user found for testing")
            return
        
        print(f"📋 Testing with user: {user.username}")
        
        # Test login
        login_start = time.time()
        login_response = client.get('/login/')
        login_time = time.time() - login_start
        
        print(f"✅ Login page loaded in {login_time:.3f} seconds (Status: {login_response.status_code})")
        
        # Login the user
        client.force_login(user)
        
        # Test dashboard loading
        dashboard_start = time.time()
        dashboard_response = client.get('/dashboard/')
        dashboard_time = time.time() - dashboard_start
        
        print(f"✅ Dashboard loaded in {dashboard_time:.3f} seconds (Status: {dashboard_response.status_code})")
        
        # Check if dashboard loaded successfully
        if dashboard_response.status_code == 200:
            content = dashboard_response.content.decode('utf-8')
            
            # Check for success indicators
            if 'Dashboard Loaded Successfully' in content:
                print("✅ Dashboard loaded with success message")
            elif 'Dashboard Loading Issue' in content:
                print("⚠️  Dashboard loaded with error message")
            else:
                print("📋 Dashboard loaded (status unknown)")
            
            # Check for key elements
            checks = [
                ('Total Products', 'total_products' in content.lower()),
                ('Total Warehouses', 'total_warehouses' in content.lower()),
                ('Stock Value', 'stock_value' in content.lower()),
                ('Recent Movements', 'recent' in content.lower()),
                ('Low Stock', 'low_stock' in content.lower()),
            ]
            
            print("\n📊 Dashboard Content Check:")
            for check_name, check_result in checks:
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}: {'Present' if check_result else 'Missing'}")
        
        else:
            print(f"❌ Dashboard failed to load (Status: {dashboard_response.status_code})")
        
        # Performance assessment
        print(f"\n⚡ Performance Assessment:")
        if dashboard_time < 1.0:
            print(f"   🚀 Excellent: {dashboard_time:.3f}s (< 1s)")
        elif dashboard_time < 3.0:
            print(f"   ✅ Good: {dashboard_time:.3f}s (< 3s)")
        elif dashboard_time < 5.0:
            print(f"   ⚠️  Acceptable: {dashboard_time:.3f}s (< 5s)")
        else:
            print(f"   ❌ Slow: {dashboard_time:.3f}s (> 5s)")
        
        # Test other key pages
        print(f"\n🔍 Testing Other Key Pages:")
        
        pages_to_test = [
            ('/products/', 'Products'),
            ('/warehouses/list/', 'Warehouses'),
            ('/sales/orders/', 'Sales Orders'),
            ('/admin/', 'Admin Panel'),
        ]
        
        for url, name in pages_to_test:
            try:
                start_time = time.time()
                response = client.get(url)
                load_time = time.time() - start_time
                
                if response.status_code == 200:
                    print(f"   ✅ {name}: {load_time:.3f}s (Status: {response.status_code})")
                elif response.status_code == 302:
                    print(f"   🔄 {name}: {load_time:.3f}s (Redirect: {response.status_code})")
                else:
                    print(f"   ❌ {name}: {load_time:.3f}s (Error: {response.status_code})")
            except Exception as e:
                print(f"   ❌ {name}: Error - {str(e)}")
        
        print(f"\n🎉 Dashboard testing completed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dashboard_loading()
