#!/usr/bin/env python
"""
Fix admin users and groups setup
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from users.models import User, UserProfile
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType

def create_groups_and_permissions():
    """Create user groups with appropriate permissions"""
    
    # Define groups and their permissions
    groups_permissions = {
        'Administrators': [
            'add_user', 'change_user', 'delete_user', 'view_user',
            'add_product', 'change_product', 'delete_product', 'view_product',
            'add_warehouse', 'change_warehouse', 'delete_warehouse', 'view_warehouse',
            'add_salesorder', 'change_salesorder', 'delete_salesorder', 'view_salesorder',
            'add_customer', 'change_customer', 'delete_customer', 'view_customer',
        ],
        'Managers': [
            'view_user', 'change_user',
            'add_product', 'change_product', 'view_product',
            'add_warehouse', 'change_warehouse', 'view_warehouse',
            'add_salesorder', 'change_salesorder', 'view_salesorder',
            'add_customer', 'change_customer', 'view_customer',
        ],
        'Warehouse Staff': [
            'view_product', 'change_product',
            'view_warehouse', 'change_warehouse',
            'view_salesorder',
        ],
        'Sales Staff': [
            'view_product',
            'add_salesorder', 'change_salesorder', 'view_salesorder',
            'add_customer', 'change_customer', 'view_customer',
        ],
        'Viewers': [
            'view_user', 'view_product', 'view_warehouse', 'view_salesorder', 'view_customer',
        ]
    }
    
    print("🔧 Creating/updating groups and permissions...")
    
    for group_name, permission_codenames in groups_permissions.items():
        group, created = Group.objects.get_or_create(name=group_name)
        if created:
            print(f"✅ Created group: {group_name}")
        else:
            print(f"📋 Group exists: {group_name}")
        
        # Clear existing permissions
        group.permissions.clear()
        
        # Add permissions
        for codename in permission_codenames:
            try:
                permission = Permission.objects.get(codename=codename)
                group.permissions.add(permission)
            except Permission.DoesNotExist:
                print(f"⚠️  Permission not found: {codename}")
        
        print(f"   Added {group.permissions.count()} permissions to {group_name}")

def assign_users_to_groups():
    """Assign users to appropriate groups based on their roles"""
    
    role_to_group = {
        'admin': 'Administrators',
        'manager': 'Managers',
        'warehouse_staff': 'Warehouse Staff',
        'sales_staff': 'Sales Staff',
        'accountant': 'Viewers',  # For now, accountants get viewer access
        'viewer': 'Viewers'
    }
    
    print("\n👥 Assigning users to groups...")
    
    users = User.objects.all()
    for user in users:
        # Clear existing groups
        user.groups.clear()
        
        # Assign to appropriate group
        group_name = role_to_group.get(user.role, 'Viewers')
        try:
            group = Group.objects.get(name=group_name)
            user.groups.add(group)
            print(f"✅ {user.username} ({user.get_role_display()}) → {group_name}")
        except Group.DoesNotExist:
            print(f"❌ Group not found: {group_name}")

def ensure_staff_status():
    """Ensure admin users have staff status to access admin panel"""
    print("\n🔑 Ensuring admin users have staff status...")
    
    admin_users = User.objects.filter(role='admin')
    for user in admin_users:
        if not user.is_staff:
            user.is_staff = True
            user.save()
            print(f"✅ Granted staff status to: {user.username}")
        else:
            print(f"📋 {user.username} already has staff status")

def create_user_profiles():
    """Create UserProfile for users that don't have one"""
    print("\n👤 Creating missing user profiles...")
    
    users_without_profile = User.objects.filter(profile__isnull=True)
    for user in users_without_profile:
        profile = UserProfile.objects.create(
            user=user,
            employee_id=f"EMP{user.id:04d}",
            department="General"
        )
        print(f"✅ Created profile for: {user.username}")

def main():
    print("🚀 Fixing Admin Users and Groups Setup...\n")
    
    # Show current users
    print("📊 Current Users:")
    users = User.objects.all()
    for user in users:
        print(f"   - {user.username} (Role: {user.get_role_display()}, Staff: {user.is_staff}, Superuser: {user.is_superuser})")
    
    # Create groups and permissions
    create_groups_and_permissions()
    
    # Assign users to groups
    assign_users_to_groups()
    
    # Ensure staff status
    ensure_staff_status()
    
    # Create user profiles
    create_user_profiles()
    
    print("\n🎉 Admin setup completed!")
    print("\n📋 Summary:")
    print(f"   Total Users: {User.objects.count()}")
    print(f"   Total Groups: {Group.objects.count()}")
    print(f"   Staff Users: {User.objects.filter(is_staff=True).count()}")
    print(f"   Superusers: {User.objects.filter(is_superuser=True).count()}")
    
    print("\n🔑 Admin Panel Access:")
    staff_users = User.objects.filter(is_staff=True)
    for user in staff_users:
        print(f"   - {user.username} can access admin panel")
    
    print("\n🌐 Admin URL: http://127.0.0.1:8000/admin/")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
