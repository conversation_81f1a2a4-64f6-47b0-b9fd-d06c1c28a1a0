#!/usr/bin/env python
"""
Test the product loading fix
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from products.models import Product, Category, Brand
from inventory.models import Stock
from django.db.models import Sum

def test_product_statistics():
    """Test that product statistics are calculated correctly"""
    print("📊 Testing Product Statistics Calculation...\n")
    
    try:
        # Test basic counts
        total_products = Product.objects.count()
        active_products = Product.objects.filter(is_active=True).count()
        inactive_products = Product.objects.filter(is_active=False).count()
        total_categories = Category.objects.filter(is_active=True).count()
        total_brands = Brand.objects.filter(is_active=True).count()
        
        print(f"📦 Product Statistics:")
        print(f"   Total Products: {total_products}")
        print(f"   Active Products: {active_products}")
        print(f"   Inactive Products: {inactive_products}")
        print(f"   Total Categories: {total_categories}")
        print(f"   Total Brands: {total_brands}")
        
        # Test low stock calculation
        print(f"\n📉 Low Stock Analysis:")
        low_stock_count = 0
        low_stock_products = []
        
        for product in Product.objects.filter(is_active=True)[:10]:  # Test first 10 products
            total_stock = Stock.objects.filter(product=product).aggregate(
                total=Sum('quantity')
            )['total'] or 0
            
            print(f"   {product.name[:30]:<30} | Stock: {total_stock:>3} | Min: {product.min_stock_level:>3} | {'LOW' if total_stock <= product.min_stock_level else 'OK'}")
            
            if total_stock <= product.min_stock_level:
                low_stock_count += 1
                low_stock_products.append(product.name)
        
        print(f"\n   Total Low Stock Products: {low_stock_count}")
        if low_stock_products:
            print(f"   Low Stock Items: {', '.join(low_stock_products[:5])}")
        
        # Test product queryset with stock annotation
        print(f"\n🔍 Testing Product Queryset with Stock:")
        products_with_stock = Product.objects.select_related('category', 'brand').annotate(
            total_stock=Sum('stock__quantity')
        )[:5]
        
        for product in products_with_stock:
            stock_value = product.total_stock or 0
            print(f"   {product.name[:30]:<30} | Annotated Stock: {stock_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing product statistics: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_context():
    """Test that the template context would be correct"""
    print(f"\n🎨 Testing Template Context Data:")
    
    try:
        # Simulate what the view should provide
        context_data = {
            'total_products': Product.objects.count(),
            'active_products': Product.objects.filter(is_active=True).count(),
            'inactive_products': Product.objects.filter(is_active=False).count(),
            'total_categories': Category.objects.filter(is_active=True).count(),
            'total_brands': Brand.objects.filter(is_active=True).count(),
        }
        
        # Calculate low stock
        low_stock_count = 0
        for product in Product.objects.filter(is_active=True):
            total_stock = Stock.objects.filter(product=product).aggregate(
                total=Sum('quantity')
            )['total'] or 0
            if total_stock <= product.min_stock_level:
                low_stock_count += 1
        
        context_data['low_stock_products'] = low_stock_count
        
        print(f"📋 Context Data for Template:")
        for key, value in context_data.items():
            print(f"   {key}: {value}")
        
        # Check if all values are reasonable
        issues = []
        if context_data['total_products'] == 0:
            issues.append("No products in database")
        if context_data['total_categories'] == 0:
            issues.append("No categories in database")
        
        if issues:
            print(f"\n⚠️  Potential Issues:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print(f"\n✅ All context data looks good!")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ Error testing template context: {e}")
        return False

def check_import_issues():
    """Check for any import issues in the views"""
    print(f"\n🔍 Checking Import Issues:")
    
    try:
        # Test imports that the view uses
        from products.views import ProductListView
        print(f"   ✅ ProductListView imported successfully")
        
        from products.models import Product, Category, Brand
        print(f"   ✅ Product models imported successfully")
        
        from inventory.models import Stock
        print(f"   ✅ Stock model imported successfully")
        
        from django.db.models import Sum
        print(f"   ✅ Django Sum imported successfully")
        
        # Test that the view can be instantiated
        view = ProductListView()
        print(f"   ✅ ProductListView can be instantiated")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Product Loading Fix Testing\n")
    
    import_success = check_import_issues()
    stats_success = test_product_statistics()
    context_success = test_template_context()
    
    print(f"\n📋 Test Results:")
    print(f"   Import Check: {'✅ PASSED' if import_success else '❌ FAILED'}")
    print(f"   Statistics Test: {'✅ PASSED' if stats_success else '❌ FAILED'}")
    print(f"   Context Test: {'✅ PASSED' if context_success else '❌ FAILED'}")
    
    if import_success and stats_success and context_success:
        print(f"\n🎉 All tests passed!")
        print(f"✅ Product loading fix should work correctly")
        print(f"✅ No more 'Loading...' text in product management")
        print(f"✅ Statistics will display actual numbers")
        print(f"✅ Stock quantities will show properly")
    else:
        print(f"\n⚠️  Some tests failed")
        print(f"🔧 Review the issues above before testing the web interface")
