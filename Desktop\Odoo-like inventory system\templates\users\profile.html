{% extends 'base.html' %}

{% block title %}My Profile - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">My Profile</h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Personal Information</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    {% if user_form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ user_form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ user_form.username.id_for_label }}" class="form-label">Username</label>
                                {{ user_form.username }}
                                {% if user_form.username.errors %}
                                    <div class="text-danger small">{{ user_form.username.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ user_form.email.id_for_label }}" class="form-label">Email</label>
                                {{ user_form.email }}
                                {% if user_form.email.errors %}
                                    <div class="text-danger small">{{ user_form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ user_form.first_name.id_for_label }}" class="form-label">First Name</label>
                                {{ user_form.first_name }}
                                {% if user_form.first_name.errors %}
                                    <div class="text-danger small">{{ user_form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ user_form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                {{ user_form.last_name }}
                                {% if user_form.last_name.errors %}
                                    <div class="text-danger small">{{ user_form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ user_form.phone.id_for_label }}" class="form-label">Phone</label>
                                {{ user_form.phone }}
                                {% if user_form.phone.errors %}
                                    <div class="text-danger small">{{ user_form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Role</label>
                                <input type="text" class="form-control" value="{{ user.get_role_display }}" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ user_form.address.id_for_label }}" class="form-label">Address</label>
                        {{ user_form.address }}
                        {% if user_form.address.errors %}
                            <div class="text-danger small">{{ user_form.address.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ user_form.profile_picture.id_for_label }}" class="form-label">Profile Picture</label>
                        {{ user_form.profile_picture }}
                        {% if user_form.profile_picture.errors %}
                            <div class="text-danger small">{{ user_form.profile_picture.errors.0 }}</div>
                        {% endif %}
                        {% if user.profile_picture %}
                            <div class="mt-2">
                                <img src="{{ user.profile_picture.url }}" alt="Current profile picture" class="img-thumbnail" style="max-width: 100px;">
                                <small class="text-muted d-block">Current profile picture</small>
                            </div>
                        {% endif %}
                    </div>

                    <h6 class="mt-4 mb-3">Profile Details</h6>
                    
                    {% if profile_form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ profile_form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ profile_form.employee_id.id_for_label }}" class="form-label">Employee ID</label>
                                {{ profile_form.employee_id }}
                                {% if profile_form.employee_id.errors %}
                                    <div class="text-danger small">{{ profile_form.employee_id.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ profile_form.department.id_for_label }}" class="form-label">Department</label>
                                {{ profile_form.department }}
                                {% if profile_form.department.errors %}
                                    <div class="text-danger small">{{ profile_form.department.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ profile_form.hire_date.id_for_label }}" class="form-label">Hire Date</label>
                                {{ profile_form.hire_date }}
                                {% if profile_form.hire_date.errors %}
                                    <div class="text-danger small">{{ profile_form.hire_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ profile_form.emergency_contact.id_for_label }}" class="form-label">Emergency Contact</label>
                                {{ profile_form.emergency_contact }}
                                {% if profile_form.emergency_contact.errors %}
                                    <div class="text-danger small">{{ profile_form.emergency_contact.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ profile_form.emergency_phone.id_for_label }}" class="form-label">Emergency Phone</label>
                                {{ profile_form.emergency_phone }}
                                {% if profile_form.emergency_phone.errors %}
                                    <div class="text-danger small">{{ profile_form.emergency_phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ profile_form.notes.id_for_label }}" class="form-label">Notes</label>
                        {{ profile_form.notes }}
                        {% if profile_form.notes.errors %}
                            <div class="text-danger small">{{ profile_form.notes.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Profile Summary</h5>
            </div>
            <div class="card-body text-center">
                {% if user.profile_picture %}
                    <img src="{{ user.profile_picture.url }}" alt="Profile Picture" class="img-fluid rounded-circle mb-3" style="max-width: 120px;">
                {% else %}
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 120px; height: 120px;">
                        <i class="fas fa-user fa-3x text-muted"></i>
                    </div>
                {% endif %}
                <h5>{{ user.get_full_name|default:user.username }}</h5>
                <p class="text-muted">{{ user.get_role_display }}</p>
                
                <hr>
                
                <div class="text-start">
                    <small class="text-muted">
                        <strong>Member since:</strong> {{ user.date_joined|date:"M Y" }}<br>
                        <strong>Last login:</strong> 
                        {% if user.last_login %}
                            {{ user.last_login|date:"M d, Y" }}
                        {% else %}
                            Never
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Permissions</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Inventory Management
                        {% if user.can_manage_inventory %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-secondary">No</span>
                        {% endif %}
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Sales Management
                        {% if user.can_manage_sales %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-secondary">No</span>
                        {% endif %}
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Accounting Access
                        {% if user.can_view_accounting %}
                            <span class="badge bg-success">Yes</span>
                        {% else %}
                            <span class="badge bg-secondary">No</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
