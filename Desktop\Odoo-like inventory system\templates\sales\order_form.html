{% extends 'base.html' %}

{% block title %}{% if object %}Edit Order{% else %}New Order{% endif %} - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{% if object %}Edit Order{% else %}New Sales Order{% endif %}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'sales:order_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Orders
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <form method="post" id="orderForm">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <!-- Order Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Order Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.customer.id_for_label }}" class="form-label">Customer *</label>
                                {{ form.customer }}
                                {% if form.customer.errors %}
                                    <div class="text-danger small">{{ form.customer.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.order_date.id_for_label }}" class="form-label">Order Date *</label>
                                {{ form.order_date }}
                                {% if form.order_date.errors %}
                                    <div class="text-danger small">{{ form.order_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.expected_delivery_date.id_for_label }}" class="form-label">Expected Delivery</label>
                                {{ form.expected_delivery_date }}
                                {% if form.expected_delivery_date.errors %}
                                    <div class="text-danger small">{{ form.expected_delivery_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.priority.id_for_label }}" class="form-label">Priority</label>
                                {{ form.priority }}
                                {% if form.priority.errors %}
                                    <div class="text-danger small">{{ form.priority.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if object %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="text-danger small">{{ form.status.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="{{ form.shipping_address.id_for_label }}" class="form-label">Shipping Address</label>
                        {{ form.shipping_address }}
                        {% if form.shipping_address.errors %}
                            <div class="text-danger small">{{ form.shipping_address.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">Leave empty to use customer's default address</small>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Order Notes</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Order Items</h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="addItemBtn">
                        <i class="fas fa-plus"></i> Add Item
                    </button>
                </div>
                <div class="card-body">
                    <div id="orderItems">
                        {% if object and object.items.exists %}
                            {% for item in object.items.all %}
                                <div class="row order-item mb-3 p-3 border rounded">
                                    <div class="col-md-5">
                                        <label class="form-label">Product</label>
                                        <select class="form-control product-select" name="item_product_{{ forloop.counter0 }}">
                                            <option value="">Select Product</option>
                                            {% for product in products %}
                                                <option value="{{ product.id }}" {% if product.id == item.product.id %}selected{% endif %} data-price="{{ product.selling_price }}">
                                                    {{ product.name }} - ${{ product.selling_price }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Quantity</label>
                                        <input type="number" class="form-control quantity-input" name="item_quantity_{{ forloop.counter0 }}" value="{{ item.quantity }}" min="1">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Unit Price</label>
                                        <input type="number" class="form-control price-input" name="item_price_{{ forloop.counter0 }}" value="{{ item.unit_price }}" step="0.01">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">Total</label>
                                        <input type="text" class="form-control total-display" readonly value="${{ item.total_price }}">
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end">
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-item">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-3 text-muted">
                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                <p>No items added yet. Click "Add Item" to start.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Order Totals -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Order Totals</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.tax_amount.id_for_label }}" class="form-label">Tax Amount</label>
                                {{ form.tax_amount }}
                                {% if form.tax_amount.errors %}
                                    <div class="text-danger small">{{ form.tax_amount.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.shipping_cost.id_for_label }}" class="form-label">Shipping Cost</label>
                                {{ form.shipping_cost }}
                                {% if form.shipping_cost.errors %}
                                    <div class="text-danger small">{{ form.shipping_cost.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 offset-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td>Subtotal:</td>
                                    <td class="text-end" id="subtotalDisplay">$0.00</td>
                                </tr>
                                <tr>
                                    <td>Tax:</td>
                                    <td class="text-end" id="taxDisplay">$0.00</td>
                                </tr>
                                <tr>
                                    <td>Shipping:</td>
                                    <td class="text-end" id="shippingDisplay">$0.00</td>
                                </tr>
                                <tr class="table-active">
                                    <th>Total:</th>
                                    <th class="text-end" id="totalDisplay">$0.00</th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between mt-3">
                <a href="{% url 'sales:order_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> {% if object %}Update Order{% else %}Create Order{% endif %}
                </button>
            </div>
        </form>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>Order Guidelines:</h6>
                    <ul class="mb-0">
                        <li>Select customer and add products</li>
                        <li>Quantities and prices update automatically</li>
                        <li>Tax and shipping are optional</li>
                        <li>Save as draft or confirm immediately</li>
                    </ul>
                </div>
            </div>
        </div>

        {% if object %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Order Summary</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td>Order Number:</td>
                        <td><code>{{ object.order_number }}</code></td>
                    </tr>
                    <tr>
                        <td>Status:</td>
                        <td>
                            <span class="badge bg-{% if object.status == 'pending' %}warning{% elif object.status == 'confirmed' %}info{% elif object.status == 'shipped' %}primary{% elif object.status == 'delivered' %}success{% elif object.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                {{ object.get_status_display }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td>Created:</td>
                        <td>{{ object.created_at|date:"M d, Y" }}</td>
                    </tr>
                    <tr>
                        <td>Items:</td>
                        <td>{{ object.items.count }}</td>
                    </tr>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let itemCounter = {{ object.items.count|default:0 }};
    
    // Add item functionality
    document.getElementById('addItemBtn').addEventListener('click', function() {
        const orderItems = document.getElementById('orderItems');
        const newItem = createOrderItem(itemCounter);
        orderItems.appendChild(newItem);
        itemCounter++;
        updateTotals();
    });
    
    // Create order item HTML
    function createOrderItem(index) {
        const div = document.createElement('div');
        div.className = 'row order-item mb-3 p-3 border rounded';
        div.innerHTML = `
            <div class="col-md-5">
                <label class="form-label">Product</label>
                <select class="form-control product-select" name="item_product_${index}">
                    <option value="">Select Product</option>
                    {% for product in products %}
                        <option value="{{ product.id }}" data-price="{{ product.selling_price }}">
                            {{ product.name }} - ${{ product.selling_price }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Quantity</label>
                <input type="number" class="form-control quantity-input" name="item_quantity_${index}" value="1" min="1">
            </div>
            <div class="col-md-2">
                <label class="form-label">Unit Price</label>
                <input type="number" class="form-control price-input" name="item_price_${index}" value="0.00" step="0.01">
            </div>
            <div class="col-md-2">
                <label class="form-label">Total</label>
                <input type="text" class="form-control total-display" readonly value="$0.00">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="button" class="btn btn-sm btn-outline-danger remove-item">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        // Add event listeners
        const productSelect = div.querySelector('.product-select');
        const quantityInput = div.querySelector('.quantity-input');
        const priceInput = div.querySelector('.price-input');
        const removeBtn = div.querySelector('.remove-item');
        
        productSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.dataset.price) {
                priceInput.value = selectedOption.dataset.price;
                updateItemTotal(div);
            }
        });
        
        quantityInput.addEventListener('input', () => updateItemTotal(div));
        priceInput.addEventListener('input', () => updateItemTotal(div));
        removeBtn.addEventListener('click', () => {
            div.remove();
            updateTotals();
        });
        
        return div;
    }
    
    // Update item total
    function updateItemTotal(itemDiv) {
        const quantity = parseFloat(itemDiv.querySelector('.quantity-input').value) || 0;
        const price = parseFloat(itemDiv.querySelector('.price-input').value) || 0;
        const total = quantity * price;
        itemDiv.querySelector('.total-display').value = `$${total.toFixed(2)}`;
        updateTotals();
    }
    
    // Update order totals
    function updateTotals() {
        let subtotal = 0;
        document.querySelectorAll('.order-item').forEach(item => {
            const quantity = parseFloat(item.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(item.querySelector('.price-input').value) || 0;
            subtotal += quantity * price;
        });
        
        const tax = parseFloat(document.querySelector('[name="tax_amount"]').value) || 0;
        const shipping = parseFloat(document.querySelector('[name="shipping_cost"]').value) || 0;
        const total = subtotal + tax + shipping;
        
        document.getElementById('subtotalDisplay').textContent = `$${subtotal.toFixed(2)}`;
        document.getElementById('taxDisplay').textContent = `$${tax.toFixed(2)}`;
        document.getElementById('shippingDisplay').textContent = `$${shipping.toFixed(2)}`;
        document.getElementById('totalDisplay').textContent = `$${total.toFixed(2)}`;
    }
    
    // Initialize existing items
    document.querySelectorAll('.order-item').forEach(item => {
        const productSelect = item.querySelector('.product-select');
        const quantityInput = item.querySelector('.quantity-input');
        const priceInput = item.querySelector('.price-input');
        const removeBtn = item.querySelector('.remove-item');
        
        if (productSelect) productSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.dataset.price) {
                priceInput.value = selectedOption.dataset.price;
                updateItemTotal(item);
            }
        });
        
        if (quantityInput) quantityInput.addEventListener('input', () => updateItemTotal(item));
        if (priceInput) priceInput.addEventListener('input', () => updateItemTotal(item));
        if (removeBtn) removeBtn.addEventListener('click', () => {
            item.remove();
            updateTotals();
        });
    });
    
    // Tax and shipping change listeners
    document.querySelector('[name="tax_amount"]').addEventListener('input', updateTotals);
    document.querySelector('[name="shipping_cost"]').addEventListener('input', updateTotals);
    
    // Initial calculation
    updateTotals();
});
</script>

{% endblock %}
