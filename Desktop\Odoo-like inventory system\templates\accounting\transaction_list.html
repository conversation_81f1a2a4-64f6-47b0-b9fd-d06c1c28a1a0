{% extends 'base.html' %}

{% block title %}Transactions - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Financial Transactions</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if user.can_view_accounting %}
        <a href="{% url 'accounting:transaction_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> New Transaction
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-md-12">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <input type="text" name="search" class="form-control" placeholder="Search transactions..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-2">
                <select name="transaction_type" class="form-control">
                    <option value="">All Types</option>
                    <option value="income" {% if request.GET.transaction_type == 'income' %}selected{% endif %}>Income</option>
                    <option value="expense" {% if request.GET.transaction_type == 'expense' %}selected{% endif %}>Expense</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-2">
                <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}">
            </div>
            <div class="col-md-2">
                <select name="account" class="form-control">
                    <option value="">All Accounts</option>
                    {% for account in accounts %}
                        <option value="{{ account.id }}" {% if request.GET.account == account.id|stringformat:"s" %}selected{% endif %}>
                            {{ account.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-outline-secondary">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Transactions ({{ transactions|length }})</h5>
    </div>
    <div class="card-body">
        {% if transactions %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Reference</th>
                            <th>Description</th>
                            <th>Account</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.transaction_date|date:"M d, Y" }}</td>
                                <td><code>{{ transaction.reference_number }}</code></td>
                                <td>
                                    <strong>{{ transaction.description|truncatechars:40 }}</strong>
                                    {% if transaction.notes %}
                                        <br><small class="text-muted">{{ transaction.notes|truncatechars:30 }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ transaction.account.name }}</td>
                                <td>
                                    <span class="badge bg-{% if transaction.transaction_type == 'income' %}success{% else %}danger{% endif %}">
                                        {{ transaction.get_transaction_type_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="{% if transaction.transaction_type == 'income' %}text-success{% else %}text-danger{% endif %}">
                                        {% if transaction.transaction_type == 'expense' %}-{% endif %}${{ transaction.amount|floatformat:2 }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'accounting:transaction_detail' transaction.pk %}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if user.can_view_accounting %}
                                            <a href="{% url 'accounting:transaction_edit' transaction.pk %}" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No transactions found</h5>
                <p class="text-muted">No transactions match your search criteria.</p>
                {% if user.can_view_accounting %}
                    <a href="{% url 'accounting:transaction_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Transaction
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Financial Summary -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">${{ total_income|floatformat:2 }}</h5>
                <p class="card-text text-muted">Total Income</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">${{ total_expenses|floatformat:2 }}</h5>
                <p class="card-text text-muted">Total Expenses</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title {% if net_profit >= 0 %}text-success{% else %}text-danger{% endif %}">
                    ${{ net_profit|floatformat:2 }}
                </h5>
                <p class="card-text text-muted">Net Profit</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">{{ transactions|length }}</h5>
                <p class="card-text text-muted">Total Transactions</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}
