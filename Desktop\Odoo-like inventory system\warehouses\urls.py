from django.urls import path
from . import views

app_name = 'warehouses'

urlpatterns = [
    # Dashboard
    path('', views.warehouse_dashboard, name='dashboard'),
    
    # Warehouse URLs
    path('list/', views.WarehouseListView.as_view(), name='list'),
    path('create/', views.WarehouseCreateView.as_view(), name='create'),
    path('<int:pk>/', views.WarehouseDetailView.as_view(), name='detail'),
    path('<int:pk>/edit/', views.WarehouseUpdateView.as_view(), name='edit'),
    path('<int:pk>/toggle-status/', views.warehouse_toggle_status, name='toggle_status'),
    path('<int:pk>/report/', views.warehouse_report, name='report'),
    
    # Location URLs
    path('<int:warehouse_pk>/locations/', views.LocationListView.as_view(), name='location_list'),
    path('<int:warehouse_pk>/locations/create/', views.location_create, name='location_create'),
    path('<int:warehouse_pk>/locations/<int:pk>/', views.LocationDetailView.as_view(), name='location_detail'),
    path('<int:warehouse_pk>/locations/<int:pk>/edit/', views.location_update, name='location_edit'),
    
    # Stock URLs
    path('<int:warehouse_pk>/stock/', views.StockListView.as_view(), name='stock_list'),
    path('<int:warehouse_pk>/stock/<int:pk>/', views.stock_detail, name='stock_detail'),
    
    # AJAX URLs
    path('ajax/search/', views.warehouse_search_ajax, name='search_ajax'),
    path('ajax/locations/search/', views.location_search_ajax, name='location_search_ajax'),
    path('ajax/check-code/', views.check_warehouse_code, name='check_code'),
]
