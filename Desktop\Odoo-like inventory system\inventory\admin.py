from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Sum
from .models import StockMovement, StockAdjustment, StockAdjustmentLine


class StockAdjustmentLineInline(admin.TabularInline):
    model = StockAdjustmentLine
    extra = 1
    fields = ('product', 'product_variant', 'location', 'expected_quantity', 'actual_quantity', 'difference', 'unit_cost')
    readonly_fields = ('difference',)


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    list_display = (
        'reference', 'product', 'movement_type', 'reason', 'quantity',
        'from_warehouse', 'to_warehouse', 'is_processed', 'created_at'
    )
    list_filter = (
        'movement_type', 'reason', 'is_processed', 'from_warehouse', 
        'to_warehouse', 'created_at'
    )
    search_fields = (
        'reference', 'product__name', 'product__sku', 'notes'
    )
    ordering = ('-created_at',)
    readonly_fields = (
        'reference', 'is_processed', 'processed_at', 'total_cost', 'created_at'
    )
    
    fieldsets = (
        ('Movement Information', {
            'fields': ('reference', 'movement_type', 'reason', 'quantity')
        }),
        ('Product', {
            'fields': ('product', 'product_variant')
        }),
        ('Location', {
            'fields': ('from_warehouse', 'from_location', 'to_warehouse', 'to_location')
        }),
        ('Financial', {
            'fields': ('unit_cost', 'total_cost'),
            'classes': ('collapse',)
        }),
        ('Related Documents', {
            'fields': ('purchase_order', 'sales_order'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('is_processed', 'processed_at', 'notes')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'product', 'product_variant', 'from_warehouse', 'to_warehouse', 'created_by'
        )
    
    actions = ['process_movements', 'mark_as_processed']
    
    def process_movements(self, request, queryset):
        """Process selected movements"""
        processed_count = 0
        error_count = 0
        
        for movement in queryset.filter(is_processed=False):
            try:
                movement.process_movement()
                processed_count += 1
            except Exception as e:
                error_count += 1
        
        if processed_count > 0:
            self.message_user(request, f'{processed_count} movements processed successfully.')
        if error_count > 0:
            self.message_user(request, f'{error_count} movements failed to process.', level='ERROR')
    
    process_movements.short_description = "Process selected movements"
    
    def mark_as_processed(self, request, queryset):
        """Mark movements as processed without actually processing them"""
        from django.utils import timezone
        updated = queryset.update(is_processed=True, processed_at=timezone.now())
        self.message_user(request, f'{updated} movements marked as processed.')
    
    mark_as_processed.short_description = "Mark as processed (without processing)"


@admin.register(StockAdjustment)
class StockAdjustmentAdmin(admin.ModelAdmin):
    list_display = (
        'reference', 'adjustment_type', 'warehouse', 'total_lines',
        'is_approved', 'approved_by', 'created_at'
    )
    list_filter = (
        'adjustment_type', 'warehouse', 'is_approved', 'created_at'
    )
    search_fields = ('reference', 'reason', 'notes')
    ordering = ('-created_at',)
    readonly_fields = (
        'reference', 'is_approved', 'approved_by', 'approved_at', 
        'created_at', 'total_lines'
    )
    
    inlines = [StockAdjustmentLineInline]
    
    fieldsets = (
        ('Adjustment Information', {
            'fields': ('reference', 'adjustment_type', 'warehouse')
        }),
        ('Details', {
            'fields': ('reason', 'notes')
        }),
        ('Approval', {
            'fields': ('is_approved', 'approved_by', 'approved_at')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'warehouse', 'created_by', 'approved_by'
        ).prefetch_related('lines')
    
    def total_lines(self, obj):
        """Display total number of adjustment lines"""
        return obj.lines.count()
    total_lines.short_description = 'Total Lines'
    
    actions = ['approve_adjustments']
    
    def approve_adjustments(self, request, queryset):
        """Approve selected adjustments"""
        if not request.user.is_superuser:
            self.message_user(request, 'Only superusers can approve adjustments.', level='ERROR')
            return
        
        approved_count = 0
        for adjustment in queryset.filter(is_approved=False):
            # Process adjustment lines
            for line in adjustment.lines.all():
                if line.difference != 0:
                    # Create stock movement for each line
                    from django.utils import timezone
                    movement = StockMovement.objects.create(
                        product=line.product,
                        product_variant=line.product_variant,
                        movement_type='adjustment',
                        reason='adjustment',
                        quantity=abs(line.difference),
                        to_warehouse=adjustment.warehouse if line.difference > 0 else None,
                        from_warehouse=adjustment.warehouse if line.difference < 0 else None,
                        to_location=line.location if line.difference > 0 else None,
                        from_location=line.location if line.difference < 0 else None,
                        unit_cost=line.unit_cost,
                        notes=f"Adjustment: {adjustment.reference}",
                        created_by=request.user
                    )
                    movement.process_movement()
            
            # Mark as approved
            adjustment.is_approved = True
            adjustment.approved_by = request.user
            adjustment.approved_at = timezone.now()
            adjustment.save()
            approved_count += 1
        
        self.message_user(request, f'{approved_count} adjustments approved and processed.')
    
    approve_adjustments.short_description = "Approve and process selected adjustments"


@admin.register(StockAdjustmentLine)
class StockAdjustmentLineAdmin(admin.ModelAdmin):
    list_display = (
        'adjustment', 'product', 'product_variant', 'location',
        'expected_quantity', 'actual_quantity', 'difference_display',
        'total_cost_impact'
    )
    list_filter = (
        'adjustment__warehouse', 'adjustment__adjustment_type',
        'product__category', 'adjustment__created_at'
    )
    search_fields = (
        'adjustment__reference', 'product__name', 'product__sku'
    )
    ordering = ('-adjustment__created_at',)
    readonly_fields = ('difference', 'total_cost_impact')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'adjustment', 'product', 'product_variant', 'location'
        )
    
    def difference_display(self, obj):
        """Display difference with color coding"""
        diff = obj.difference
        if diff > 0:
            return format_html('<span style="color: green;">+{}</span>', diff)
        elif diff < 0:
            return format_html('<span style="color: red;">{}</span>', diff)
        else:
            return format_html('<span style="color: gray;">0</span>')
    difference_display.short_description = 'Difference'
    difference_display.admin_order_field = 'difference'
