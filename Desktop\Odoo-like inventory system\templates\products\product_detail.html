{% extends 'base.html' %}

{% block title %}{{ product.name }} - Product Details{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ product.name }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'products:list' %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Products
        </a>
        {% if user.can_manage_inventory %}
        <div class="btn-group">
            <a href="{% url 'products:edit' product.pk %}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Product
            </a>
            <a href="{% url 'products:generate_barcode' product.pk %}" class="btn btn-outline-info">
                <i class="fas fa-barcode"></i> Generate Barcode
            </a>
        </div>
        {% endif %}
    </div>
</div>

<div class="row">
    <!-- Product Information -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Product Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">SKU:</th>
                                <td><code>{{ product.sku }}</code></td>
                            </tr>
                            <tr>
                                <th>Name:</th>
                                <td>{{ product.name }}</td>
                            </tr>
                            <tr>
                                <th>Category:</th>
                                <td>{{ product.category.name|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Brand:</th>
                                <td>{{ product.brand.name|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Product Type:</th>
                                <td>{{ product.get_product_type_display }}</td>
                            </tr>
                            <tr>
                                <th>Unit:</th>
                                <td>{{ product.get_unit_display }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Cost Price:</th>
                                <td>${{ product.cost_price|floatformat:2 }}</td>
                            </tr>
                            <tr>
                                <th>Selling Price:</th>
                                <td>${{ product.selling_price|floatformat:2 }}</td>
                            </tr>
                            <tr>
                                <th>Profit Margin:</th>
                                <td>{{ product.profit_margin|floatformat:2 }}%</td>
                            </tr>
                            <tr>
                                <th>Weight:</th>
                                <td>{{ product.weight|default:"-" }} kg</td>
                            </tr>
                            <tr>
                                <th>Dimensions:</th>
                                <td>
                                    {% if product.dimensions_length %}
                                        {{ product.dimensions_length }} x {{ product.dimensions_width }} x {{ product.dimensions_height }} cm
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                    {% if product.is_featured %}
                                        <span class="badge bg-warning ms-1">Featured</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if product.description %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ product.description }}</p>
                    </div>
                </div>
                {% endif %}

                {% if product.barcode %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Barcode:</h6>
                        <p><code>{{ product.barcode }}</code></p>
                        {% if product.barcode_image %}
                            <img src="{{ product.barcode_image.url }}" alt="Barcode" class="img-fluid" style="max-height: 60px;">
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Stock Information -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Stock Information</h5>
            </div>
            <div class="card-body">
                {% if product.track_inventory %}
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="{% if total_stock <= product.min_stock_level %}text-danger{% else %}text-success{% endif %}">
                                    {{ total_stock }}
                                </h4>
                                <p class="text-muted">Total Stock</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">{{ total_reserved }}</h4>
                                <p class="text-muted">Reserved</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ available_stock }}</h4>
                                <p class="text-muted">Available</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-secondary">{{ product.min_stock_level }}</h4>
                                <p class="text-muted">Min Level</p>
                            </div>
                        </div>
                    </div>

                    {% if stock_records %}
                    <div class="table-responsive mt-3">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Warehouse</th>
                                    <th>Location</th>
                                    <th>Quantity</th>
                                    <th>Reserved</th>
                                    <th>Available</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock in stock_records %}
                                    <tr>
                                        <td>{{ stock.warehouse.name }}</td>
                                        <td>{{ stock.location.name|default:"No Location" }}</td>
                                        <td>{{ stock.quantity }}</td>
                                        <td>{{ stock.reserved_quantity }}</td>
                                        <td>{{ stock.available_quantity }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Inventory tracking is disabled for this product.
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Product Variants -->
        {% if variants %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Product Variants</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>SKU</th>
                                <th>Color</th>
                                <th>Size</th>
                                <th>Price</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for variant in variants %}
                                <tr>
                                    <td>{{ variant.name }}</td>
                                    <td><code>{{ variant.sku }}</code></td>
                                    <td>{{ variant.color|default:"-" }}</td>
                                    <td>{{ variant.size|default:"-" }}</td>
                                    <td>${{ variant.effective_selling_price|floatformat:2 }}</td>
                                    <td>
                                        {% if variant.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Recent Stock Movements -->
        {% if recent_movements %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Stock Movements</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Quantity</th>
                                <th>Warehouse</th>
                                <th>Reference</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in recent_movements %}
                                <tr>
                                    <td>{{ movement.created_at|date:"M d, Y H:i" }}</td>
                                    <td>
                                        <span class="badge bg-{% if movement.movement_type == 'in' %}success{% elif movement.movement_type == 'out' %}danger{% else %}info{% endif %}">
                                            {{ movement.get_movement_type_display }}
                                        </span>
                                    </td>
                                    <td>{{ movement.quantity }}</td>
                                    <td>
                                        {% if movement.movement_type == 'in' %}
                                            {{ movement.to_warehouse.name }}
                                        {% else %}
                                            {{ movement.from_warehouse.name }}
                                        {% endif %}
                                    </td>
                                    <td><code>{{ movement.reference }}</code></td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Product Image and Actions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Product Image</h5>
            </div>
            <div class="card-body text-center">
                {% if product.image %}
                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded mb-3" style="max-height: 300px;">
                {% else %}
                    <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" style="height: 200px;">
                        <i class="fas fa-image fa-4x text-muted"></i>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Additional Images -->
        {% if images %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Additional Images</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for image in images %}
                        <div class="col-6 mb-2">
                            <img src="{{ image.image.url }}" alt="{{ image.alt_text }}" class="img-fluid rounded">
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Quick Actions -->
        {% if user.can_manage_inventory %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'products:edit' product.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Edit Product
                    </a>
                    <a href="{% url 'products:variants' product.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-layer-group"></i> Manage Variants
                    </a>
                    <a href="{% url 'products:generate_barcode' product.pk %}" class="btn btn-outline-info">
                        <i class="fas fa-barcode"></i> Generate Barcode
                    </a>
                    <a href="{% url 'inventory:quick_stock_in' %}?product={{ product.pk }}" class="btn btn-outline-success">
                        <i class="fas fa-plus-circle"></i> Add Stock
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Product Statistics -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Statistics</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td>Created:</td>
                        <td>{{ product.created_at|date:"M d, Y" }}</td>
                    </tr>
                    <tr>
                        <td>Updated:</td>
                        <td>{{ product.updated_at|date:"M d, Y" }}</td>
                    </tr>
                    <tr>
                        <td>Created By:</td>
                        <td>{{ product.created_by.get_full_name|default:product.created_by.username|default:"-" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

{% endblock %}
