{% extends 'base.html' %}

{% block title %}Warehouses - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Warehouse Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% include 'components/export_buttons.html' with export_url=request.get_full_path page_title='Warehouses Report' %}
        {% if user.is_manager %}
        <a href="{% url 'warehouses:create' %}" class="btn btn-primary ms-2">
            <i class="fas fa-plus"></i> Add Warehouse
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-md-12">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <input type="text" name="search" class="form-control" placeholder="Search warehouses..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <select name="type" class="form-control">
                    <option value="">All Types</option>
                    {% for value, label in warehouse_types %}
                        <option value="{{ value }}" {% if request.GET.type == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-secondary">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Warehouses Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Warehouses ({{ warehouses|length }})</h5>
    </div>
    <div class="card-body">
        {% if warehouses %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Code</th>
                            <th>Type</th>
                            <th>Location</th>
                            <th>Manager</th>
                            <th>Products</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for warehouse in warehouses %}
                            <tr>
                                <td>
                                    <strong>{{ warehouse.name }}</strong>
                                    {% if warehouse.is_default %}
                                        <span class="badge bg-primary ms-1">Default</span>
                                    {% endif %}
                                </td>
                                <td><code>{{ warehouse.code }}</code></td>
                                <td>{{ warehouse.get_warehouse_type_display }}</td>
                                <td>{{ warehouse.city }}, {{ warehouse.state }}</td>
                                <td>{{ warehouse.manager.get_full_name|default:warehouse.manager.username|default:"-" }}</td>
                                <td>{{ warehouse.total_products|default:0 }}</td>
                                <td>
                                    {% if warehouse.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'warehouses:detail' warehouse.pk %}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if user.is_manager %}
                                            <a href="{% url 'warehouses:edit' warehouse.pk %}" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No warehouses found</h5>
                <p class="text-muted">Create your first warehouse to start managing inventory.</p>
                {% if user.is_manager %}
                    <a href="{% url 'warehouses:create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Warehouse
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}
