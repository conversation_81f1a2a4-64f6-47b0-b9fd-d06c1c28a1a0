/* Cross-Browser Compatibility Styles */

/* CSS Reset for consistent rendering across browsers */
* {
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* Fix for Internet Explorer */
html {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: left;
    background-color: #fff;
}

/* Flexbox fixes for older browsers */
.d-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
}

.flex-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
}

.flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important;
}

.justify-content-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
}

.justify-content-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
}

.align-items-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
}

.flex-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
}

/* Grid fixes for older browsers */
.row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Button fixes */
.btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Form control fixes */
.form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Internet Explorer specific fixes */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .card {
        display: block;
    }
    
    .d-flex {
        display: block;
    }
    
    .btn-group {
        display: inline-block;
    }
}

/* Safari specific fixes */
@supports (-webkit-appearance: none) {
    .form-control {
        -webkit-appearance: none;
    }
    
    select.form-control {
        -webkit-appearance: menulist;
    }
}

/* Firefox specific fixes */
@-moz-document url-prefix() {
    .form-control {
        -moz-appearance: none;
    }
    
    select.form-control {
        -moz-appearance: menulist;
    }
}

/* Edge specific fixes */
@supports (-ms-ime-align: auto) {
    .card {
        display: block;
    }
}

/* Older browser fallbacks */
.no-flexbox .d-flex {
    display: block;
}

.no-flexbox .justify-content-between > * {
    float: left;
}

.no-flexbox .justify-content-between > *:last-child {
    float: right;
}

/* CSS Grid fallbacks */
.no-cssgrid .row {
    display: block;
}

.no-cssgrid .col-md-6 {
    width: 50%;
    float: left;
}

.no-cssgrid .col-md-4 {
    width: 33.333%;
    float: left;
}

.no-cssgrid .col-md-3 {
    width: 25%;
    float: left;
}

/* Transform fixes */
.transform-fix {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
}

/* Transition fixes */
.transition-fix {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

/* Border radius fixes */
.border-radius-fix {
    -webkit-border-radius: 0.25rem;
    -moz-border-radius: 0.25rem;
    border-radius: 0.25rem;
}

/* Box shadow fixes */
.box-shadow-fix {
    -webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    -moz-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Opacity fixes for IE8 */
.opacity-fix {
    opacity: 0.5;
    filter: alpha(opacity=50);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
}

/* Clearfix for older browsers */
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* Print compatibility */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid;
    }
    
    .form-control {
        border: 2px solid;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .auto-dark {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .auto-dark .form-control {
        background-color: #2d2d2d;
        color: #ffffff;
        border-color: #404040;
    }
}

/* Mobile browser fixes */
@media screen and (max-width: 768px) {
    /* iOS Safari fixes */
    .ios-fix {
        -webkit-overflow-scrolling: touch;
    }
    
    /* Android browser fixes */
    .android-fix {
        -webkit-text-size-adjust: 100%;
    }
}

/* Vendor prefix utilities */
.user-select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.pointer-events-none {
    pointer-events: none;
}

.pointer-events-auto {
    pointer-events: auto;
}

/* Focus visible polyfill */
.focus-visible-polyfill:focus:not(.focus-visible) {
    outline: none;
}

.focus-visible-polyfill.focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Sticky positioning fallback */
.sticky-fallback {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
}

/* Legacy browser support message */
.browser-warning {
    display: none;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0.25rem;
}

.legacy-browser .browser-warning {
    display: block;
}

/* Accessibility improvements */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Skip to content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 9999;
}

.skip-link:focus {
    top: 6px;
}
