# Generated by Django 5.2.4 on 2025-07-17 17:12

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('warehouses', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='warehouse',
            name='code',
            field=models.CharField(editable=False, max_length=10, unique=True, validators=[django.core.validators.RegexValidator('^[A-Z0-9]+$', 'Code must contain only uppercase letters and numbers')]),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='country',
            field=models.CharField(default='US', help_text='ISO 3166-1 alpha-2 country code', max_length=2),
        ),
        migrations.AlterField(
            model_name='warehouse',
            name='phone',
            field=models.CharField(blank=True, help_text='Phone number with country code', max_length=20, null=True),
        ),
    ]
