{% extends 'base.html' %}

{% block title %}Reports & Analytics - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Reports & Analytics</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> Export Reports
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#"><i class="fas fa-file-excel"></i> Export to Excel</a></li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-file-pdf"></i> Export to PDF</a></li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-file-csv"></i> Export to CSV</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label class="form-label">From Date</label>
                        <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">To Date</label>
                        <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Report Type</label>
                        <select name="report_type" class="form-control">
                            <option value="overview" {% if request.GET.report_type == 'overview' %}selected{% endif %}>Overview</option>
                            <option value="inventory" {% if request.GET.report_type == 'inventory' %}selected{% endif %}>Inventory</option>
                            <option value="sales" {% if request.GET.report_type == 'sales' %}selected{% endif %}>Sales</option>
                            <option value="financial" {% if request.GET.report_type == 'financial' %}selected{% endif %}>Financial</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-chart-bar"></i> Generate Report
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Revenue</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ total_revenue|floatformat:2 }}</div>
                        <div class="text-xs text-success">
                            <i class="fas fa-arrow-up"></i> {{ revenue_growth|floatformat:1 }}% from last period
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Orders</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_orders }}</div>
                        <div class="text-xs text-success">
                            <i class="fas fa-arrow-up"></i> {{ order_growth|floatformat:1 }}% from last period
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Inventory Value</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ inventory_value|floatformat:2 }}</div>
                        <div class="text-xs text-info">
                            <i class="fas fa-boxes"></i> {{ total_products }} products
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-boxes fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Low Stock Items</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_count }}</div>
                        <div class="text-xs text-warning">
                            <i class="fas fa-exclamation-triangle"></i> Requires attention
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Revenue Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Revenue Overview</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#">Daily</a>
                        <a class="dropdown-item" href="#">Weekly</a>
                        <a class="dropdown-item" href="#">Monthly</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="revenueChart" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Categories Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Sales by Category</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="categoryChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    {% for category in top_categories %}
                        <span class="mr-2">
                            <i class="fas fa-circle" style="color: {{ category.color }}"></i> {{ category.name }}
                        </span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Reports -->
<div class="row">
    <!-- Top Products -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Top Selling Products</h6>
            </div>
            <div class="card-body">
                {% if top_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th class="text-center">Sold</th>
                                    <th class="text-end">Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if product.image %}
                                                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                {% else %}
                                                    <div class="bg-light me-2 d-flex align-items-center justify-content-center" style="width: 30px; height: 30px;">
                                                        <i class="fas fa-image text-muted small"></i>
                                                    </div>
                                                {% endif %}
                                                <div>
                                                    <div class="font-weight-bold">{{ product.name|truncatechars:25 }}</div>
                                                    <small class="text-muted">{{ product.sku }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">{{ product.total_sold }}</td>
                                        <td class="text-end">${{ product.total_revenue|floatformat:2 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No sales data available</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Transactions</h6>
            </div>
            <div class="card-body">
                {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th class="text-end">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                    <tr>
                                        <td>{{ transaction.transaction_date|date:"M d" }}</td>
                                        <td>
                                            <span class="badge bg-{% if transaction.transaction_type == 'income' %}success{% else %}danger{% endif %} small">
                                                {{ transaction.get_transaction_type_display }}
                                            </span>
                                        </td>
                                        <td class="text-end">
                                            <span class="{% if transaction.transaction_type == 'income' %}text-success{% else %}text-danger{% endif %}">
                                                {% if transaction.transaction_type == 'expense' %}-{% endif %}${{ transaction.amount|floatformat:2 }}
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No transactions available</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Stock Alerts -->
{% if low_stock_products %}
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 bg-warning">
                <h6 class="m-0 font-weight-bold text-white">
                    <i class="fas fa-exclamation-triangle"></i> Low Stock Alerts
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th class="text-center">Current Stock</th>
                                <th class="text-center">Min Level</th>
                                <th class="text-center">Warehouse</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in low_stock_products %}
                                <tr>
                                    <td>
                                        <strong>{{ item.product.name }}</strong>
                                        <br><small class="text-muted">{{ item.product.sku }}</small>
                                    </td>
                                    <td class="text-center text-danger">{{ item.current_stock }}</td>
                                    <td class="text-center">{{ item.min_level }}</td>
                                    <td class="text-center">{{ item.warehouse.name }}</td>
                                    <td>
                                        <a href="{% url 'inventory:quick_stock_in' %}?product={{ item.product.pk }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-plus"></i> Restock
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: {{ revenue_labels|safe }},
        datasets: [{
            label: 'Revenue',
            data: {{ revenue_data|safe }},
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Category Chart
const categoryCtx = document.getElementById('categoryChart').getContext('2d');
const categoryChart = new Chart(categoryCtx, {
    type: 'doughnut',
    data: {
        labels: {{ category_labels|safe }},
        datasets: [{
            data: {{ category_data|safe }},
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

{% endblock %}
