#!/usr/bin/env python
"""
Verify that all business pages are clean of status messages
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.test import Client
from users.models import User

def test_business_pages_clean():
    """Test that main business pages are clean of status messages"""
    print("🧹 Testing Business Pages for Clean Interface...\n")
    
    try:
        # Create test client
        client = Client()
        
        # Get admin user
        user = User.objects.filter(is_active=True, role='admin').first()
        if not user:
            print("❌ No admin user found for testing")
            return False
        
        # Login the user
        client.force_login(user)
        print(f"✅ Logged in as: {user.username}")
        
        # Define business pages to test
        business_pages = [
            ('/dashboard/', 'Dashboard'),
            ('/products/', 'Products'),
            ('/warehouses/list/', 'Warehouses'),
            ('/sales/orders/', 'Sales Orders'),
            ('/sales/customers/', 'Customers'),
            ('/dashboard/reports/', 'Reports & Analytics'),
        ]
        
        # Status message patterns to check for (should NOT be present)
        status_patterns = [
            'Products Page is Working',
            'Warehouses Page is Working',
            'Sales Orders Page is NOW WORKING',
            'Customer Management is NOW WORKING',
            'Dashboard Loaded Successfully',
            'Template fixed and working',
            'Template recursion issue',
            '100% functional',
            'System Status.*✅',
            'Features.*✅.*available',
            'Status.*✅.*working',
        ]
        
        print("🔍 Testing Business Pages:")
        all_clean = True
        
        for url, page_name in business_pages:
            try:
                response = client.get(url)
                
                if response.status_code == 200:
                    content = response.content.decode('utf-8')
                    
                    # Check for status message patterns
                    found_patterns = []
                    for pattern in status_patterns:
                        if pattern.lower() in content.lower():
                            found_patterns.append(pattern)
                    
                    if found_patterns:
                        print(f"   ❌ {page_name}: Still has status messages")
                        for pattern in found_patterns:
                            print(f"      - Found: '{pattern}'")
                        all_clean = False
                    else:
                        print(f"   ✅ {page_name}: Clean (no status messages)")
                        
                elif response.status_code == 302:
                    print(f"   🔄 {page_name}: Redirected (likely to login)")
                else:
                    print(f"   ❌ {page_name}: Error {response.status_code}")
                    all_clean = False
                    
            except Exception as e:
                print(f"   ❌ {page_name}: Exception - {str(e)}")
                all_clean = False
        
        # Check for functional content presence
        print(f"\n📊 Checking Functional Content:")
        
        functional_checks = [
            ('/dashboard/', ['Analytics', 'Products', 'Warehouses']),
            ('/products/', ['Product Management', 'Add Product', 'Categories']),
            ('/warehouses/list/', ['Warehouse Management', 'Add Warehouse']),
            ('/sales/orders/', ['Sales Orders', 'Add Order']),
            ('/sales/customers/', ['Customer Management', 'Add Customer']),
        ]
        
        functional_ok = True
        for url, expected_elements in functional_checks:
            try:
                response = client.get(url)
                if response.status_code == 200:
                    content = response.content.decode('utf-8')
                    
                    missing_elements = []
                    for element in expected_elements:
                        if element.lower() not in content.lower():
                            missing_elements.append(element)
                    
                    page_name = url.split('/')[-2] or 'dashboard'
                    if missing_elements:
                        print(f"   ⚠️  {page_name}: Missing elements: {missing_elements}")
                        functional_ok = False
                    else:
                        print(f"   ✅ {page_name}: All functional elements present")
                        
            except Exception as e:
                print(f"   ❌ Error checking functional content: {e}")
                functional_ok = False
        
        return all_clean and functional_ok
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Business Pages Clean Interface Verification\n")
    
    success = test_business_pages_clean()
    
    print(f"\n📋 Final Results:")
    if success:
        print(f"🎉 SUCCESS: All business pages are clean!")
        print(f"✅ No status messages found on any business page")
        print(f"✅ All functional content is present")
        print(f"✅ Professional appearance across all pages")
        print(f"✅ Ready for business use")
        print(f"\n🌐 Your clean business pages:")
        print(f"   📊 Dashboard: http://127.0.0.1:8000/dashboard/")
        print(f"   📦 Products: http://127.0.0.1:8000/products/")
        print(f"   🏢 Warehouses: http://127.0.0.1:8000/warehouses/list/")
        print(f"   🛒 Sales Orders: http://127.0.0.1:8000/sales/orders/")
        print(f"   👥 Customers: http://127.0.0.1:8000/sales/customers/")
        print(f"   📈 Analytics: http://127.0.0.1:8000/dashboard/reports/")
    else:
        print(f"⚠️  Some pages still need cleanup")
        print(f"🔧 Review the issues listed above")
