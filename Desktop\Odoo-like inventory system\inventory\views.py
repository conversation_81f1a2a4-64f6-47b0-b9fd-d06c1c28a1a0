from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, Sum, Count, F
from django.http import JsonResponse
from django.utils import timezone
from datetime import datetime, timedelta
from users.views import inventory_access_required
from .models import StockMovement, StockAdjustment, StockAdjustmentLine
from .forms import (
    StockMovementForm, StockAdjustmentForm, StockAdjustmentLineForm,
    StockTransferForm, QuickStockInForm, QuickStockOutForm
)
from warehouses.models import Warehouse, Location, Stock
from products.models import Product


@login_required
def dashboard(request):
    """Main inventory dashboard"""
    # Get date range for filtering
    days = int(request.GET.get('days', 30))
    start_date = timezone.now() - timedelta(days=days)
    
    # Key metrics
    total_products = Product.objects.filter(is_active=True).count()
    total_warehouses = Warehouse.objects.filter(is_active=True).count()
    
    # Stock value calculation
    total_stock_value = 0
    for stock in Stock.objects.select_related('product').filter(quantity__gt=0):
        total_stock_value += stock.stock_value
    
    # Low stock alerts
    low_stock_items = Stock.objects.filter(
        quantity__lte=F('product__min_stock_level'),
        quantity__gt=0
    ).select_related('product', 'warehouse')[:10]
    
    # Recent movements
    recent_movements = StockMovement.objects.filter(
        created_at__gte=start_date
    ).select_related('product', 'from_warehouse', 'to_warehouse').order_by('-created_at')[:10]
    
    # Movement statistics
    movements_in = StockMovement.objects.filter(
        movement_type='in',
        created_at__gte=start_date
    ).aggregate(total=Sum('quantity'))['total'] or 0
    
    movements_out = StockMovement.objects.filter(
        movement_type='out',
        created_at__gte=start_date
    ).aggregate(total=Sum('quantity'))['total'] or 0
    
    # Top moving products
    top_products = StockMovement.objects.filter(
        created_at__gte=start_date
    ).values('product__name', 'product__sku').annotate(
        total_movements=Sum('quantity')
    ).order_by('-total_movements')[:5]
    
    # Pending adjustments
    pending_adjustments = StockAdjustment.objects.filter(
        is_approved=False
    ).count()
    
    context = {
        'total_products': total_products,
        'total_warehouses': total_warehouses,
        'total_stock_value': total_stock_value,
        'low_stock_items': low_stock_items,
        'recent_movements': recent_movements,
        'movements_in': movements_in,
        'movements_out': movements_out,
        'top_products': top_products,
        'pending_adjustments': pending_adjustments,
        'days': days,
    }
    return render(request, 'inventory/dashboard.html', context)


class StockMovementListView(LoginRequiredMixin, ListView):
    """List all stock movements"""
    model = StockMovement
    template_name = 'inventory/movement_list.html'
    context_object_name = 'movements'
    paginate_by = 50
    
    def get_queryset(self):
        queryset = StockMovement.objects.select_related(
            'product', 'from_warehouse', 'to_warehouse', 'created_by'
        )
        
        # Filter by movement type
        movement_type = self.request.GET.get('type')
        if movement_type:
            queryset = queryset.filter(movement_type=movement_type)
        
        # Filter by warehouse
        warehouse = self.request.GET.get('warehouse')
        if warehouse:
            queryset = queryset.filter(
                Q(from_warehouse_id=warehouse) | Q(to_warehouse_id=warehouse)
            )
        
        # Filter by product
        product = self.request.GET.get('product')
        if product:
            queryset = queryset.filter(product_id=product)
        
        # Filter by date range
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status == 'processed':
            queryset = queryset.filter(is_processed=True)
        elif status == 'pending':
            queryset = queryset.filter(is_processed=False)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['movement_types'] = StockMovement.MOVEMENT_TYPES
        context['warehouses'] = Warehouse.objects.filter(is_active=True)
        context['products'] = Product.objects.filter(is_active=True)[:100]  # Limit for performance
        return context


class StockMovementDetailView(LoginRequiredMixin, DetailView):
    """Stock movement detail view"""
    model = StockMovement
    template_name = 'inventory/movement_detail.html'
    context_object_name = 'movement'


@login_required
@inventory_access_required
def stock_movement_create(request):
    """Create new stock movement"""
    if request.method == 'POST':
        form = StockMovementForm(request.POST)
        if form.is_valid():
            movement = form.save(commit=False)
            movement.created_by = request.user
            movement.save()
            
            # Process movement if requested
            if request.POST.get('process_immediately'):
                try:
                    movement.process_movement()
                    messages.success(request, f'Stock movement {movement.reference} created and processed successfully.')
                except Exception as e:
                    messages.error(request, f'Movement created but processing failed: {str(e)}')
            else:
                messages.success(request, f'Stock movement {movement.reference} created successfully.')
            
            return redirect('inventory:movement_detail', pk=movement.pk)
    else:
        form = StockMovementForm()
    
    return render(request, 'inventory/movement_form.html', {'form': form})


@login_required
@inventory_access_required
def process_movement(request, pk):
    """Process a pending stock movement"""
    movement = get_object_or_404(StockMovement, pk=pk)
    
    if movement.is_processed:
        messages.warning(request, 'This movement has already been processed.')
        return redirect('inventory:movement_detail', pk=movement.pk)
    
    try:
        movement.process_movement()
        messages.success(request, f'Movement {movement.reference} processed successfully.')
    except Exception as e:
        messages.error(request, f'Error processing movement: {str(e)}')
    
    return redirect('inventory:movement_detail', pk=movement.pk)


# Quick Stock Operations
@login_required
@inventory_access_required
def quick_stock_in(request):
    """Quick stock in form"""
    if request.method == 'POST':
        form = QuickStockInForm(request.POST)
        if form.is_valid():
            # Create stock movement
            movement = StockMovement.objects.create(
                product=form.cleaned_data['product'],
                product_variant=form.cleaned_data.get('product_variant'),
                movement_type='in',
                reason='purchase',
                quantity=form.cleaned_data['quantity'],
                to_warehouse=form.cleaned_data['warehouse'],
                to_location=form.cleaned_data.get('location'),
                unit_cost=form.cleaned_data.get('unit_cost'),
                notes=form.cleaned_data.get('notes'),
                created_by=request.user
            )
            
            # Process immediately
            try:
                movement.process_movement()
                messages.success(request, f'Stock added successfully. Reference: {movement.reference}')
            except Exception as e:
                messages.error(request, f'Error adding stock: {str(e)}')
            
            return redirect('inventory:quick_stock_in')
    else:
        form = QuickStockInForm()
    
    return render(request, 'inventory/quick_stock_in.html', {'form': form})


@login_required
@inventory_access_required
def quick_stock_out(request):
    """Quick stock out form"""
    if request.method == 'POST':
        form = QuickStockOutForm(request.POST)
        if form.is_valid():
            # Create stock movement
            movement = StockMovement.objects.create(
                product=form.cleaned_data['product'],
                product_variant=form.cleaned_data.get('product_variant'),
                movement_type='out',
                reason=form.cleaned_data['reason'],
                quantity=form.cleaned_data['quantity'],
                from_warehouse=form.cleaned_data['warehouse'],
                from_location=form.cleaned_data.get('location'),
                notes=form.cleaned_data.get('notes'),
                created_by=request.user
            )
            
            # Process immediately
            try:
                movement.process_movement()
                messages.success(request, f'Stock removed successfully. Reference: {movement.reference}')
            except Exception as e:
                messages.error(request, f'Error removing stock: {str(e)}')
            
            return redirect('inventory:quick_stock_out')
    else:
        form = QuickStockOutForm()
    
    return render(request, 'inventory/quick_stock_out.html', {'form': form})


# Stock Transfer
@login_required
@inventory_access_required
def stock_transfer(request):
    """Transfer stock between warehouses/locations"""
    if request.method == 'POST':
        form = StockTransferForm(request.POST)
        if form.is_valid():
            # Create transfer movement
            movement = StockMovement.objects.create(
                product=form.cleaned_data['product'],
                product_variant=form.cleaned_data.get('product_variant'),
                movement_type='transfer',
                reason='transfer',
                quantity=form.cleaned_data['quantity'],
                from_warehouse=form.cleaned_data['from_warehouse'],
                from_location=form.cleaned_data.get('from_location'),
                to_warehouse=form.cleaned_data['to_warehouse'],
                to_location=form.cleaned_data.get('to_location'),
                notes=form.cleaned_data.get('notes'),
                created_by=request.user
            )
            
            # Process immediately
            try:
                movement.process_movement()
                messages.success(request, f'Stock transferred successfully. Reference: {movement.reference}')
            except Exception as e:
                messages.error(request, f'Error transferring stock: {str(e)}')
            
            return redirect('inventory:stock_transfer')
    else:
        form = StockTransferForm()
    
    return render(request, 'inventory/stock_transfer.html', {'form': form})


# Stock Adjustments
class StockAdjustmentListView(LoginRequiredMixin, ListView):
    """List all stock adjustments"""
    model = StockAdjustment
    template_name = 'inventory/adjustment_list.html'
    context_object_name = 'adjustments'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = StockAdjustment.objects.select_related('warehouse', 'created_by', 'approved_by')
        
        # Filter by warehouse
        warehouse = self.request.GET.get('warehouse')
        if warehouse:
            queryset = queryset.filter(warehouse_id=warehouse)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status == 'approved':
            queryset = queryset.filter(is_approved=True)
        elif status == 'pending':
            queryset = queryset.filter(is_approved=False)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['warehouses'] = Warehouse.objects.filter(is_active=True)
        return context


class StockAdjustmentDetailView(LoginRequiredMixin, DetailView):
    """Stock adjustment detail view"""
    model = StockAdjustment
    template_name = 'inventory/adjustment_detail.html'
    context_object_name = 'adjustment'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['lines'] = self.object.lines.select_related('product', 'location')
        return context


@login_required
@inventory_access_required
def stock_adjustment_create(request):
    """Create new stock adjustment"""
    if request.method == 'POST':
        form = StockAdjustmentForm(request.POST)
        if form.is_valid():
            adjustment = form.save(commit=False)
            adjustment.created_by = request.user
            adjustment.save()
            messages.success(request, f'Stock adjustment {adjustment.reference} created successfully.')
            return redirect('inventory:adjustment_detail', pk=adjustment.pk)
    else:
        form = StockAdjustmentForm()
    
    return render(request, 'inventory/adjustment_form.html', {'form': form})


@login_required
@inventory_access_required
def approve_adjustment(request, pk):
    """Approve stock adjustment"""
    adjustment = get_object_or_404(StockAdjustment, pk=pk)
    
    if adjustment.is_approved:
        messages.warning(request, 'This adjustment has already been approved.')
        return redirect('inventory:adjustment_detail', pk=adjustment.pk)
    
    if not request.user.is_manager:
        messages.error(request, 'Only managers can approve stock adjustments.')
        return redirect('inventory:adjustment_detail', pk=adjustment.pk)
    
    # Process adjustment lines
    for line in adjustment.lines.all():
        if line.difference != 0:
            # Create stock movement for each line
            movement = StockMovement.objects.create(
                product=line.product,
                product_variant=line.product_variant,
                movement_type='adjustment',
                reason='adjustment',
                quantity=abs(line.difference),
                to_warehouse=adjustment.warehouse if line.difference > 0 else None,
                from_warehouse=adjustment.warehouse if line.difference < 0 else None,
                to_location=line.location if line.difference > 0 else None,
                from_location=line.location if line.difference < 0 else None,
                unit_cost=line.unit_cost,
                notes=f"Adjustment: {adjustment.reference}",
                created_by=request.user
            )
            movement.process_movement()
    
    # Mark as approved
    adjustment.is_approved = True
    adjustment.approved_by = request.user
    adjustment.approved_at = timezone.now()
    adjustment.save()
    
    messages.success(request, f'Stock adjustment {adjustment.reference} approved and processed.')
    return redirect('inventory:adjustment_detail', pk=adjustment.pk)


# Reports and Analytics
@login_required
def stock_report(request):
    """Stock level report"""
    warehouses = Warehouse.objects.filter(is_active=True)
    
    # Get stock data
    stock_data = []
    for warehouse in warehouses:
        stock_records = Stock.objects.filter(
            warehouse=warehouse,
            quantity__gt=0
        ).select_related('product')
        
        total_products = stock_records.count()
        total_value = sum(stock.stock_value for stock in stock_records)
        low_stock_count = stock_records.filter(
            quantity__lte=F('product__min_stock_level')
        ).count()
        
        stock_data.append({
            'warehouse': warehouse,
            'total_products': total_products,
            'total_value': total_value,
            'low_stock_count': low_stock_count,
        })
    
    context = {
        'stock_data': stock_data,
        'total_warehouses': warehouses.count(),
    }
    return render(request, 'inventory/stock_report.html', context)


# AJAX Views
@login_required
def get_product_stock_ajax(request):
    """Get stock levels for a product"""
    product_id = request.GET.get('product_id')
    warehouse_id = request.GET.get('warehouse_id')
    
    if not product_id:
        return JsonResponse({'error': 'Product ID required'})
    
    stock_records = Stock.objects.filter(product_id=product_id)
    if warehouse_id:
        stock_records = stock_records.filter(warehouse_id=warehouse_id)
    
    stock_data = []
    for stock in stock_records.select_related('warehouse', 'location'):
        stock_data.append({
            'warehouse': stock.warehouse.name,
            'location': stock.location.name if stock.location else 'No Location',
            'quantity': stock.quantity,
            'reserved': stock.reserved_quantity,
            'available': stock.available_quantity,
        })
    
    return JsonResponse({'stock_data': stock_data})


@login_required
def movement_stats_ajax(request):
    """Get movement statistics for dashboard charts"""
    days = int(request.GET.get('days', 30))
    start_date = timezone.now() - timedelta(days=days)
    
    # Daily movement data
    daily_data = []
    for i in range(days):
        date = start_date + timedelta(days=i)
        movements_in = StockMovement.objects.filter(
            movement_type='in',
            created_at__date=date.date()
        ).aggregate(total=Sum('quantity'))['total'] or 0
        
        movements_out = StockMovement.objects.filter(
            movement_type='out',
            created_at__date=date.date()
        ).aggregate(total=Sum('quantity'))['total'] or 0
        
        daily_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'in': movements_in,
            'out': movements_out,
        })
    
    return JsonResponse({'daily_data': daily_data})
