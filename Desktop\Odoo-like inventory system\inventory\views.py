from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, Sum, Count, F
from django.http import JsonResponse
from django.utils import timezone
from datetime import datetime, timedelta
from users.views import inventory_access_required
from .models import StockMovement, StockAdjustment, StockAdjustmentLine
from .forms import (
    StockMovementForm, StockAdjustmentForm, StockAdjustmentLineForm,
    StockTransferForm, QuickStockInForm, QuickStockOutForm
)
from warehouses.models import Warehouse, Location, Stock
from products.models import Product
from utils.exports import export_queryset_to_pdf, export_queryset_to_excel, export_queryset_to_csv


@login_required
def dashboard(request):
    """Main inventory dashboard - Optimized for fast loading"""
    try:
        # Get date range for filtering
        days = int(request.GET.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)

        # Key metrics - Simple counts first
        total_products = Product.objects.filter(is_active=True).count()
        total_warehouses = Warehouse.objects.filter(is_active=True).count()

        # Optimized stock value calculation using database aggregation
        from django.db.models import Sum, Case, When, DecimalField

        # Calculate total stock value more efficiently
        stock_value_query = Stock.objects.filter(quantity__gt=0).aggregate(
            total_value=Sum(
                Case(
                    When(product_variant__isnull=False,
                         then=F('quantity') * F('product_variant__cost_price')),
                    default=F('quantity') * F('product__cost_price'),
                    output_field=DecimalField(max_digits=12, decimal_places=2)
                )
            )
        )
        total_stock_value = stock_value_query['total_value'] or 0

        # Low stock alerts - Limit and optimize
        low_stock_items = Stock.objects.filter(
            quantity__lte=F('product__min_stock_level'),
            quantity__gt=0
        ).select_related('product', 'warehouse').only(
            'quantity', 'product__name', 'product__sku', 'product__min_stock_level',
            'warehouse__name'
        )[:5]  # Reduced from 10 to 5 for faster loading

        # Recent movements - Optimize with only necessary fields
        recent_movements = StockMovement.objects.filter(
            created_at__gte=start_date
        ).select_related('product', 'from_warehouse', 'to_warehouse').only(
            'movement_type', 'quantity', 'created_at', 'reason',
            'product__name', 'product__sku',
            'from_warehouse__name', 'to_warehouse__name'
        ).order_by('-created_at')[:5]  # Reduced from 10 to 5

        # Movement statistics - Keep as is (efficient aggregation)
        movements_in = StockMovement.objects.filter(
            movement_type='in',
            created_at__gte=start_date
        ).aggregate(total=Sum('quantity'))['total'] or 0

        movements_out = StockMovement.objects.filter(
            movement_type='out',
            created_at__gte=start_date
        ).aggregate(total=Sum('quantity'))['total'] or 0

        # Top moving products - Optimize
        top_products = StockMovement.objects.filter(
            created_at__gte=start_date
        ).values('product__name', 'product__sku').annotate(
            total_movements=Sum('quantity')
        ).order_by('-total_movements')[:3]  # Reduced from 5 to 3

        # Pending adjustments - Simple count
        pending_adjustments = StockAdjustment.objects.filter(
            is_approved=False
        ).count()

        # Additional quick stats for better dashboard
        total_stock_items = Stock.objects.filter(quantity__gt=0).count()
        low_stock_count = Stock.objects.filter(
            quantity__lte=F('product__min_stock_level'),
            quantity__gt=0
        ).count()

        context = {
            'total_products': total_products,
            'total_warehouses': total_warehouses,
            'total_stock_value': total_stock_value,
            'total_stock_items': total_stock_items,
            'low_stock_items': low_stock_items,
            'low_stock_count': low_stock_count,
            'recent_movements': recent_movements,
            'movements_in': movements_in,
            'movements_out': movements_out,
            'top_products': top_products,
            'pending_adjustments': pending_adjustments,
            'days': days,
            'dashboard_loaded': True,  # Flag to show dashboard loaded successfully
        }

    except Exception as e:
        # Fallback context in case of database issues
        context = {
            'total_products': 0,
            'total_warehouses': 0,
            'total_stock_value': 0,
            'total_stock_items': 0,
            'low_stock_items': [],
            'low_stock_count': 0,
            'recent_movements': [],
            'movements_in': 0,
            'movements_out': 0,
            'top_products': [],
            'pending_adjustments': 0,
            'days': days,
            'dashboard_error': str(e),
            'dashboard_loaded': False,
        }

    return render(request, 'inventory/dashboard.html', context)


class StockMovementListView(LoginRequiredMixin, ListView):
    """List all stock movements"""
    model = StockMovement
    template_name = 'inventory/movement_list.html'
    context_object_name = 'movements'
    paginate_by = 50
    
    def get_queryset(self):
        queryset = StockMovement.objects.select_related(
            'product', 'from_warehouse', 'to_warehouse', 'created_by'
        )
        
        # Filter by movement type
        movement_type = self.request.GET.get('type')
        if movement_type:
            queryset = queryset.filter(movement_type=movement_type)
        
        # Filter by warehouse
        warehouse = self.request.GET.get('warehouse')
        if warehouse:
            queryset = queryset.filter(
                Q(from_warehouse_id=warehouse) | Q(to_warehouse_id=warehouse)
            )
        
        # Filter by product
        product = self.request.GET.get('product')
        if product:
            queryset = queryset.filter(product_id=product)
        
        # Filter by date range
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status == 'processed':
            queryset = queryset.filter(is_processed=True)
        elif status == 'pending':
            queryset = queryset.filter(is_processed=False)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['movement_types'] = StockMovement.MOVEMENT_TYPES
        context['warehouses'] = Warehouse.objects.filter(is_active=True)
        context['products'] = Product.objects.filter(is_active=True)[:100]  # Limit for performance
        return context

    def get(self, request, *args, **kwargs):
        # Handle export requests
        export_format = request.GET.get('format')
        if export_format in ['pdf', 'excel', 'csv']:
            queryset = self.get_queryset()
            fields = ['reference', 'product__name', 'movement_type', 'quantity', 'from_warehouse__name', 'to_warehouse__name', 'created_at', 'created_by__username']
            title = 'Stock Movements Report'

            if export_format == 'pdf':
                return export_queryset_to_pdf(queryset, fields, title)
            elif export_format == 'excel':
                return export_queryset_to_excel(queryset, fields, title)
            elif export_format == 'csv':
                return export_queryset_to_csv(queryset, fields, title)

        return super().get(request, *args, **kwargs)


class StockMovementDetailView(LoginRequiredMixin, DetailView):
    """Stock movement detail view"""
    model = StockMovement
    template_name = 'inventory/movement_detail.html'
    context_object_name = 'movement'


@login_required
@inventory_access_required
def stock_movement_create(request):
    """Create new stock movement"""
    if request.method == 'POST':
        form = StockMovementForm(request.POST)
        if form.is_valid():
            movement = form.save(commit=False)
            movement.created_by = request.user
            movement.save()
            
            # Process movement if requested
            if request.POST.get('process_immediately'):
                try:
                    movement.process_movement()
                    messages.success(request, f'Stock movement {movement.reference} created and processed successfully.')
                except Exception as e:
                    messages.error(request, f'Movement created but processing failed: {str(e)}')
            else:
                messages.success(request, f'Stock movement {movement.reference} created successfully.')
            
            return redirect('inventory:movement_detail', pk=movement.pk)
    else:
        form = StockMovementForm()
    
    return render(request, 'inventory/movement_form.html', {'form': form})


@login_required
@inventory_access_required
def process_movement(request, pk):
    """Process a pending stock movement"""
    movement = get_object_or_404(StockMovement, pk=pk)
    
    if movement.is_processed:
        messages.warning(request, 'This movement has already been processed.')
        return redirect('inventory:movement_detail', pk=movement.pk)
    
    try:
        movement.process_movement()
        messages.success(request, f'Movement {movement.reference} processed successfully.')
    except Exception as e:
        messages.error(request, f'Error processing movement: {str(e)}')
    
    return redirect('inventory:movement_detail', pk=movement.pk)


# Quick Stock Operations
@login_required
@inventory_access_required
def quick_stock_in(request):
    """Quick stock in form"""
    if request.method == 'POST':
        form = QuickStockInForm(request.POST)
        if form.is_valid():
            # Create stock movement
            movement = StockMovement.objects.create(
                product=form.cleaned_data['product'],
                product_variant=form.cleaned_data.get('product_variant'),
                movement_type='in',
                reason='purchase',
                quantity=form.cleaned_data['quantity'],
                to_warehouse=form.cleaned_data['warehouse'],
                to_location=form.cleaned_data.get('location'),
                unit_cost=form.cleaned_data.get('unit_cost'),
                notes=form.cleaned_data.get('notes'),
                created_by=request.user
            )
            
            # Process immediately
            try:
                movement.process_movement()
                messages.success(request, f'Stock added successfully. Reference: {movement.reference}')
            except Exception as e:
                messages.error(request, f'Error adding stock: {str(e)}')
            
            return redirect('inventory:quick_stock_in')
    else:
        form = QuickStockInForm()
    
    return render(request, 'inventory/quick_stock_in.html', {'form': form})


@login_required
@inventory_access_required
def quick_stock_out(request):
    """Quick stock out form"""
    if request.method == 'POST':
        form = QuickStockOutForm(request.POST)
        if form.is_valid():
            # Create stock movement
            movement = StockMovement.objects.create(
                product=form.cleaned_data['product'],
                product_variant=form.cleaned_data.get('product_variant'),
                movement_type='out',
                reason=form.cleaned_data['reason'],
                quantity=form.cleaned_data['quantity'],
                from_warehouse=form.cleaned_data['warehouse'],
                from_location=form.cleaned_data.get('location'),
                notes=form.cleaned_data.get('notes'),
                created_by=request.user
            )
            
            # Process immediately
            try:
                movement.process_movement()
                messages.success(request, f'Stock removed successfully. Reference: {movement.reference}')
            except Exception as e:
                messages.error(request, f'Error removing stock: {str(e)}')
            
            return redirect('inventory:quick_stock_out')
    else:
        form = QuickStockOutForm()
    
    return render(request, 'inventory/quick_stock_out.html', {'form': form})


# Stock Transfer
@login_required
@inventory_access_required
def stock_transfer(request):
    """Transfer stock between warehouses/locations"""
    if request.method == 'POST':
        form = StockTransferForm(request.POST)
        if form.is_valid():
            # Create transfer movement
            movement = StockMovement.objects.create(
                product=form.cleaned_data['product'],
                product_variant=form.cleaned_data.get('product_variant'),
                movement_type='transfer',
                reason='transfer',
                quantity=form.cleaned_data['quantity'],
                from_warehouse=form.cleaned_data['from_warehouse'],
                from_location=form.cleaned_data.get('from_location'),
                to_warehouse=form.cleaned_data['to_warehouse'],
                to_location=form.cleaned_data.get('to_location'),
                notes=form.cleaned_data.get('notes'),
                created_by=request.user
            )
            
            # Process immediately
            try:
                movement.process_movement()
                messages.success(request, f'Stock transferred successfully. Reference: {movement.reference}')
            except Exception as e:
                messages.error(request, f'Error transferring stock: {str(e)}')
            
            return redirect('inventory:stock_transfer')
    else:
        form = StockTransferForm()
    
    return render(request, 'inventory/stock_transfer.html', {'form': form})


# Stock Adjustments
class StockAdjustmentListView(LoginRequiredMixin, ListView):
    """List all stock adjustments"""
    model = StockAdjustment
    template_name = 'inventory/adjustment_list.html'
    context_object_name = 'adjustments'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = StockAdjustment.objects.select_related('warehouse', 'created_by', 'approved_by')
        
        # Filter by warehouse
        warehouse = self.request.GET.get('warehouse')
        if warehouse:
            queryset = queryset.filter(warehouse_id=warehouse)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status == 'approved':
            queryset = queryset.filter(is_approved=True)
        elif status == 'pending':
            queryset = queryset.filter(is_approved=False)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['warehouses'] = Warehouse.objects.filter(is_active=True)
        return context


class StockAdjustmentDetailView(LoginRequiredMixin, DetailView):
    """Stock adjustment detail view"""
    model = StockAdjustment
    template_name = 'inventory/adjustment_detail.html'
    context_object_name = 'adjustment'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['lines'] = self.object.lines.select_related('product', 'location')
        return context


@login_required
@inventory_access_required
def stock_adjustment_create(request):
    """Create new stock adjustment"""
    if request.method == 'POST':
        form = StockAdjustmentForm(request.POST)
        if form.is_valid():
            adjustment = form.save(commit=False)
            adjustment.created_by = request.user
            adjustment.save()
            messages.success(request, f'Stock adjustment {adjustment.reference} created successfully.')
            return redirect('inventory:adjustment_detail', pk=adjustment.pk)
    else:
        form = StockAdjustmentForm()
    
    return render(request, 'inventory/adjustment_form.html', {'form': form})


@login_required
@inventory_access_required
def approve_adjustment(request, pk):
    """Approve stock adjustment"""
    adjustment = get_object_or_404(StockAdjustment, pk=pk)
    
    if adjustment.is_approved:
        messages.warning(request, 'This adjustment has already been approved.')
        return redirect('inventory:adjustment_detail', pk=adjustment.pk)
    
    if not request.user.is_manager:
        messages.error(request, 'Only managers can approve stock adjustments.')
        return redirect('inventory:adjustment_detail', pk=adjustment.pk)
    
    # Process adjustment lines
    for line in adjustment.lines.all():
        if line.difference != 0:
            # Create stock movement for each line
            movement = StockMovement.objects.create(
                product=line.product,
                product_variant=line.product_variant,
                movement_type='adjustment',
                reason='adjustment',
                quantity=abs(line.difference),
                to_warehouse=adjustment.warehouse if line.difference > 0 else None,
                from_warehouse=adjustment.warehouse if line.difference < 0 else None,
                to_location=line.location if line.difference > 0 else None,
                from_location=line.location if line.difference < 0 else None,
                unit_cost=line.unit_cost,
                notes=f"Adjustment: {adjustment.reference}",
                created_by=request.user
            )
            movement.process_movement()
    
    # Mark as approved
    adjustment.is_approved = True
    adjustment.approved_by = request.user
    adjustment.approved_at = timezone.now()
    adjustment.save()
    
    messages.success(request, f'Stock adjustment {adjustment.reference} approved and processed.')
    return redirect('inventory:adjustment_detail', pk=adjustment.pk)


# Reports and Analytics
@login_required
def stock_report(request):
    """Stock level report"""
    warehouses = Warehouse.objects.filter(is_active=True)
    
    # Get stock data
    stock_data = []
    for warehouse in warehouses:
        stock_records = Stock.objects.filter(
            warehouse=warehouse,
            quantity__gt=0
        ).select_related('product')
        
        total_products = stock_records.count()
        total_value = sum(stock.stock_value for stock in stock_records)
        low_stock_count = stock_records.filter(
            quantity__lte=F('product__min_stock_level')
        ).count()
        
        stock_data.append({
            'warehouse': warehouse,
            'total_products': total_products,
            'total_value': total_value,
            'low_stock_count': low_stock_count,
        })
    
    context = {
        'stock_data': stock_data,
        'total_warehouses': warehouses.count(),
    }
    return render(request, 'inventory/stock_report.html', context)


# AJAX Views
@login_required
def get_product_stock_ajax(request):
    """Get stock levels for a product"""
    product_id = request.GET.get('product_id')
    warehouse_id = request.GET.get('warehouse_id')
    
    if not product_id:
        return JsonResponse({'error': 'Product ID required'})
    
    stock_records = Stock.objects.filter(product_id=product_id)
    if warehouse_id:
        stock_records = stock_records.filter(warehouse_id=warehouse_id)
    
    stock_data = []
    for stock in stock_records.select_related('warehouse', 'location'):
        stock_data.append({
            'warehouse': stock.warehouse.name,
            'location': stock.location.name if stock.location else 'No Location',
            'quantity': stock.quantity,
            'reserved': stock.reserved_quantity,
            'available': stock.available_quantity,
        })
    
    return JsonResponse({'stock_data': stock_data})


@login_required
def movement_stats_ajax(request):
    """Get movement statistics for dashboard charts"""
    days = int(request.GET.get('days', 30))
    start_date = timezone.now() - timedelta(days=days)
    
    # Daily movement data
    daily_data = []
    for i in range(days):
        date = start_date + timedelta(days=i)
        movements_in = StockMovement.objects.filter(
            movement_type='in',
            created_at__date=date.date()
        ).aggregate(total=Sum('quantity'))['total'] or 0
        
        movements_out = StockMovement.objects.filter(
            movement_type='out',
            created_at__date=date.date()
        ).aggregate(total=Sum('quantity'))['total'] or 0
        
        daily_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'in': movements_in,
            'out': movements_out,
        })
    
    return JsonResponse({'daily_data': daily_data})


@login_required
def reports_dashboard(request):
    """Enhanced Reports and analytics dashboard with comprehensive analysis"""
    try:
        # Get date range for filtering
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')

        if date_from:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
        else:
            date_from = timezone.now().date() - timedelta(days=30)

        if date_to:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        else:
            date_to = timezone.now().date()

        # Import sales models for revenue calculations
        from sales.models import SalesOrder, SalesOrderLine

        # Sales Analytics
        sales_orders = SalesOrder.objects.filter(
            order_date__range=[date_from, date_to]
        )

        total_orders = sales_orders.count()
        total_revenue = sales_orders.aggregate(
            total=Sum('total_amount')
        )['total'] or 0

        # Calculate growth (compare with previous period)
        previous_period_start = date_from - timedelta(days=(date_to - date_from).days)
        previous_orders = SalesOrder.objects.filter(
            order_date__range=[previous_period_start, date_from]
        )

        previous_revenue = previous_orders.aggregate(total=Sum('total_amount'))['total'] or 0
        previous_order_count = previous_orders.count()

        revenue_growth = ((total_revenue - previous_revenue) / previous_revenue * 100) if previous_revenue > 0 else 0
        order_growth = ((total_orders - previous_order_count) / previous_order_count * 100) if previous_order_count > 0 else 0

        # Inventory Analytics
        inventory_value = Stock.objects.filter(quantity__gt=0).aggregate(
            total_value=Sum(
                Case(
                    When(product_variant__isnull=False,
                         then=F('quantity') * F('product_variant__cost_price')),
                    default=F('quantity') * F('product__cost_price'),
                    output_field=DecimalField(max_digits=12, decimal_places=2)
                )
            )
        )['total_value'] or 0

        total_products = Product.objects.filter(is_active=True).count()
        total_stock_items = Stock.objects.filter(quantity__gt=0).count()

        low_stock_count = Stock.objects.filter(
            quantity__lte=F('product__min_stock_level'),
            quantity__gt=0
        ).count()

        # Low stock products with details
        low_stock_products = Stock.objects.filter(
            quantity__lte=F('product__min_stock_level'),
            quantity__gt=0
        ).select_related('product', 'warehouse').only(
            'quantity', 'product__name', 'product__sku', 'product__min_stock_level',
            'warehouse__name'
        )[:10]

        # Top selling products
        top_products = Product.objects.filter(
            stockmovement__created_at__range=[date_from, date_to],
            stockmovement__movement_type='out'
        ).annotate(
            total_sold=Sum('stockmovement__quantity'),
            total_revenue=Sum(F('stockmovement__quantity') * F('selling_price'))
        ).order_by('-total_sold')[:10]

        # Category analysis
        from products.models import Category
        top_categories = Category.objects.filter(
            product__stockmovement__created_at__range=[date_from, date_to],
            product__stockmovement__movement_type='out'
        ).annotate(
            total_sold=Sum('product__stockmovement__quantity'),
            total_revenue=Sum(F('product__stockmovement__quantity') * F('product__selling_price'))
        ).order_by('-total_sold')[:5]

        # Movement analytics
        movements_in = StockMovement.objects.filter(
            movement_type='in',
            created_at__range=[date_from, date_to]
        ).aggregate(total=Sum('quantity'))['total'] or 0

        movements_out = StockMovement.objects.filter(
            movement_type='out',
            created_at__range=[date_from, date_to]
        ).aggregate(total=Sum('quantity'))['total'] or 0

        # Recent high-value transactions
        recent_transactions = SalesOrder.objects.filter(
            order_date__range=[date_from, date_to]
        ).select_related('customer').order_by('-total_amount')[:10]

        # Chart data for revenue trend (last 6 months)
        revenue_labels = []
        revenue_data = []

        for i in range(6):
            month_start = (timezone.now().date().replace(day=1) - timedelta(days=30*i))
            month_end = month_start.replace(day=28) + timedelta(days=4)
            month_end = month_end - timedelta(days=month_end.day)

            month_revenue = SalesOrder.objects.filter(
                order_date__range=[month_start, month_end]
            ).aggregate(total=Sum('total_amount'))['total'] or 0

            revenue_labels.insert(0, month_start.strftime('%b %Y'))
            revenue_data.insert(0, float(month_revenue))

        # Category distribution for pie chart
        category_labels = []
        category_data = []

        for category in top_categories:
            category_labels.append(category.name)
            category_data.append(float(category.total_revenue or 0))

        context = {
            'date_from': date_from,
            'date_to': date_to,
            'total_revenue': total_revenue,
            'total_orders': total_orders,
            'revenue_growth': round(revenue_growth, 2),
            'order_growth': round(order_growth, 2),
            'inventory_value': inventory_value,
            'total_products': total_products,
            'total_stock_items': total_stock_items,
            'low_stock_count': low_stock_count,
            'low_stock_products': low_stock_products,
            'top_products': top_products,
            'top_categories': top_categories,
            'movements_in': movements_in,
            'movements_out': movements_out,
            'recent_transactions': recent_transactions,
            'revenue_labels': revenue_labels,
            'revenue_data': revenue_data,
            'category_labels': category_labels,
            'category_data': category_data,
            'analysis_loaded': True,
        }

    except Exception as e:
        # Fallback context if there are any issues
        context = {
            'date_from': date_from if 'date_from' in locals() else timezone.now().date() - timedelta(days=30),
            'date_to': date_to if 'date_to' in locals() else timezone.now().date(),
            'total_revenue': 0,
            'total_orders': 0,
            'revenue_growth': 0,
            'order_growth': 0,
            'inventory_value': 0,
            'total_products': 0,
            'total_stock_items': 0,
            'low_stock_count': 0,
            'low_stock_products': [],
            'top_products': [],
            'top_categories': [],
            'movements_in': 0,
            'movements_out': 0,
            'recent_transactions': [],
            'revenue_labels': [],
            'revenue_data': [],
            'category_labels': [],
            'category_data': [],
            'analysis_error': str(e),
            'analysis_loaded': False,
        }

    return render(request, 'inventory/reports.html', context)


@login_required
def test_navigation(request):
    """Test page for navigation links"""
    return render(request, 'test_navigation.html')


@login_required
def features_demo(request):
    """Demo page for new features"""
    return render(request, 'features_demo.html')
