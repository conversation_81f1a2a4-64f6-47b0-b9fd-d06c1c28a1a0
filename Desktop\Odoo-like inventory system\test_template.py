#!/usr/bin/env python
"""
Test dashboard template rendering
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.template.loader import render_to_string
from django.template import Context, Template
from users.models import User

def test_dashboard_template():
    """Test if dashboard template renders without errors"""
    print("🧪 Testing Dashboard Template Rendering...\n")
    
    try:
        # Create a mock context similar to what the view provides
        context = {
            'total_products': 10,
            'total_warehouses': 2,
            'total_stock_value': 5000.00,
            'total_stock_items': 50,
            'low_stock_items': [],
            'low_stock_count': 3,
            'recent_movements': [],
            'movements_in': 25,
            'movements_out': 15,
            'top_products': [],
            'pending_adjustments': 2,
            'days': 30,
            'dashboard_loaded': True,
        }
        
        # Try to render the template
        print("📋 Attempting to render dashboard template...")
        
        rendered_html = render_to_string('inventory/dashboard.html', context)
        
        print("✅ Template rendered successfully!")
        print(f"   HTML length: {len(rendered_html)} characters")
        
        # Check for key elements
        checks = [
            ('Title block', 'Dashboard - Inventory Management System' in rendered_html),
            ('Content block', 'Dashboard' in rendered_html),
            ('CSS styles', 'border-left-primary' in rendered_html),
            ('Success message', 'Dashboard Loaded Successfully' in rendered_html),
            ('Stock value', 'Stock Value' in rendered_html),
            ('Recent movements', 'movements' in rendered_html.lower()),
        ]
        
        print("\n🔍 Template Content Check:")
        all_passed = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}: {'Present' if check_result else 'Missing'}")
            if not check_result:
                all_passed = False
        
        if all_passed:
            print("\n🎉 All template checks passed!")
            print("✅ Dashboard template is working correctly")
        else:
            print("\n⚠️  Some template elements are missing")
            
        return True
        
    except Exception as e:
        print(f"❌ Template rendering failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_syntax():
    """Test template syntax specifically"""
    print("\n🔧 Testing Template Syntax...\n")
    
    try:
        # Read the template file directly
        template_path = r"Desktop\Odoo-like inventory system\templates\inventory\dashboard.html"
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Try to create a Template object (this will catch syntax errors)
        template = Template(template_content)
        print("✅ Template syntax is valid")
        
        # Check for balanced blocks
        block_starts = template_content.count('{% block')
        block_ends = template_content.count('{% endblock')
        
        print(f"📊 Block Analysis:")
        print(f"   Block starts: {block_starts}")
        print(f"   Block ends: {block_ends}")
        
        if block_starts == block_ends:
            print("✅ All blocks are properly closed")
        else:
            print("❌ Block mismatch detected")
            
        return block_starts == block_ends
        
    except Exception as e:
        print(f"❌ Template syntax error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Dashboard Template Testing\n")
    
    syntax_ok = test_template_syntax()
    template_ok = test_dashboard_template()
    
    print(f"\n📋 Final Results:")
    print(f"   Template Syntax: {'✅ PASS' if syntax_ok else '❌ FAIL'}")
    print(f"   Template Rendering: {'✅ PASS' if template_ok else '❌ FAIL'}")
    
    if syntax_ok and template_ok:
        print(f"\n🎉 Dashboard template is fully functional!")
        print(f"✅ The error at line 252 has been fixed")
        print(f"🚀 Dashboard should now load without errors")
    else:
        print(f"\n⚠️  There are still issues with the template")
