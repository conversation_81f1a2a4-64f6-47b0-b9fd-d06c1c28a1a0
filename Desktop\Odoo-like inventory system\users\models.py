from django.contrib.auth.models import AbstractUser
from django.db import models


class User(AbstractUser):
    """
    Custom User model extending Django's AbstractUser
    """
    USER_ROLES = [
        ('admin', 'Administrator'),
        ('manager', 'Manager'),
        ('warehouse_staff', 'Warehouse Staff'),
        ('sales_staff', 'Sales Staff'),
        ('accountant', 'Accountant'),
        ('viewer', 'Viewer'),
    ]
    
    role = models.CharField(max_length=20, choices=USER_ROLES, default='viewer')
    phone = models.CharField(max_length=20, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    profile_picture = models.ImageField(upload_to='profile_pics/', blank=True, null=True)
    is_active_employee = models.BooleanField(default=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)
    
    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip() or self.username
    
    @property
    def is_admin(self):
        return self.role == 'admin'
    
    @property
    def is_manager(self):
        return self.role in ['admin', 'manager']
    
    @property
    def can_manage_inventory(self):
        return self.role in ['admin', 'manager', 'warehouse_staff']
    
    @property
    def can_manage_sales(self):
        return self.role in ['admin', 'manager', 'sales_staff']
    
    @property
    def can_view_accounting(self):
        return self.role in ['admin', 'manager', 'accountant']


class UserProfile(models.Model):
    """
    Extended user profile information
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    employee_id = models.CharField(max_length=20, unique=True, blank=True, null=True)
    department = models.CharField(max_length=50, blank=True, null=True)
    hire_date = models.DateField(blank=True, null=True)
    emergency_contact = models.CharField(max_length=100, blank=True, null=True)
    emergency_phone = models.CharField(max_length=20, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    
    def __str__(self):
        return f"Profile for {self.user.username}"
