# Generated by Django 5.2.4 on 2025-07-17 08:45

import django.core.validators
import django.db.models.deletion
import products.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='brands/')),
                ('website', models.URLField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to=products.models.product_image_path)),
                ('alt_text', models.CharField(blank=True, max_length=200, null=True)),
                ('is_primary', models.BooleanField(default=False)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('sku', models.CharField(max_length=50, unique=True)),
                ('barcode', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('cost_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('selling_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('color', models.CharField(blank=True, max_length=50, null=True)),
                ('size', models.CharField(blank=True, max_length=50, null=True)),
                ('material', models.CharField(blank=True, max_length=100, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to=products.models.product_image_path)),
                ('barcode_image', models.ImageField(blank=True, null=True, upload_to=products.models.barcode_image_path)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='products.category')),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('sku', models.CharField(help_text='Stock Keeping Unit', max_length=50, unique=True)),
                ('barcode', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('short_description', models.CharField(blank=True, max_length=500, null=True)),
                ('product_type', models.CharField(choices=[('simple', 'Simple Product'), ('variant', 'Product with Variants'), ('service', 'Service'), ('digital', 'Digital Product')], default='simple', max_length=20)),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('weight', models.DecimalField(blank=True, decimal_places=3, help_text='Weight in kg', max_digits=8, null=True)),
                ('dimensions_length', models.DecimalField(blank=True, decimal_places=2, help_text='Length in cm', max_digits=8, null=True)),
                ('dimensions_width', models.DecimalField(blank=True, decimal_places=2, help_text='Width in cm', max_digits=8, null=True)),
                ('dimensions_height', models.DecimalField(blank=True, decimal_places=2, help_text='Height in cm', max_digits=8, null=True)),
                ('unit', models.CharField(choices=[('pcs', 'Pieces'), ('kg', 'Kilograms'), ('g', 'Grams'), ('l', 'Liters'), ('ml', 'Milliliters'), ('m', 'Meters'), ('cm', 'Centimeters'), ('box', 'Box'), ('pack', 'Pack')], default='pcs', max_length=10)),
                ('track_inventory', models.BooleanField(default=True)),
                ('min_stock_level', models.IntegerField(default=0, help_text='Minimum stock level for alerts')),
                ('max_stock_level', models.IntegerField(blank=True, help_text='Maximum stock level', null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to=products.models.product_image_path)),
                ('barcode_image', models.ImageField(blank=True, null=True, upload_to=products.models.barcode_image_path)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('brand', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='products.brand')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='products.category')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
    ]
