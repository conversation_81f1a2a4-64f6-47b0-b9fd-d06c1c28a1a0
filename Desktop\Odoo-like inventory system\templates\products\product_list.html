{% extends 'base.html' %}

{% block title %}Products - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Product Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if user.can_manage_inventory %}
        <div class="btn-group me-2">
            <a href="{% url 'products:create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Product
            </a>
            <a href="{% url 'products:category_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-tags"></i> Categories
            </a>
            <a href="{% url 'products:brand_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-copyright"></i> Brands
            </a>
        </div>
        <div class="btn-group">
            <a href="{% url 'products:export_csv' %}" class="btn btn-outline-success">
                <i class="fas fa-download"></i> Export
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-md-12">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <input type="text" name="search" class="form-control" placeholder="Search products..." value="{{ current_search }}">
            </div>
            <div class="col-md-2">
                <select name="category" class="form-control">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                        <option value="{{ category.id }}" {% if current_category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <select name="brand" class="form-control">
                    <option value="">All Brands</option>
                    {% for brand in brands %}
                        <option value="{{ brand.id }}" {% if current_brand == brand.id|stringformat:"s" %}selected{% endif %}>
                            {{ brand.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <select name="status" class="form-control">
                    <option value="">All Products</option>
                    <option value="active" {% if current_status == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>Inactive</option>
                    <option value="low_stock" {% if current_status == 'low_stock' %}selected{% endif %}>Low Stock</option>
                </select>
            </div>
            <div class="col-md-2">
                <select name="sort" class="form-control">
                    <option value="name" {% if current_sort == 'name' %}selected{% endif %}>Name</option>
                    <option value="sku" {% if current_sort == 'sku' %}selected{% endif %}>SKU</option>
                    <option value="created_at" {% if current_sort == 'created_at' %}selected{% endif %}>Date Created</option>
                    <option value="selling_price" {% if current_sort == 'selling_price' %}selected{% endif %}>Price</option>
                </select>
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-outline-secondary">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Products ({{ products|length }})</h5>
    </div>
    <div class="card-body">
        {% if products %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>SKU</th>
                            <th>Category</th>
                            <th>Brand</th>
                            <th>Cost Price</th>
                            <th>Selling Price</th>
                            <th>Stock</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                            <tr>
                                <td>
                                    {% if product.image %}
                                        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-thumbnail" style="width: 40px; height: 40px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ product.name }}</strong>
                                    {% if product.is_featured %}
                                        <span class="badge bg-warning ms-1">Featured</span>
                                    {% endif %}
                                    {% if product.short_description %}
                                        <br><small class="text-muted">{{ product.short_description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td><code>{{ product.sku }}</code></td>
                                <td>{{ product.category.name|default:"-" }}</td>
                                <td>{{ product.brand.name|default:"-" }}</td>
                                <td>${{ product.cost_price|floatformat:2 }}</td>
                                <td>${{ product.selling_price|floatformat:2 }}</td>
                                <td>
                                    {% if product.track_inventory %}
                                        <span class="{% if product.is_low_stock %}text-danger{% else %}text-success{% endif %}">
                                            {{ product.total_stock }}
                                        </span>
                                        {% if product.is_low_stock %}
                                            <i class="fas fa-exclamation-triangle text-warning ms-1" title="Low Stock"></i>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">Not tracked</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'products:detail' product.pk %}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if user.can_manage_inventory %}
                                            <a href="{% url 'products:edit' product.pk %}" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'products:generate_barcode' product.pk %}" class="btn btn-outline-info" title="Generate Barcode">
                                                <i class="fas fa-barcode"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="Products pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No products found</h5>
                <p class="text-muted">No products match your search criteria.</p>
                {% if user.can_manage_inventory %}
                    <a href="{% url 'products:create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Product
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Product Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">{{ products|length }}</h5>
                <p class="card-text text-muted">Total Products</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">{{ categories|length }}</h5>
                <p class="card-text text-muted">Categories</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">{{ brands|length }}</h5>
                <p class="card-text text-muted">Brands</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title">-</h5>
                <p class="card-text text-muted">Low Stock</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}
