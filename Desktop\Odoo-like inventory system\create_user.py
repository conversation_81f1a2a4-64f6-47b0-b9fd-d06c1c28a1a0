#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from users.models import User

# Create a test user
try:
    user = User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='test123',
        first_name='Test',
        last_name='User',
        role='admin'
    )
    print(f"User created: {user.username} with role: {user.role}")
except Exception as e:
    print(f"Error creating user: {e}")

# List all users
print("All users:")
for user in User.objects.all():
    print(f"- {user.username} ({user.role}) - Active: {user.is_active_employee}")
