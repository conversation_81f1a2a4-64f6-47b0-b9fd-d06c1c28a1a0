{% extends 'base.html' %}

{% block title %}{% if object %}Edit Customer{% else %}Add Customer{% endif %} - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{% if object %}Edit Customer{% else %}Add Customer{% endif %}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Customers
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">Customer Name *</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email *</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">Phone</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.mobile.id_for_label }}" class="form-label">Mobile</label>
                                {{ form.mobile }}
                                {% if form.mobile.errors %}
                                    <div class="text-danger small">{{ form.mobile.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.customer_type.id_for_label }}" class="form-label">Customer Type</label>
                                {{ form.customer_type }}
                                {% if form.customer_type.errors %}
                                    <div class="text-danger small">{{ form.customer_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.website.id_for_label }}" class="form-label">Website</label>
                        {{ form.website }}
                        {% if form.website.errors %}
                            <div class="text-danger small">{{ form.website.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Billing Address -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Billing Address</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.billing_address_line1.id_for_label }}" class="form-label">Address Line 1 *</label>
                        {{ form.billing_address_line1 }}
                        {% if form.billing_address_line1.errors %}
                            <div class="text-danger small">{{ form.billing_address_line1.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.billing_address_line2.id_for_label }}" class="form-label">Address Line 2</label>
                        {{ form.billing_address_line2 }}
                        {% if form.billing_address_line2.errors %}
                            <div class="text-danger small">{{ form.billing_address_line2.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.billing_city.id_for_label }}" class="form-label">City *</label>
                                {{ form.billing_city }}
                                {% if form.billing_city.errors %}
                                    <div class="text-danger small">{{ form.billing_city.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.billing_state.id_for_label }}" class="form-label">State *</label>
                                {{ form.billing_state }}
                                {% if form.billing_state.errors %}
                                    <div class="text-danger small">{{ form.billing_state.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.billing_postal_code.id_for_label }}" class="form-label">Postal Code *</label>
                                {{ form.billing_postal_code }}
                                {% if form.billing_postal_code.errors %}
                                    <div class="text-danger small">{{ form.billing_postal_code.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.billing_country.id_for_label }}" class="form-label">Country *</label>
                        {{ form.billing_country }}
                        {% if form.billing_country.errors %}
                            <div class="text-danger small">{{ form.billing_country.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Shipping Address -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Shipping Address (Optional)</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.shipping_address_line1.id_for_label }}" class="form-label">Address Line 1</label>
                        {{ form.shipping_address_line1 }}
                        {% if form.shipping_address_line1.errors %}
                            <div class="text-danger small">{{ form.shipping_address_line1.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">Leave empty to use billing address</small>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.shipping_address_line2.id_for_label }}" class="form-label">Address Line 2</label>
                        {{ form.shipping_address_line2 }}
                        {% if form.shipping_address_line2.errors %}
                            <div class="text-danger small">{{ form.shipping_address_line2.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.shipping_city.id_for_label }}" class="form-label">City</label>
                                {{ form.shipping_city }}
                                {% if form.shipping_city.errors %}
                                    <div class="text-danger small">{{ form.shipping_city.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.shipping_state.id_for_label }}" class="form-label">State</label>
                                {{ form.shipping_state }}
                                {% if form.shipping_state.errors %}
                                    <div class="text-danger small">{{ form.shipping_state.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.shipping_postal_code.id_for_label }}" class="form-label">Postal Code</label>
                                {{ form.shipping_postal_code }}
                                {% if form.shipping_postal_code.errors %}
                                    <div class="text-danger small">{{ form.shipping_postal_code.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.shipping_country.id_for_label }}" class="form-label">Country</label>
                        {{ form.shipping_country }}
                        {% if form.shipping_country.errors %}
                            <div class="text-danger small">{{ form.shipping_country.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Additional Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.tax_id.id_for_label }}" class="form-label">Tax ID</label>
                                {{ form.tax_id }}
                                {% if form.tax_id.errors %}
                                    <div class="text-danger small">{{ form.tax_id.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.credit_limit.id_for_label }}" class="form-label">Credit Limit</label>
                                {{ form.credit_limit }}
                                {% if form.credit_limit.errors %}
                                    <div class="text-danger small">{{ form.credit_limit.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.payment_terms.id_for_label }}" class="form-label">Payment Terms</label>
                        {{ form.payment_terms }}
                        {% if form.payment_terms.errors %}
                            <div class="text-danger small">{{ form.payment_terms.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3 form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            Active Customer
                        </label>
                        {% if form.is_active.errors %}
                            <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between mt-3">
                <a href="{% url 'sales:customer_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> {% if object %}Update Customer{% else %}Create Customer{% endif %}
                </button>
            </div>
        </form>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>Customer Guidelines:</h6>
                    <ul class="mb-0">
                        <li>Name and email are required</li>
                        <li>Business customers should include company name</li>
                        <li>Complete address helps with shipping</li>
                        <li>Notes can include special instructions</li>
                    </ul>
                </div>
            </div>
        </div>

        {% if object %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Customer Statistics</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td>Total Orders:</td>
                        <td>{{ object.total_orders|default:0 }}</td>
                    </tr>
                    <tr>
                        <td>Total Spent:</td>
                        <td>${{ object.total_spent|default:0|floatformat:2 }}</td>
                    </tr>
                    <tr>
                        <td>Created:</td>
                        <td>{{ object.created_at|date:"M d, Y" }}</td>
                    </tr>
                    <tr>
                        <td>Status:</td>
                        <td>
                            {% if object.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}
