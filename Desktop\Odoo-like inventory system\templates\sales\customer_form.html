{% extends 'base.html' %}

{% block title %}{% if object %}Edit Customer{% else %}Add Customer{% endif %} - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{% if object %}Edit Customer{% else %}Add Customer{% endif %}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Customers
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">Customer Name *</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email *</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">Phone</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.customer_type.id_for_label }}" class="form-label">Customer Type</label>
                                {{ form.customer_type }}
                                {% if form.customer_type.errors %}
                                    <div class="text-danger small">{{ form.customer_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.company.id_for_label }}" class="form-label">Company</label>
                        {{ form.company }}
                        {% if form.company.errors %}
                            <div class="text-danger small">{{ form.company.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">Required for business customers</small>
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Address Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger small">{{ form.address.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.city.id_for_label }}" class="form-label">City</label>
                                {{ form.city }}
                                {% if form.city.errors %}
                                    <div class="text-danger small">{{ form.city.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.state.id_for_label }}" class="form-label">State</label>
                                {{ form.state }}
                                {% if form.state.errors %}
                                    <div class="text-danger small">{{ form.state.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.postal_code.id_for_label }}" class="form-label">Postal Code</label>
                                {{ form.postal_code }}
                                {% if form.postal_code.errors %}
                                    <div class="text-danger small">{{ form.postal_code.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.country.id_for_label }}" class="form-label">Country</label>
                        {{ form.country }}
                        {% if form.country.errors %}
                            <div class="text-danger small">{{ form.country.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Additional Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3 form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            Active Customer
                        </label>
                        {% if form.is_active.errors %}
                            <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between mt-3">
                <a href="{% url 'sales:customer_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> {% if object %}Update Customer{% else %}Create Customer{% endif %}
                </button>
            </div>
        </form>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>Customer Guidelines:</h6>
                    <ul class="mb-0">
                        <li>Name and email are required</li>
                        <li>Business customers should include company name</li>
                        <li>Complete address helps with shipping</li>
                        <li>Notes can include special instructions</li>
                    </ul>
                </div>
            </div>
        </div>

        {% if object %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Customer Statistics</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td>Total Orders:</td>
                        <td>{{ object.total_orders|default:0 }}</td>
                    </tr>
                    <tr>
                        <td>Total Spent:</td>
                        <td>${{ object.total_spent|default:0|floatformat:2 }}</td>
                    </tr>
                    <tr>
                        <td>Created:</td>
                        <td>{{ object.created_at|date:"M d, Y" }}</td>
                    </tr>
                    <tr>
                        <td>Status:</td>
                        <td>
                            {% if object.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}
