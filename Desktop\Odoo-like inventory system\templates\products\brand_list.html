{% extends 'base.html' %}

{% block title %}Brands - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Product Brands</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'products:list' %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Products
        </a>
        {% if user.can_manage_inventory %}
        <a href="{% url 'products:brand_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Brand
        </a>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Brands ({{ brands|length }})</h5>
    </div>
    <div class="card-body">
        {% if brands %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Logo</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Website</th>
                            <th>Products</th>
                            <th>Status</th>
                            <th>Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for brand in brands %}
                            <tr>
                                <td>
                                    {% if brand.logo %}
                                        <img src="{{ brand.logo.url }}" alt="{{ brand.name }}" class="img-thumbnail" style="width: 40px; height: 40px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="fas fa-copyright text-muted"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td><strong>{{ brand.name }}</strong></td>
                                <td>{{ brand.description|truncatechars:50|default:"-" }}</td>
                                <td>
                                    {% if brand.website %}
                                        <a href="{{ brand.website }}" target="_blank" class="text-decoration-none">
                                            <i class="fas fa-external-link-alt"></i> Website
                                        </a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ brand.products.count }}</td>
                                <td>
                                    {% if brand.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>{{ brand.created_at|date:"M d, Y" }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-copyright fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No brands found</h5>
                <p class="text-muted">Create your first product brand to organize your products.</p>
                {% if user.can_manage_inventory %}
                    <a href="{% url 'products:brand_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Brand
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}
