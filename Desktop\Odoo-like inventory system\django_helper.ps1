# Inventory Management System - PowerShell Helper
# This script works around PowerShell execution policy issues

param(
    [Parameter(Position=0)]
    [string]$Command = "help",
    
    [Parameter(Position=1)]
    [string]$Args = ""
)

$PythonExe = ".\odoo_venv\Scripts\python.exe"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host " Inventory Management System Helper" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

switch ($Command.ToLower()) {
    "server" {
        Write-Host "Starting Django development server..." -ForegroundColor Green
        Write-Host "Server will be available at: http://127.0.0.1:8000/" -ForegroundColor Yellow
        Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
        Write-Host ""
        & $PythonExe manage.py runserver 127.0.0.1:8000
    }
    
    "migrate" {
        Write-Host "Running database migrations..." -ForegroundColor Green
        & $PythonExe manage.py makemigrations
        & $PythonExe manage.py migrate
        Write-Host "Migrations completed!" -ForegroundColor Green
    }
    
    "static" {
        Write-Host "Collecting static files..." -ForegroundColor Green
        & $PythonExe manage.py collectstatic --noinput
        Write-Host "Static files collected!" -ForegroundColor Green
    }
    
    "superuser" {
        Write-Host "Creating superuser account..." -ForegroundColor Green
        & $PythonExe manage.py createsuperuser
    }
    
    "test" {
        Write-Host "Running browser compatibility tests..." -ForegroundColor Green
        & $PythonExe test_browser_compatibility.py
        Write-Host ""
        Write-Host "Running export functionality tests..." -ForegroundColor Green
        & $PythonExe test_export_functionality.py
        Write-Host ""
        Write-Host "Running static files tests..." -ForegroundColor Green
        & $PythonExe test_static_files.py
        Write-Host ""
        Write-Host "All tests completed!" -ForegroundColor Green
    }
    
    "shell" {
        Write-Host "Opening Django shell..." -ForegroundColor Green
        & $PythonExe manage.py shell
    }
    
    "status" {
        Write-Host "System Status Check..." -ForegroundColor Green
        Write-Host ""
        Write-Host "Python Version:" -ForegroundColor Yellow
        & $PythonExe --version
        Write-Host ""
        Write-Host "Django Version:" -ForegroundColor Yellow
        & $PythonExe -c "import django; print(django.get_version())"
        Write-Host ""
        Write-Host "Virtual Environment:" -ForegroundColor Yellow
        if (Test-Path ".\odoo_venv\Scripts\python.exe") {
            Write-Host "✅ Virtual environment found" -ForegroundColor Green
        } else {
            Write-Host "❌ Virtual environment not found" -ForegroundColor Red
        }
        Write-Host ""
        Write-Host "Static Files:" -ForegroundColor Yellow
        $staticFiles = @(
            "static\css\browser-compatibility.css",
            "static\css\responsive-enhancements.css",
            "static\css\print.css",
            "static\js\browser-compatibility.js"
        )
        foreach ($file in $staticFiles) {
            if (Test-Path $file) {
                $size = (Get-Item $file).Length
                Write-Host "✅ $file ($size bytes)" -ForegroundColor Green
            } else {
                Write-Host "❌ $file (missing)" -ForegroundColor Red
            }
        }
    }
    
    "urls" {
        Write-Host "Available URLs:" -ForegroundColor Green
        Write-Host ""
        Write-Host "🏠 Dashboard:           http://127.0.0.1:8000/dashboard/" -ForegroundColor Cyan
        Write-Host "🧪 Browser Test:       http://127.0.0.1:8000/browser-test/" -ForegroundColor Cyan
        Write-Host "📦 Products:           http://127.0.0.1:8000/products/" -ForegroundColor Cyan
        Write-Host "🏭 Warehouses:         http://127.0.0.1:8000/warehouses/list/" -ForegroundColor Cyan
        Write-Host "👥 Customers:          http://127.0.0.1:8000/sales/customers/" -ForegroundColor Cyan
        Write-Host "📋 Sales Orders:       http://127.0.0.1:8000/sales/orders/" -ForegroundColor Cyan
        Write-Host "⚙️  Admin:             http://127.0.0.1:8000/admin/" -ForegroundColor Cyan
        Write-Host "🌟 Features Demo:      http://127.0.0.1:8000/dashboard/features-demo/" -ForegroundColor Cyan
    }
    
    "help" {
        Write-Host "Available commands:" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "  server     - Start Django development server" -ForegroundColor White
        Write-Host "  migrate    - Run database migrations" -ForegroundColor White
        Write-Host "  static     - Collect static files" -ForegroundColor White
        Write-Host "  superuser  - Create superuser account" -ForegroundColor White
        Write-Host "  test       - Run all compatibility tests" -ForegroundColor White
        Write-Host "  shell      - Open Django shell" -ForegroundColor White
        Write-Host "  status     - Check system status" -ForegroundColor White
        Write-Host "  urls       - Show available URLs" -ForegroundColor White
        Write-Host "  help       - Show this help message" -ForegroundColor White
        Write-Host ""
        Write-Host "Usage Examples:" -ForegroundColor Yellow
        Write-Host "  .\django_helper.ps1 server" -ForegroundColor Cyan
        Write-Host "  .\django_helper.ps1 migrate" -ForegroundColor Cyan
        Write-Host "  .\django_helper.ps1 test" -ForegroundColor Cyan
        Write-Host "  .\django_helper.ps1 status" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Alternative (if execution policy issues):" -ForegroundColor Yellow
        Write-Host "  powershell -ExecutionPolicy Bypass -File django_helper.ps1 server" -ForegroundColor Cyan
    }
    
    default {
        Write-Host "Unknown command: $Command" -ForegroundColor Red
        Write-Host "Use 'help' to see available commands" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " Command completed!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
