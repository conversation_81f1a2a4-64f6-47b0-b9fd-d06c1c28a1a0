from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Customer, SalesOrder
from .forms import CustomerForm, SalesOrderForm
from utils.exports import export_queryset_to_pdf, export_queryset_to_excel, export_queryset_to_csv


@login_required
def placeholder_view(request):
    """Placeholder view for sales module"""
    return redirect('sales:order_list')


@login_required
def order_list(request):
    """List all sales orders"""
    # Handle export requests
    export_format = request.GET.get('format')
    if export_format in ['pdf', 'excel', 'csv']:
        orders = SalesOrder.objects.all().order_by('-created_at')
        fields = ['order_number', 'customer__name', 'order_date', 'status', 'total_amount', 'created_by__username']
        title = 'Sales Orders Report'

        if export_format == 'pdf':
            return export_queryset_to_pdf(orders, fields, title)
        elif export_format == 'excel':
            return export_queryset_to_excel(orders, fields, title)
        elif export_format == 'csv':
            return export_queryset_to_csv(orders, fields, title)

    orders = SalesOrder.objects.all().order_by('-created_at')

    # Search and filter
    search = request.GET.get('search')
    if search:
        orders = orders.filter(
            Q(order_number__icontains=search) |
            Q(customer__name__icontains=search) |
            Q(customer__email__icontains=search)
        )

    status = request.GET.get('status')
    if status:
        orders = orders.filter(status=status)

    customer_id = request.GET.get('customer')
    if customer_id:
        orders = orders.filter(customer_id=customer_id)

    # Pagination
    paginator = Paginator(orders, 25)
    page_number = request.GET.get('page')
    orders = paginator.get_page(page_number)

    context = {
        'orders': orders,
        'customers': Customer.objects.filter(is_active=True),
        'order_statuses': SalesOrder.STATUS_CHOICES,
        'total_orders': SalesOrder.objects.count(),
        'total_revenue': 0,  # Calculate from orders
        'pending_orders': SalesOrder.objects.filter(status='pending').count(),
        'avg_order_value': 0,  # Calculate average
    }
    return render(request, 'sales/order_list.html', context)


@login_required
def order_detail(request, pk):
    """View sales order details"""
    order = get_object_or_404(SalesOrder, pk=pk)
    context = {
        'order': order,
    }
    return render(request, 'sales/order_detail.html', context)


@login_required
def order_create(request):
    """Create new sales order"""
    if request.method == 'POST':
        form = SalesOrderForm(request.POST)
        if form.is_valid():
            order = form.save(commit=False)
            order.created_by = request.user
            order.save()
            messages.success(request, 'Sales order created successfully!')
            return redirect('sales:order_detail', pk=order.pk)
    else:
        form = SalesOrderForm()

    context = {
        'form': form,
    }
    return render(request, 'sales/order_form.html', context)


@login_required
def order_edit(request, pk):
    """Edit sales order"""
    order = get_object_or_404(SalesOrder, pk=pk)
    if request.method == 'POST':
        form = SalesOrderForm(request.POST, instance=order)
        if form.is_valid():
            form.save()
            messages.success(request, 'Sales order updated successfully!')
            return redirect('sales:order_detail', pk=order.pk)
    else:
        form = SalesOrderForm(instance=order)

    context = {
        'form': form,
        'object': order,
    }
    return render(request, 'sales/order_form.html', context)


@login_required
def customer_list(request):
    """List all customers"""
    # Handle export requests
    export_format = request.GET.get('format')
    if export_format in ['pdf', 'excel', 'csv']:
        customers = Customer.objects.all().order_by('name')
        fields = ['name', 'email', 'phone', 'company', 'billing_city', 'billing_country', 'is_active']
        title = 'Customers Report'

        if export_format == 'pdf':
            return export_queryset_to_pdf(customers, fields, title)
        elif export_format == 'excel':
            return export_queryset_to_excel(customers, fields, title)
        elif export_format == 'csv':
            return export_queryset_to_csv(customers, fields, title)

    customers = Customer.objects.all().order_by('name')

    # Search and filter
    search = request.GET.get('search')
    if search:
        customers = customers.filter(
            Q(name__icontains=search) |
            Q(email__icontains=search) |
            Q(company__icontains=search)
        )

    customer_type = request.GET.get('customer_type')
    if customer_type:
        customers = customers.filter(customer_type=customer_type)

    status = request.GET.get('status')
    if status == 'active':
        customers = customers.filter(is_active=True)
    elif status == 'inactive':
        customers = customers.filter(is_active=False)

    # Pagination
    paginator = Paginator(customers, 25)
    page_number = request.GET.get('page')
    customers = paginator.get_page(page_number)

    context = {
        'customers': customers,
        'total_customers': Customer.objects.count(),
        'active_customers': Customer.objects.filter(is_active=True).count(),
        'business_customers': Customer.objects.filter(customer_type='business').count(),
        'total_revenue': 0,  # Calculate from orders
    }
    return render(request, 'sales/customer_list.html', context)


@login_required
def customer_detail(request, pk):
    """View customer details"""
    customer = get_object_or_404(Customer, pk=pk)
    recent_orders = SalesOrder.objects.filter(customer=customer).order_by('-created_at')[:5]

    context = {
        'customer': customer,
        'recent_orders': recent_orders,
        'total_orders': SalesOrder.objects.filter(customer=customer).count(),
        'total_spent': 0,  # Calculate from orders
        'avg_order_value': 0,  # Calculate average
        'last_order_date': recent_orders.first().created_at if recent_orders else None,
    }
    return render(request, 'sales/customer_detail.html', context)


@login_required
def customer_create(request):
    """Create new customer"""
    if request.method == 'POST':
        form = CustomerForm(request.POST)
        if form.is_valid():
            customer = form.save(commit=False)
            customer.created_by = request.user
            customer.save()
            messages.success(request, 'Customer created successfully!')
            return redirect('sales:customer_detail', pk=customer.pk)
    else:
        form = CustomerForm()

    context = {
        'form': form,
    }
    return render(request, 'sales/customer_form.html', context)


@login_required
def customer_edit(request, pk):
    """Edit customer"""
    customer = get_object_or_404(Customer, pk=pk)
    if request.method == 'POST':
        form = CustomerForm(request.POST, instance=customer)
        if form.is_valid():
            form.save()
            messages.success(request, 'Customer updated successfully!')
            return redirect('sales:customer_detail', pk=customer.pk)
    else:
        form = CustomerForm(instance=customer)

    context = {
        'form': form,
        'object': customer,
    }
    return render(request, 'sales/customer_form.html', context)
