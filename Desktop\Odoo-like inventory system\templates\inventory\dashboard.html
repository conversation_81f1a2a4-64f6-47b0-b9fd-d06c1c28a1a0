{% extends 'base.html' %}

{% block title %}Dashboard - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">Share</button>
            <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Products
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_products }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Warehouses
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_warehouses }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Stock Value
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ total_stock_value|floatformat:2 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Low Stock Items
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ low_stock_items|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Activity -->
<div class="row">
    <!-- Stock Movement Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Stock Movements (Last {{ days }} days)</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="text-center">
                            <h4 class="text-success">{{ movements_in }}</h4>
                            <p class="text-muted">Stock In</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <h4 class="text-danger">{{ movements_out }}</h4>
                            <p class="text-muted">Stock Out</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Alerts -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Low Stock Alerts</h6>
            </div>
            <div class="card-body">
                {% if low_stock_items %}
                    {% for stock in low_stock_items %}
                        <div class="d-flex align-items-center mb-2">
                            <div class="flex-grow-1">
                                <div class="small font-weight-bold">{{ stock.product.name }}</div>
                                <div class="small text-muted">{{ stock.warehouse.name }}</div>
                            </div>
                            <div class="text-danger font-weight-bold">{{ stock.quantity }}</div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No low stock alerts</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Movements and Top Products -->
<div class="row">
    <!-- Recent Movements -->
    <div class="col-xl-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Movements</h6>
            </div>
            <div class="card-body">
                {% if recent_movements %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Type</th>
                                    <th>Qty</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for movement in recent_movements %}
                                    <tr>
                                        <td>{{ movement.product.name|truncatechars:20 }}</td>
                                        <td>
                                            <span class="badge bg-{% if movement.movement_type == 'in' %}success{% elif movement.movement_type == 'out' %}danger{% else %}info{% endif %}">
                                                {{ movement.get_movement_type_display }}
                                            </span>
                                        </td>
                                        <td>{{ movement.quantity }}</td>
                                        <td>{{ movement.created_at|date:"M d" }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No recent movements</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Top Moving Products -->
    <div class="col-xl-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Top Moving Products</h6>
            </div>
            <div class="card-body">
                {% if top_products %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th>Total Movements</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                    <tr>
                                        <td>{{ product.product__name|truncatechars:20 }}</td>
                                        <td>{{ product.product__sku }}</td>
                                        <td>{{ product.total_movements }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No movement data available</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if user.can_manage_inventory %}
<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'inventory:quick_stock_in' %}" class="btn btn-success btn-block">
                            <i class="fas fa-plus-circle"></i> Quick Stock In
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'inventory:quick_stock_out' %}" class="btn btn-danger btn-block">
                            <i class="fas fa-minus-circle"></i> Quick Stock Out
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'inventory:stock_transfer' %}" class="btn btn-info btn-block">
                            <i class="fas fa-exchange-alt"></i> Transfer Stock
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'products:create' %}" class="btn btn-primary btn-block">
                            <i class="fas fa-plus"></i> Add Product
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}
