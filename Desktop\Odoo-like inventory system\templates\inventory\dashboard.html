{% extends 'base.html' %}

{% block title %}Dashboard - Inventory Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <a href="/dashboard/reports/" class="btn btn-sm btn-primary">
                    <i class="fas fa-chart-bar"></i> Analytics
                </a>
                <a href="/products/" class="btn btn-sm btn-success">
                    <i class="fas fa-box"></i> Products
                </a>
                <a href="/admin/" class="btn btn-sm btn-info">
                    <i class="fas fa-cog"></i> Admin
                </a>
            </div>
        </div>
    </div>



    <!-- Quick Stats Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Products
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <i class="fas fa-box"></i> {{ total_products }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Warehouses
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <i class="fas fa-warehouse"></i> {{ total_warehouses }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Stock Movements
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <i class="fas fa-exchange-alt"></i> {{ recent_movements|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <i class="fas fa-exclamation-triangle"></i> {{ low_stock_count }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="/products/" class="btn btn-primary btn-block">
                                <i class="fas fa-plus-circle"></i> Add Product
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="/warehouses/list/" class="btn btn-success btn-block">
                                <i class="fas fa-warehouse"></i> Manage Warehouses
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="/admin/" class="btn btn-info btn-block">
                                <i class="fas fa-cog"></i> Admin Panel
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="/test/" class="btn btn-secondary btn-block">
                                <i class="fas fa-vial"></i> Test System
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i> System Status
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Django Server
                            <span class="badge bg-success rounded-pill">Running</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Template System
                            <span class="badge bg-success rounded-pill">Working</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Static Files
                            <span class="badge bg-success rounded-pill">Ready</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Database
                            <span class="badge bg-success rounded-pill">Connected</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Browser Compatibility
                            <span class="badge bg-success rounded-pill">Enabled</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-link"></i> Quick Links
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="/dashboard/reports/" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar"></i> Reports & Analytics
                        </a>
                        <a href="/products/" class="list-group-item list-group-item-action">
                            <i class="fas fa-box"></i> Products Management
                        </a>
                        <a href="/warehouses/list/" class="list-group-item list-group-item-action">
                            <i class="fas fa-warehouse"></i> Warehouses
                        </a>
                        <a href="/sales/orders/" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart"></i> Sales Orders
                        </a>
                        <a href="/admin/" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog"></i> Admin Panel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}
