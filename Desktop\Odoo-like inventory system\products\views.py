from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, Sum, F
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from users.views import inventory_access_required
from .models import Product, Category, Brand, ProductVariant, ProductImage
from .forms import ProductForm, CategoryForm, BrandForm, ProductVariantForm, ProductImageForm, ProductSearchForm
from .utils import generate_barcode, generate_qr_code
from utils.exports import export_queryset_to_pdf, export_queryset_to_excel, export_queryset_to_csv
import json


class ProductListView(LoginRequiredMixin, ListView):
    """List all products with search and filtering"""
    model = Product
    template_name = 'products/product_list.html'
    context_object_name = 'products'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Product.objects.select_related('category', 'brand').prefetch_related('stock_set')
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(sku__icontains=search) |
                Q(barcode__icontains=search) |
                Q(description__icontains=search)
            )
        
        # Filter by category
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category_id=category)
        
        # Filter by brand
        brand = self.request.GET.get('brand')
        if brand:
            queryset = queryset.filter(brand_id=brand)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'low_stock':
            queryset = queryset.filter(stock__quantity__lte=F('min_stock_level'))
        
        # Sorting
        sort_by = self.request.GET.get('sort', 'name')
        if sort_by in ['name', 'sku', 'created_at', 'selling_price']:
            if self.request.GET.get('order') == 'desc':
                sort_by = f'-{sort_by}'
            queryset = queryset.order_by(sort_by)
        
        return queryset.distinct()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.filter(is_active=True)
        context['brands'] = Brand.objects.filter(is_active=True)
        context['search_form'] = ProductSearchForm(self.request.GET)

        # Add current filter values
        context['current_search'] = self.request.GET.get('search', '')
        context['current_category'] = self.request.GET.get('category', '')
        context['current_brand'] = self.request.GET.get('brand', '')
        context['current_status'] = self.request.GET.get('status', '')
        context['current_sort'] = self.request.GET.get('sort', 'name')
        context['current_order'] = self.request.GET.get('order', 'asc')

        # Add statistics for the dashboard cards
        context['total_products'] = Product.objects.count()
        context['active_products'] = Product.objects.filter(is_active=True).count()
        context['inactive_products'] = Product.objects.filter(is_active=False).count()
        context['total_categories'] = Category.objects.filter(is_active=True).count()
        context['total_brands'] = Brand.objects.filter(is_active=True).count()

        # Calculate low stock products (products where current stock <= min_stock_level)
        from warehouses.models import Stock
        low_stock_count = 0
        for product in Product.objects.filter(is_active=True):
            total_stock = Stock.objects.filter(product=product).aggregate(
                total=Sum('quantity')
            )['total'] or 0
            if total_stock <= product.min_stock_level:
                low_stock_count += 1
        context['low_stock_products'] = low_stock_count

        return context

    def get(self, request, *args, **kwargs):
        # Handle export requests
        export_format = request.GET.get('format')
        if export_format in ['pdf', 'excel', 'csv']:
            queryset = self.get_queryset()
            fields = ['sku', 'name', 'category__name', 'brand__name', 'selling_price', 'cost_price', 'min_stock_level', 'is_active']
            title = 'Products Report'

            if export_format == 'pdf':
                return export_queryset_to_pdf(queryset, fields, title)
            elif export_format == 'excel':
                return export_queryset_to_excel(queryset, fields, title)
            elif export_format == 'csv':
                return export_queryset_to_csv(queryset, fields, title)

        return super().get(request, *args, **kwargs)


class ProductDetailView(LoginRequiredMixin, DetailView):
    """Product detail view with stock information"""
    model = Product
    template_name = 'products/product_detail.html'
    context_object_name = 'product'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        product = self.get_object()
        
        # Get stock information
        context['stock_records'] = product.stock_set.select_related('warehouse', 'location').all()
        context['total_stock'] = sum(stock.quantity for stock in context['stock_records'])
        context['total_reserved'] = sum(stock.reserved_quantity for stock in context['stock_records'])
        context['available_stock'] = context['total_stock'] - context['total_reserved']
        
        # Get variants
        context['variants'] = product.variants.filter(is_active=True)
        
        # Get additional images
        context['images'] = product.images.all().order_by('order')
        
        # Get recent stock movements
        context['recent_movements'] = product.stockmovement_set.all()[:10]
        
        return context


class ProductCreateView(LoginRequiredMixin, CreateView):
    """Create new product"""
    model = Product
    form_class = ProductForm
    template_name = 'products/product_form.html'
    success_url = reverse_lazy('products:list')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.can_manage_inventory:
            messages.error(request, 'You do not have permission to create products.')
            return redirect('products:list')
        return super().dispatch(request, *args, **kwargs)
    
    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, f'Product "{form.instance.name}" created successfully.')
        return super().form_valid(form)


class ProductUpdateView(LoginRequiredMixin, UpdateView):
    """Update existing product"""
    model = Product
    form_class = ProductForm
    template_name = 'products/product_form.html'
    success_url = reverse_lazy('products:list')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.can_manage_inventory:
            messages.error(request, 'You do not have permission to edit products.')
            return redirect('products:list')
        return super().dispatch(request, *args, **kwargs)
    
    def form_valid(self, form):
        messages.success(self.request, f'Product "{form.instance.name}" updated successfully.')
        return super().form_valid(form)


@login_required
@inventory_access_required
def product_delete(request, pk):
    """Delete product (soft delete by setting inactive)"""
    product = get_object_or_404(Product, pk=pk)
    
    if request.method == 'POST':
        product.is_active = False
        product.save()
        messages.success(request, f'Product "{product.name}" has been deactivated.')
        return redirect('products:list')
    
    return render(request, 'products/product_confirm_delete.html', {'product': product})


# Category Views
class CategoryListView(LoginRequiredMixin, ListView):
    """List all categories"""
    model = Category
    template_name = 'products/category_list.html'
    context_object_name = 'categories'
    
    def get_queryset(self):
        return Category.objects.filter(is_active=True).order_by('name')


class CategoryCreateView(LoginRequiredMixin, CreateView):
    """Create new category"""
    model = Category
    form_class = CategoryForm
    template_name = 'products/category_form.html'
    success_url = reverse_lazy('products:category_list')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.can_manage_inventory:
            messages.error(request, 'You do not have permission to create categories.')
            return redirect('products:category_list')
        return super().dispatch(request, *args, **kwargs)


# Brand Views
class BrandListView(LoginRequiredMixin, ListView):
    """List all brands"""
    model = Brand
    template_name = 'products/brand_list.html'
    context_object_name = 'brands'
    
    def get_queryset(self):
        return Brand.objects.filter(is_active=True).order_by('name')


class BrandCreateView(LoginRequiredMixin, CreateView):
    """Create new brand"""
    model = Brand
    form_class = BrandForm
    template_name = 'products/brand_form.html'
    success_url = reverse_lazy('products:brand_list')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.can_manage_inventory:
            messages.error(request, 'You do not have permission to create brands.')
            return redirect('products:brand_list')
        return super().dispatch(request, *args, **kwargs)


# Product Variant Views
@login_required
@inventory_access_required
def product_variants(request, product_pk):
    """Manage product variants"""
    product = get_object_or_404(Product, pk=product_pk)
    variants = product.variants.all()
    
    if request.method == 'POST':
        form = ProductVariantForm(request.POST, request.FILES)
        if form.is_valid():
            variant = form.save(commit=False)
            variant.product = product
            variant.save()
            messages.success(request, f'Variant "{variant.name}" created successfully.')
            return redirect('products:variants', product_pk=product.pk)
    else:
        form = ProductVariantForm()
    
    context = {
        'product': product,
        'variants': variants,
        'form': form,
    }
    return render(request, 'products/product_variants.html', context)


# Barcode and QR Code Generation
@login_required
def generate_product_barcode(request, pk):
    """Generate barcode for product"""
    product = get_object_or_404(Product, pk=pk)
    
    if not product.barcode:
        # Generate barcode if not exists
        product.barcode = f"PRD{product.pk:08d}"
        product.save()
    
    try:
        barcode_path = generate_barcode(product.barcode, product.sku)
        product.barcode_image = barcode_path
        product.save()
        
        messages.success(request, f'Barcode generated for product "{product.name}".')
    except Exception as e:
        messages.error(request, f'Error generating barcode: {str(e)}')
    
    return redirect('products:detail', pk=product.pk)


@login_required
def generate_product_qr(request, pk):
    """Generate QR code for product"""
    product = get_object_or_404(Product, pk=pk)
    
    try:
        qr_data = {
            'sku': product.sku,
            'name': product.name,
            'barcode': product.barcode,
            'price': str(product.selling_price),
        }
        qr_path = generate_qr_code(json.dumps(qr_data), f"qr_{product.sku}")
        
        messages.success(request, f'QR code generated for product "{product.name}".')
        
        # Return QR code path for download or display
        return JsonResponse({'success': True, 'qr_path': qr_path})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


# AJAX Views
@login_required
def product_search_ajax(request):
    """AJAX search for products"""
    query = request.GET.get('q', '')
    products = Product.objects.filter(
        Q(name__icontains=query) | Q(sku__icontains=query),
        is_active=True
    )[:10]
    
    results = []
    for product in products:
        results.append({
            'id': product.id,
            'sku': product.sku,
            'name': product.name,
            'price': str(product.selling_price),
            'stock': product.total_stock,
        })
    
    return JsonResponse({'results': results})


@login_required
def check_sku_availability(request):
    """Check if SKU is available"""
    sku = request.GET.get('sku', '')
    product_id = request.GET.get('product_id', None)
    
    query = Product.objects.filter(sku=sku)
    if product_id:
        query = query.exclude(id=product_id)
    
    is_available = not query.exists()
    
    return JsonResponse({'available': is_available})


# Bulk Operations
@login_required
@inventory_access_required
def bulk_update_products(request):
    """Bulk update products"""
    if request.method == 'POST':
        product_ids = request.POST.getlist('product_ids')
        action = request.POST.get('action')
        
        products = Product.objects.filter(id__in=product_ids)
        
        if action == 'activate':
            products.update(is_active=True)
            messages.success(request, f'{len(product_ids)} products activated.')
        elif action == 'deactivate':
            products.update(is_active=False)
            messages.success(request, f'{len(product_ids)} products deactivated.')
        elif action == 'delete':
            products.update(is_active=False)
            messages.success(request, f'{len(product_ids)} products deleted.')
    
    return redirect('products:list')


# Export Views
@login_required
def export_products_csv(request):
    """Export products to CSV"""
    import csv
    from django.http import HttpResponse
    
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="products.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['SKU', 'Name', 'Category', 'Brand', 'Cost Price', 'Selling Price', 'Stock', 'Status'])
    
    products = Product.objects.select_related('category', 'brand').all()
    for product in products:
        writer.writerow([
            product.sku,
            product.name,
            product.category.name if product.category else '',
            product.brand.name if product.brand else '',
            product.cost_price,
            product.selling_price,
            product.total_stock,
            'Active' if product.is_active else 'Inactive'
        ])
    
    return response
