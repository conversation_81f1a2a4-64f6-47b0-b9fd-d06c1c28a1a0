{% extends 'base.html' %}

{% block title %}Customers - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Customer Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% include 'components/export_buttons.html' with export_url=request.get_full_path page_title='Customers Report' %}
        {% if user.can_manage_sales %}
        <a href="{% url 'sales:customer_create' %}" class="btn btn-primary ms-2">
            <i class="fas fa-plus"></i> Add Customer
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-12">
        <form method="get" class="row g-3">
            <div class="col-md-4 col-sm-6">
                <input type="text" name="search" class="form-control" placeholder="Search customers..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3 col-sm-6">
                <select name="customer_type" class="form-control">
                    <option value="">All Types</option>
                    <option value="individual" {% if request.GET.customer_type == 'individual' %}selected{% endif %}>Individual</option>
                    <option value="business" {% if request.GET.customer_type == 'business' %}selected{% endif %}>Business</option>
                </select>
            </div>
            <div class="col-md-3 col-sm-6">
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                </select>
            </div>
            <div class="col-md-2 col-sm-6">
                <button type="submit" class="btn btn-outline-secondary w-100">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Customers ({{ customers|length }})</h5>
    </div>
    <div class="card-body">
        {% if customers %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th class="d-none d-md-table-cell">Email</th>
                            <th class="d-none d-lg-table-cell">Phone</th>
                            <th class="d-none d-md-table-cell">Type</th>
                            <th class="d-none d-lg-table-cell">Total Orders</th>
                            <th class="d-none d-lg-table-cell">Total Spent</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ customer.name }}</strong>
                                        {% if customer.company %}
                                            <br><small class="text-muted">{{ customer.company }}</small>
                                        {% endif %}
                                        <div class="d-md-none">
                                            <small class="text-muted">{{ customer.email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">{{ customer.email }}</td>
                                <td class="d-none d-lg-table-cell">{{ customer.phone|default:"-" }}</td>
                                <td class="d-none d-md-table-cell">
                                    <span class="badge bg-{% if customer.customer_type == 'business' %}info{% else %}secondary{% endif %}">
                                        {{ customer.get_customer_type_display }}
                                    </span>
                                </td>
                                <td class="d-none d-lg-table-cell">{{ customer.total_orders|default:0 }}</td>
                                <td class="d-none d-lg-table-cell">${{ customer.total_spent|default:0|floatformat:2 }}</td>
                                <td>
                                    {% if customer.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'sales:customer_detail' customer.pk %}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if user.can_manage_sales %}
                                            <a href="{% url 'sales:customer_edit' customer.pk %}" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="Customers pagination" class="mt-3">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No customers found</h5>
                <p class="text-muted">No customers match your search criteria.</p>
                {% if user.can_manage_sales %}
                    <a href="{% url 'sales:customer_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Customer
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Customer Statistics -->
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <h5 class="card-title">{{ total_customers }}</h5>
                <p class="card-text text-muted">Total Customers</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <h5 class="card-title">{{ active_customers }}</h5>
                <p class="card-text text-muted">Active Customers</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <h5 class="card-title">{{ business_customers }}</h5>
                <p class="card-text text-muted">Business Customers</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <h5 class="card-title">${{ total_revenue|floatformat:2 }}</h5>
                <p class="card-text text-muted">Total Revenue</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}
