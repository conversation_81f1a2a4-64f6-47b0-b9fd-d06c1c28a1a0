/**
 * Cross-Browser Compatibility JavaScript
 * Polyfills and fixes for all major browsers
 */

(function() {
    'use strict';

    // Browser detection
    var browserDetection = {
        isIE: function() {
            return navigator.userAgent.indexOf('MSIE') !== -1 || navigator.appVersion.indexOf('Trident/') > -1;
        },
        isEdge: function() {
            return navigator.userAgent.indexOf('Edge') !== -1;
        },
        isChrome: function() {
            return navigator.userAgent.indexOf('Chrome') !== -1 && !this.isEdge();
        },
        isFirefox: function() {
            return navigator.userAgent.indexOf('Firefox') !== -1;
        },
        isSafari: function() {
            return navigator.userAgent.indexOf('Safari') !== -1 && !this.isChrome() && !this.isEdge();
        },
        isOpera: function() {
            return navigator.userAgent.indexOf('Opera') !== -1 || navigator.userAgent.indexOf('OPR') !== -1;
        },
        isMobile: function() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }
    };

    // Add browser classes to body
    function addBrowserClasses() {
        var body = document.body;
        var classes = [];

        if (browserDetection.isIE()) classes.push('browser-ie');
        if (browserDetection.isEdge()) classes.push('browser-edge');
        if (browserDetection.isChrome()) classes.push('browser-chrome');
        if (browserDetection.isFirefox()) classes.push('browser-firefox');
        if (browserDetection.isSafari()) classes.push('browser-safari');
        if (browserDetection.isOpera()) classes.push('browser-opera');
        if (browserDetection.isMobile()) classes.push('browser-mobile');

        // Check for legacy browsers
        if (browserDetection.isIE() && parseFloat(navigator.appVersion.split("MSIE")[1]) < 11) {
            classes.push('legacy-browser');
        }

        body.className += ' ' + classes.join(' ');
    }

    // Feature detection
    var featureDetection = {
        hasFlexbox: function() {
            var elem = document.createElement('div');
            return 'flexWrap' in elem.style;
        },
        hasCSSGrid: function() {
            return typeof document.createElement('div').style.grid !== 'undefined';
        },
        hasLocalStorage: function() {
            try {
                return 'localStorage' in window && window['localStorage'] !== null;
            } catch (e) {
                return false;
            }
        },
        hasSessionStorage: function() {
            try {
                return 'sessionStorage' in window && window['sessionStorage'] !== null;
            } catch (e) {
                return false;
            }
        },
        hasJSON: function() {
            return typeof JSON !== 'undefined';
        },
        hasConsole: function() {
            return typeof console !== 'undefined';
        }
    };

    // Add feature classes
    function addFeatureClasses() {
        var html = document.documentElement;
        
        if (!featureDetection.hasFlexbox()) html.className += ' no-flexbox';
        if (!featureDetection.hasCSSGrid()) html.className += ' no-cssgrid';
        if (!featureDetection.hasLocalStorage()) html.className += ' no-localstorage';
        if (!featureDetection.hasJSON()) html.className += ' no-json';
    }

    // Console polyfill for older browsers
    if (!featureDetection.hasConsole()) {
        window.console = {
            log: function() {},
            warn: function() {},
            error: function() {},
            info: function() {},
            debug: function() {}
        };
    }

    // JSON polyfill for IE7 and below
    if (!featureDetection.hasJSON()) {
        window.JSON = {
            parse: function(str) {
                return eval('(' + str + ')');
            },
            stringify: function(obj) {
                var t = typeof (obj);
                if (t != "object" || obj === null) {
                    if (t == "string") obj = '"' + obj + '"';
                    return String(obj);
                } else {
                    var n, v, json = [], arr = (obj && obj.constructor == Array);
                    for (n in obj) {
                        v = obj[n];
                        t = typeof(v);
                        if (t == "string") v = '"' + v + '"';
                        else if (t == "object" && v !== null) v = JSON.stringify(v);
                        json.push((arr ? "" : '"' + n + '":') + String(v));
                    }
                    return (arr ? "[" : "{") + String(json) + (arr ? "]" : "}");
                }
            }
        };
    }

    // Array.forEach polyfill
    if (!Array.prototype.forEach) {
        Array.prototype.forEach = function(callback, thisArg) {
            var T, k;
            if (this == null) {
                throw new TypeError('this is null or not defined');
            }
            var O = Object(this);
            var len = parseInt(O.length) || 0;
            if (typeof callback !== "function") {
                throw new TypeError(callback + ' is not a function');
            }
            if (arguments.length > 1) {
                T = thisArg;
            }
            k = 0;
            while (k < len) {
                var kValue;
                if (k in O) {
                    kValue = O[k];
                    callback.call(T, kValue, k, O);
                }
                k++;
            }
        };
    }

    // Array.indexOf polyfill
    if (!Array.prototype.indexOf) {
        Array.prototype.indexOf = function(searchElement, fromIndex) {
            var k;
            if (this == null) {
                throw new TypeError('"this" is null or not defined');
            }
            var o = Object(this);
            var len = parseInt(o.length) || 0;
            if (len === 0) {
                return -1;
            }
            var n = parseInt(fromIndex) || 0;
            var k;
            if (n >= len) {
                return -1;
            }
            k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);
            while (k < len) {
                if (k in o && o[k] === searchElement) {
                    return k;
                }
                k++;
            }
            return -1;
        };
    }

    // Object.keys polyfill
    if (!Object.keys) {
        Object.keys = function(obj) {
            var keys = [];
            for (var i in obj) {
                if (obj.hasOwnProperty(i)) {
                    keys.push(i);
                }
            }
            return keys;
        };
    }

    // addEventListener polyfill for IE8
    if (!window.addEventListener) {
        window.addEventListener = function(type, listener, useCapture) {
            window.attachEvent('on' + type, listener);
        };
        window.removeEventListener = function(type, listener, useCapture) {
            window.detachEvent('on' + type, listener);
        };
    }

    // querySelector polyfill for IE7
    if (!document.querySelector) {
        document.querySelector = function(selector) {
            var elements = document.querySelectorAll(selector);
            return elements.length ? elements[0] : null;
        };
    }

    // querySelectorAll polyfill for IE7
    if (!document.querySelectorAll) {
        document.querySelectorAll = function(selector) {
            var style = document.createElement('style');
            var elements = [];
            var element;
            var i;
            
            document.documentElement.firstChild.appendChild(style);
            document._qsa = [];
            
            style.styleSheet.cssText = selector + '{x-qsa:expression(document._qsa && document._qsa.push(this))}';
            window.scrollBy(0, 0);
            style.parentNode.removeChild(style);
            
            while (document._qsa.length) {
                element = document._qsa.shift();
                element.style.removeAttribute('x-qsa');
                elements.push(element);
            }
            document._qsa = null;
            return elements;
        };
    }

    // classList polyfill
    if (!("classList" in document.createElement("_"))) {
        (function(view) {
            "use strict";
            if (!('Element' in view)) return;
            var classListProp = "classList",
                protoProp = "prototype",
                elemCtrProto = view.Element[protoProp],
                objCtr = Object,
                strTrim = String[protoProp].trim || function() {
                    return this.replace(/^\s+|\s+$/g, "");
                },
                arrIndexOf = Array[protoProp].indexOf || function(item) {
                    var i = 0,
                        len = this.length;
                    for (; i < len; i++) {
                        if (i in this && this[i] === item) {
                            return i;
                        }
                    }
                    return -1;
                },
                DOMTokenList = function(el) {
                    this.el = el;
                    var classes = el.className.replace(/^\s+|\s+$/g, '').split(/\s+/);
                    for (var i = 0; i < classes.length; i++) {
                        this.push(classes[i]);
                    }
                    this._updateClassName = function() {
                        el.className = this.toString();
                    };
                },
                tokenListProto = DOMTokenList[protoProp] = [],
                tokenListGetter = function() {
                    return new DOMTokenList(this);
                };
            
            tokenListProto.item = function(i) {
                return this[i] || null;
            };
            tokenListProto.contains = function(token) {
                token += "";
                return arrIndexOf.call(this, token) !== -1;
            };
            tokenListProto.add = function() {
                var tokens = arguments,
                    i = 0,
                    l = tokens.length,
                    token, updated = false;
                do {
                    token = tokens[i] + "";
                    if (arrIndexOf.call(this, token) === -1) {
                        this.push(token);
                        updated = true;
                    }
                } while (++i < l);
                if (updated) {
                    this._updateClassName();
                }
            };
            tokenListProto.remove = function() {
                var tokens = arguments,
                    i = 0,
                    l = tokens.length,
                    token, updated = false, index;
                do {
                    token = tokens[i] + "";
                    index = arrIndexOf.call(this, token);
                    while (index !== -1) {
                        this.splice(index, 1);
                        updated = true;
                        index = arrIndexOf.call(this, token);
                    }
                } while (++i < l);
                if (updated) {
                    this._updateClassName();
                }
            };
            tokenListProto.toggle = function(token, force) {
                token += "";
                var result = this.contains(token),
                    method = result ? force !== true && "remove" : force !== false && "add";
                if (method) {
                    this[method](token);
                }
                if (force === true || force === false) {
                    return force;
                } else {
                    return !result;
                }
            };
            tokenListProto.toString = function() {
                return this.join(" ");
            };
            
            if (objCtr.defineProperty) {
                var defineProperty = function(object, name, definition) {
                    if (definition.get) {
                        objCtr.defineProperty(object, name, definition);
                    }
                };
                defineProperty(elemCtrProto, classListProp, {
                    get: tokenListGetter,
                    enumerable: true,
                    configurable: true
                });
            } else if (objCtr[protoProp].__defineGetter__) {
                elemCtrProto.__defineGetter__(classListProp, tokenListGetter);
            }
        }(window));
    }

    // Initialize compatibility features
    function init() {
        addBrowserClasses();
        addFeatureClasses();
        
        // Add skip link for accessibility
        var skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.className = 'skip-link';
        skipLink.textContent = 'Skip to main content';
        document.body.insertBefore(skipLink, document.body.firstChild);
        
        // Add main content ID if not exists
        var mainContent = document.querySelector('.main-content') || document.querySelector('main') || document.querySelector('#content');
        if (mainContent && !mainContent.id) {
            mainContent.id = 'main-content';
        }
    }

    // Run initialization when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Modern JavaScript fallbacks
    var modernJSFallbacks = {
        // Fetch API polyfill
        initFetch: function() {
            if (!window.fetch) {
                window.fetch = function(url, options) {
                    return new Promise(function(resolve, reject) {
                        var xhr = new XMLHttpRequest();
                        options = options || {};

                        xhr.open(options.method || 'GET', url);

                        if (options.headers) {
                            Object.keys(options.headers).forEach(function(key) {
                                xhr.setRequestHeader(key, options.headers[key]);
                            });
                        }

                        xhr.onload = function() {
                            resolve({
                                ok: xhr.status >= 200 && xhr.status < 300,
                                status: xhr.status,
                                statusText: xhr.statusText,
                                text: function() {
                                    return Promise.resolve(xhr.responseText);
                                },
                                json: function() {
                                    return Promise.resolve(JSON.parse(xhr.responseText));
                                }
                            });
                        };

                        xhr.onerror = function() {
                            reject(new Error('Network error'));
                        };

                        xhr.send(options.body);
                    });
                };
            }
        },

        // Promise polyfill
        initPromise: function() {
            if (!window.Promise) {
                window.Promise = function(executor) {
                    var self = this;
                    self.state = 'pending';
                    self.value = undefined;
                    self.handlers = [];

                    function resolve(result) {
                        if (self.state === 'pending') {
                            self.state = 'fulfilled';
                            self.value = result;
                            self.handlers.forEach(handle);
                            self.handlers = null;
                        }
                    }

                    function reject(error) {
                        if (self.state === 'pending') {
                            self.state = 'rejected';
                            self.value = error;
                            self.handlers.forEach(handle);
                            self.handlers = null;
                        }
                    }

                    function handle(handler) {
                        if (self.state === 'pending') {
                            self.handlers.push(handler);
                        } else {
                            if (self.state === 'fulfilled' && typeof handler.onFulfilled === 'function') {
                                handler.onFulfilled(self.value);
                            }
                            if (self.state === 'rejected' && typeof handler.onRejected === 'function') {
                                handler.onRejected(self.value);
                            }
                        }
                    }

                    this.then = function(onFulfilled, onRejected) {
                        return new Promise(function(resolve, reject) {
                            handle({
                                onFulfilled: function(result) {
                                    try {
                                        resolve(onFulfilled ? onFulfilled(result) : result);
                                    } catch (ex) {
                                        reject(ex);
                                    }
                                },
                                onRejected: function(error) {
                                    try {
                                        resolve(onRejected ? onRejected(error) : error);
                                    } catch (ex) {
                                        reject(ex);
                                    }
                                }
                            });
                        });
                    };

                    this.catch = function(onRejected) {
                        return this.then(null, onRejected);
                    };

                    executor(resolve, reject);
                };

                Promise.resolve = function(value) {
                    return new Promise(function(resolve) {
                        resolve(value);
                    });
                };

                Promise.reject = function(reason) {
                    return new Promise(function(resolve, reject) {
                        reject(reason);
                    });
                };
            }
        }
    };

    // Initialize modern JS fallbacks
    modernJSFallbacks.initPromise();
    modernJSFallbacks.initFetch();

    // Export for global use
    window.BrowserCompatibility = {
        detection: browserDetection,
        features: featureDetection,
        modernJS: modernJSFallbacks,
        init: init
    };

})();
