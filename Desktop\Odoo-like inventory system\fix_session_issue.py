#!/usr/bin/env python
"""
Fix session consistency issue by cleaning up old sessions
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.contrib.sessions.models import Session
from users.models import User
from django.utils import timezone

def fix_session_issue():
    """Clean up old sessions and ensure consistent user experience"""
    print("🔧 Fixing Session Consistency Issue...\n")
    
    try:
        # Show current sessions
        print("📊 Current Active Sessions:")
        sessions = Session.objects.all()
        
        session_users = {}
        for session in sessions:
            session_data = session.get_decoded()
            user_id = session_data.get('_auth_user_id')
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                    if user.username not in session_users:
                        session_users[user.username] = []
                    session_users[user.username].append({
                        'session_key': session.session_key,
                        'expires': session.expire_date,
                        'user_id': user_id
                    })
                except User.DoesNotExist:
                    print(f"   - Unknown user ID: {user_id}")
        
        for username, user_sessions in session_users.items():
            print(f"   - {username}: {len(user_sessions)} sessions")
            for i, session in enumerate(user_sessions):
                print(f"     Session {i+1}: Expires {session['expires']}")
        
        # Clean up old admin sessions (keep only the most recent one)
        print(f"\n🧹 Cleaning Up Old Sessions:")
        
        cleaned_sessions = 0
        
        for username, user_sessions in session_users.items():
            if len(user_sessions) > 1:
                # Sort by expiration date (most recent first)
                user_sessions.sort(key=lambda x: x['expires'], reverse=True)
                
                # Keep only the most recent session, delete the rest
                sessions_to_delete = user_sessions[1:]  # All except the first (most recent)
                
                for session_info in sessions_to_delete:
                    try:
                        session = Session.objects.get(session_key=session_info['session_key'])
                        session.delete()
                        cleaned_sessions += 1
                        print(f"   ✅ Deleted old session for {username}")
                    except Session.DoesNotExist:
                        print(f"   ⚠️  Session already deleted for {username}")
        
        print(f"\n📊 Cleanup Results:")
        print(f"   Sessions cleaned: {cleaned_sessions}")
        
        # Show remaining sessions
        remaining_sessions = Session.objects.all()
        print(f"   Remaining sessions: {remaining_sessions.count()}")
        
        for session in remaining_sessions:
            session_data = session.get_decoded()
            user_id = session_data.get('_auth_user_id')
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                    print(f"   - {user.username}: Expires {session.expire_date}")
                except User.DoesNotExist:
                    print(f"   - Unknown user: Expires {session.expire_date}")
        
        # Additional cleanup: Remove expired sessions
        print(f"\n🗑️  Removing Expired Sessions:")
        expired_sessions = Session.objects.filter(expire_date__lt=timezone.now())
        expired_count = expired_sessions.count()
        
        if expired_count > 0:
            expired_sessions.delete()
            print(f"   ✅ Removed {expired_count} expired sessions")
        else:
            print(f"   ✅ No expired sessions found")
        
        # Final session count
        final_sessions = Session.objects.all()
        print(f"\n🎯 Final Session State:")
        print(f"   Total active sessions: {final_sessions.count()}")
        
        for session in final_sessions:
            session_data = session.get_decoded()
            user_id = session_data.get('_auth_user_id')
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                    print(f"   - {user.username} (ID: {user_id})")
                    print(f"     Expires: {session.expire_date}")
                except User.DoesNotExist:
                    print(f"   - Unknown user ID: {user_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing session issue: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_solution():
    """Provide solution steps for the user"""
    print(f"\n💡 Solution for Consistent User Display:")
    print(f"")
    print(f"🔧 What was the problem:")
    print(f"   - Multiple active sessions for different users")
    print(f"   - Browser switching between admin and kenny sessions")
    print(f"   - Inconsistent user display across pages")
    print(f"")
    print(f"✅ What has been fixed:")
    print(f"   - Cleaned up old duplicate sessions")
    print(f"   - Removed expired sessions")
    print(f"   - Ensured single session per user")
    print(f"")
    print(f"🎯 Next steps:")
    print(f"   1. Clear your browser cache and cookies")
    print(f"   2. Close all browser tabs for the inventory system")
    print(f"   3. Open a new browser window/tab")
    print(f"   4. Go to: http://127.0.0.1:8000/login/")
    print(f"   5. Login with: kenny / InventoryAdmin2025!")
    print(f"   6. You should now see 'kenny' consistently across all pages")
    print(f"")
    print(f"🔍 If the issue persists:")
    print(f"   - Try using an incognito/private browser window")
    print(f"   - Or use a different browser")
    print(f"   - This will ensure a fresh session")

if __name__ == "__main__":
    print("🚀 Session Consistency Fix\n")
    
    success = fix_session_issue()
    provide_solution()
    
    print(f"\n📋 Final Results:")
    if success:
        print(f"🎉 Session cleanup completed successfully!")
        print(f"✅ User display should now be consistent")
        print(f"✅ Follow the next steps above to test")
    else:
        print(f"⚠️  Session cleanup encountered issues")
        print(f"🔧 Manual session cleanup may be needed")
