#!/usr/bin/env python
"""
Test authentication setup
"""

import os
import sys
import django
from django.test import Client
from django.urls import reverse

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

try:
    from users.models import User
    from django.contrib.auth import authenticate
    
    print("🔐 Testing Authentication Setup...")
    
    # Test 1: Check if superuser exists
    superusers = User.objects.filter(is_superuser=True)
    print(f"✅ Superusers found: {superusers.count()}")
    for user in superusers:
        print(f"   - {user.username} (Active: {user.is_active}, Employee: {user.is_active_employee})")
    
    # Test 2: Check if we can authenticate with existing user
    if superusers.exists():
        test_user = superusers.first()
        print(f"\n🔑 Testing authentication for user: {test_user.username}")
        
        # Test with Django's test client
        client = Client()
        
        # Test login page access
        login_url = '/login/'
        response = client.get(login_url)
        print(f"✅ Login page status: {response.status_code}")
        
        # Test dashboard redirect (should redirect to login)
        dashboard_url = '/dashboard/'
        response = client.get(dashboard_url)
        print(f"✅ Dashboard redirect status: {response.status_code}")
        if response.status_code == 302:
            print(f"   Redirected to: {response.url}")
        
    # Test 3: Check URL configuration
    from django.urls import resolve
    try:
        login_resolver = resolve('/login/')
        print(f"✅ Login URL resolves to: {login_resolver.func.__name__}")
    except Exception as e:
        print(f"❌ Login URL resolution error: {e}")
    
    try:
        dashboard_resolver = resolve('/dashboard/')
        print(f"✅ Dashboard URL resolves to: {dashboard_resolver.func.__name__}")
    except Exception as e:
        print(f"❌ Dashboard URL resolution error: {e}")
    
    print("\n🎉 Authentication system appears to be properly configured!")
    print("\n📋 LOGIN CREDENTIALS:")
    print("   Username: kenny")
    print("   Password: InventoryAdmin2025!")
    print("\n🌐 LOGIN URL: http://127.0.0.1:8000/login/")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
