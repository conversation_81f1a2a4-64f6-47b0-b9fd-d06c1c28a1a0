from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.db.models import Q
from .models import User, UserProfile
from .forms import UserRegistrationForm, UserUpdateForm, UserProfileForm, LoginForm


def login_view(request):
    """Custom login view"""
    if request.user.is_authenticated:
        return redirect('inventory:dashboard')
    
    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            user = authenticate(request, username=username, password=password)
            
            if user is not None:
                if user.is_active_employee:
                    login(request, user)
                    # Update last login IP
                    user.last_login_ip = get_client_ip(request)
                    user.save(update_fields=['last_login_ip'])
                    
                    next_url = request.GET.get('next', 'inventory:dashboard')
                    return redirect(next_url)
                else:
                    messages.error(request, 'Your account has been deactivated.')
            else:
                messages.error(request, 'Invalid username or password.')
    else:
        form = LoginForm()
    
    return render(request, 'users/login.html', {'form': form})


def logout_view(request):
    """Custom logout view"""
    logout(request)
    messages.success(request, 'You have been successfully logged out.')
    return redirect('users:login')


@login_required
def profile_view(request):
    """User profile view"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    
    if request.method == 'POST':
        user_form = UserUpdateForm(request.POST, request.FILES, instance=request.user)
        profile_form = UserProfileForm(request.POST, instance=profile)
        
        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()
            messages.success(request, 'Your profile has been updated successfully.')
            return redirect('users:profile')
    else:
        user_form = UserUpdateForm(instance=request.user)
        profile_form = UserProfileForm(instance=profile)
    
    context = {
        'user_form': user_form,
        'profile_form': profile_form,
    }
    return render(request, 'users/profile.html', context)


def is_admin_or_manager(user):
    """Check if user is admin or manager"""
    return user.is_authenticated and user.is_manager


class UserListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """List all users (admin/manager only)"""
    model = User
    template_name = 'users/user_list.html'
    context_object_name = 'users'
    paginate_by = 20
    
    def test_func(self):
        return self.request.user.is_manager
    
    def get_queryset(self):
        queryset = User.objects.select_related('profile').all()
        search = self.request.GET.get('search')
        role = self.request.GET.get('role')
        
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search)
            )
        
        if role:
            queryset = queryset.filter(role=role)
        
        return queryset.order_by('username')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['roles'] = User.USER_ROLES
        context['current_search'] = self.request.GET.get('search', '')
        context['current_role'] = self.request.GET.get('role', '')
        return context


class UserDetailView(LoginRequiredMixin, UserPassesTestMixin, DetailView):
    """User detail view (admin/manager only)"""
    model = User
    template_name = 'users/user_detail.html'
    context_object_name = 'user_obj'
    
    def test_func(self):
        return self.request.user.is_manager


class UserCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """Create new user (admin only)"""
    model = User
    form_class = UserRegistrationForm
    template_name = 'users/user_form.html'
    success_url = reverse_lazy('users:list')
    
    def test_func(self):
        return self.request.user.is_admin
    
    def form_valid(self, form):
        messages.success(self.request, f'User {form.instance.username} created successfully.')
        return super().form_valid(form)


class UserUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """Update user (admin only)"""
    model = User
    form_class = UserUpdateForm
    template_name = 'users/user_form.html'
    success_url = reverse_lazy('users:list')
    
    def test_func(self):
        return self.request.user.is_admin
    
    def form_valid(self, form):
        messages.success(self.request, f'User {form.instance.username} updated successfully.')
        return super().form_valid(form)


@login_required
@user_passes_test(is_admin_or_manager)
def toggle_user_status(request, pk):
    """Toggle user active status"""
    user = get_object_or_404(User, pk=pk)
    
    if request.user.is_admin:
        user.is_active_employee = not user.is_active_employee
        user.save()
        
        status = "activated" if user.is_active_employee else "deactivated"
        messages.success(request, f'User {user.username} has been {status}.')
    else:
        messages.error(request, 'You do not have permission to perform this action.')
    
    return redirect('users:list')


def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


# Permission decorators
def admin_required(view_func):
    """Decorator to require admin role"""
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated or not request.user.is_admin:
            messages.error(request, 'You do not have permission to access this page.')
            return redirect('inventory:dashboard')
        return view_func(request, *args, **kwargs)
    return wrapper


def manager_required(view_func):
    """Decorator to require manager role or higher"""
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated or not request.user.is_manager:
            messages.error(request, 'You do not have permission to access this page.')
            return redirect('inventory:dashboard')
        return view_func(request, *args, **kwargs)
    return wrapper


def inventory_access_required(view_func):
    """Decorator to require inventory management access"""
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated or not request.user.can_manage_inventory:
            messages.error(request, 'You do not have permission to access this page.')
            return redirect('inventory:dashboard')
        return view_func(request, *args, **kwargs)
    return wrapper


def sales_access_required(view_func):
    """Decorator to require sales management access"""
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated or not request.user.can_manage_sales:
            messages.error(request, 'You do not have permission to access this page.')
            return redirect('inventory:dashboard')
        return view_func(request, *args, **kwargs)
    return wrapper


def accounting_access_required(view_func):
    """Decorator to require accounting access"""
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated or not request.user.can_view_accounting:
            messages.error(request, 'You do not have permission to access this page.')
            return redirect('inventory:dashboard')
        return view_func(request, *args, **kwargs)
    return wrapper
