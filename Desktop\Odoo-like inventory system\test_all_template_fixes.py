#!/usr/bin/env python
"""
Test all template fixes for VariableDoesNotExist errors
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from products.models import Product
from warehouses.models import Warehouse
from users.models import User

def check_database_state():
    """Check the current state of the database"""
    print("🔍 Checking Database State...\n")
    
    try:
        # Check products
        products = Product.objects.all()
        products_with_creator = Product.objects.filter(created_by__isnull=False)
        products_without_creator = Product.objects.filter(created_by__isnull=True)
        
        print(f"📦 Products:")
        print(f"   Total: {products.count()}")
        print(f"   With creator: {products_with_creator.count()}")
        print(f"   Without creator: {products_without_creator.count()}")
        
        # Check warehouses
        warehouses = Warehouse.objects.all()
        warehouses_with_manager = Warehouse.objects.filter(manager__isnull=False)
        warehouses_without_manager = Warehouse.objects.filter(manager__isnull=True)
        
        print(f"\n🏢 Warehouses:")
        print(f"   Total: {warehouses.count()}")
        print(f"   With manager: {warehouses_with_manager.count()}")
        print(f"   Without manager: {warehouses_without_manager.count()}")
        
        # Check users
        users = User.objects.all()
        print(f"\n👥 Users:")
        print(f"   Total: {users.count()}")
        
        return {
            'products_without_creator': products_without_creator.count(),
            'warehouses_without_manager': warehouses_without_manager.count(),
            'total_products': products.count(),
            'total_warehouses': warehouses.count(),
        }
        
    except Exception as e:
        print(f"❌ Error checking database state: {e}")
        return None

def test_template_fixes():
    """Test that template fixes handle null foreign keys correctly"""
    print(f"\n🎨 Testing Template Fixes:")
    
    try:
        # Test scenarios that were causing errors
        test_cases = []
        
        # Test products without creators
        products_without_creator = Product.objects.filter(created_by__isnull=True)
        if products_without_creator.exists():
            product = products_without_creator.first()
            test_cases.append({
                'type': 'Product Detail',
                'url': f'/products/{product.id}/',
                'description': f'Product "{product.name}" without creator',
                'null_field': 'created_by'
            })
        
        # Test warehouses without managers
        warehouses_without_manager = Warehouse.objects.filter(manager__isnull=True)
        if warehouses_without_manager.exists():
            warehouse = warehouses_without_manager.first()
            test_cases.append({
                'type': 'Warehouse Detail',
                'url': f'/warehouses/{warehouse.id}/',
                'description': f'Warehouse "{warehouse.name}" without manager',
                'null_field': 'manager'
            })
            
            test_cases.append({
                'type': 'Warehouse List',
                'url': '/warehouses/list/',
                'description': 'Warehouse list with warehouses without managers',
                'null_field': 'manager'
            })
        
        print(f"   Found {len(test_cases)} test scenarios:")
        
        for i, case in enumerate(test_cases, 1):
            print(f"   {i}. {case['type']}: {case['description']}")
            print(f"      URL: {case['url']}")
            print(f"      Null field: {case['null_field']}")
        
        return len(test_cases) > 0
        
    except Exception as e:
        print(f"❌ Error testing template fixes: {e}")
        return False

def check_template_patterns():
    """Check for potential template issues in all templates"""
    print(f"\n🔍 Checking Template Patterns:")
    
    try:
        import os
        import re
        
        template_dir = r"Desktop\Odoo-like inventory system\templates"
        potential_issues = []
        
        # Patterns that might cause issues
        risky_patterns = [
            r'\.username(?!\s*%})',  # .username not in template tags
            r'\.get_full_name(?!\s*%})',  # .get_full_name not in template tags
            r'\.email(?!\s*%})',  # .email not in template tags
            r'\.manager\.(?!.*if)',  # .manager. without if check
            r'\.created_by\.(?!.*if)',  # .created_by. without if check
            r'\.updated_by\.(?!.*if)',  # .updated_by. without if check
        ]
        
        # Walk through template directories
        for root, dirs, files in os.walk(template_dir):
            for file in files:
                if file.endswith('.html'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                        for pattern in risky_patterns:
                            matches = re.finditer(pattern, content, re.IGNORECASE)
                            for match in matches:
                                # Get line number
                                line_num = content[:match.start()].count('\n') + 1
                                line_content = content.split('\n')[line_num - 1].strip()
                                
                                potential_issues.append({
                                    'file': file_path.replace(template_dir, '').replace('\\', '/'),
                                    'line': line_num,
                                    'pattern': pattern,
                                    'content': line_content[:100] + '...' if len(line_content) > 100 else line_content
                                })
                    except Exception as e:
                        print(f"   ⚠️  Could not read {file}: {e}")
        
        print(f"   Scanned template files for risky patterns")
        print(f"   Found {len(potential_issues)} potential issues:")
        
        for issue in potential_issues:
            print(f"   ⚠️  {issue['file']}:{issue['line']}")
            print(f"      Pattern: {issue['pattern']}")
            print(f"      Content: {issue['content']}")
        
        return len(potential_issues) == 0
        
    except Exception as e:
        print(f"❌ Error checking template patterns: {e}")
        return False

def test_fixed_templates():
    """Test that the specific templates we fixed are working"""
    print(f"\n✅ Testing Fixed Templates:")
    
    try:
        from django.template.loader import get_template
        from django.template import Context
        
        # Test templates we fixed
        fixed_templates = [
            'products/product_detail.html',
            'warehouses/warehouse_detail.html', 
            'warehouses/warehouse_list.html',
            'products/product_variants.html',  # Also created this one
        ]
        
        results = []
        
        for template_name in fixed_templates:
            try:
                template = get_template(template_name)
                print(f"   ✅ {template_name}: Template loads successfully")
                results.append(True)
            except Exception as e:
                print(f"   ❌ {template_name}: Error loading - {e}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ Error testing fixed templates: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Comprehensive Template Fix Testing\n")
    
    db_state = check_database_state()
    template_tests = test_template_fixes()
    pattern_check = check_template_patterns()
    fixed_templates = test_fixed_templates()
    
    print(f"\n📋 Test Results:")
    print(f"   Database State: {'✅ CHECKED' if db_state else '❌ FAILED'}")
    print(f"   Template Tests: {'✅ SCENARIOS FOUND' if template_tests else '⚠️  NO SCENARIOS'}")
    print(f"   Pattern Check: {'✅ CLEAN' if pattern_check else '⚠️  ISSUES FOUND'}")
    print(f"   Fixed Templates: {'✅ ALL LOAD' if fixed_templates else '❌ SOME FAILED'}")
    
    if db_state and fixed_templates:
        print(f"\n🎉 Template fixes successful!")
        print(f"✅ Fixed VariableDoesNotExist errors in:")
        print(f"   - Product detail page (created_by field)")
        print(f"   - Warehouse detail page (manager field)")
        print(f"   - Warehouse list page (manager field)")
        print(f"   - Created missing product variants template")
        print(f"\n🌐 Test these URLs:")
        
        if db_state['total_products'] > 0:
            print(f"   📦 Product Detail: http://127.0.0.1:8000/products/1/")
            print(f"   🔧 Product Variants: http://127.0.0.1:8000/products/1/variants/")
        
        if db_state['total_warehouses'] > 0:
            print(f"   🏢 Warehouse Detail: http://127.0.0.1:8000/warehouses/1/")
            print(f"   📋 Warehouse List: http://127.0.0.1:8000/warehouses/list/")
        
        print(f"\n✨ All pages should now load without VariableDoesNotExist errors!")
    else:
        print(f"\n⚠️  Some issues may remain")
        print(f"🔧 Review the test results above")
