<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Compatibility Test - Inventory Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>


<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Browser Compatibility Test</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-secondary" onclick="runAllTests()">
                        <i class="fas fa-play"></i> Run All Tests
                    </button>
                </div>
            </div>

            <!-- Browser Information -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle"></i> Browser Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>User Agent:</strong></td>
                                    <td id="userAgent">Loading...</td>
                                </tr>
                                <tr>
                                    <td><strong>Browser:</strong></td>
                                    <td id="browserName">Loading...</td>
                                </tr>
                                <tr>
                                    <td><strong>Version:</strong></td>
                                    <td id="browserVersion">Loading...</td>
                                </tr>
                                <tr>
                                    <td><strong>Platform:</strong></td>
                                    <td id="platform">Loading...</td>
                                </tr>
                                <tr>
                                    <td><strong>Screen Resolution:</strong></td>
                                    <td id="screenResolution">Loading...</td>
                                </tr>
                                <tr>
                                    <td><strong>Viewport Size:</strong></td>
                                    <td id="viewportSize">Loading...</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-check-circle"></i> Feature Support</h5>
                        </div>
                        <div class="card-body">
                            <div id="featureSupport">
                                <div class="d-flex justify-content-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- CSS Feature Tests -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-palette"></i> CSS Feature Tests</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6>Flexbox Test</h6>
                                    <div class="test-flexbox d-flex justify-content-between align-items-center p-2 border rounded mb-3">
                                        <span>Left</span>
                                        <span>Center</span>
                                        <span>Right</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6>Grid Test</h6>
                                    <div class="test-grid border rounded mb-3" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; padding: 10px;">
                                        <div class="bg-light p-2">Grid 1</div>
                                        <div class="bg-light p-2">Grid 2</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h6>Transform Test</h6>
                                    <div class="test-transform border rounded mb-3 p-2">
                                        <div class="bg-primary text-white p-2 text-center" style="transform: rotate(5deg);">
                                            Rotated Element
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- JavaScript Feature Tests -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-code"></i> JavaScript Feature Tests</h5>
                        </div>
                        <div class="card-body">
                            <div id="jsTestResults">
                                <div class="d-flex justify-content-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Elements Test -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-wpforms"></i> Form Elements Test</h5>
                        </div>
                        <div class="card-body">
                            <form class="row g-3">
                                <div class="col-md-4">
                                    <label for="testInput" class="form-label">Text Input</label>
                                    <input type="text" class="form-control" id="testInput" placeholder="Test input">
                                </div>
                                <div class="col-md-4">
                                    <label for="testSelect" class="form-label">Select</label>
                                    <select class="form-select" id="testSelect">
                                        <option selected>Choose...</option>
                                        <option value="1">Option 1</option>
                                        <option value="2">Option 2</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="testDate" class="form-label">Date Input</label>
                                    <input type="date" class="form-control" id="testDate">
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="testCheck">
                                        <label class="form-check-label" for="testCheck">
                                            Test checkbox
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="button" class="btn btn-primary">Test Button</button>
                                    <button type="button" class="btn btn-outline-secondary">Outline Button</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Export Functionality Test -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-download"></i> Export Functionality Test</h5>
                        </div>
                        <div class="card-body">
                            {% include 'components/export_buttons.html' with page_title='Browser Test Report' %}
                            <p class="mt-3">Test the export and print functionality using the buttons above.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Results Summary -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Test Results Summary</h5>
                        </div>
                        <div class="card-body">
                            <div id="testSummary">
                                <p>Click "Run All Tests" to see compatibility results.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadBrowserInfo();
    loadFeatureSupport();
    runJavaScriptTests();
});

function loadBrowserInfo() {
    document.getElementById('userAgent').textContent = navigator.userAgent;
    document.getElementById('platform').textContent = navigator.platform;
    document.getElementById('screenResolution').textContent = screen.width + 'x' + screen.height;
    document.getElementById('viewportSize').textContent = window.innerWidth + 'x' + window.innerHeight;
    
    // Detect browser
    var browserInfo = getBrowserInfo();
    document.getElementById('browserName').textContent = browserInfo.name;
    document.getElementById('browserVersion').textContent = browserInfo.version;
}

function getBrowserInfo() {
    var ua = navigator.userAgent;
    var browser = {name: 'Unknown', version: 'Unknown'};
    
    if (ua.indexOf('Chrome') > -1 && ua.indexOf('Edge') === -1) {
        browser.name = 'Chrome';
        browser.version = ua.match(/Chrome\/([0-9.]+)/)[1];
    } else if (ua.indexOf('Firefox') > -1) {
        browser.name = 'Firefox';
        browser.version = ua.match(/Firefox\/([0-9.]+)/)[1];
    } else if (ua.indexOf('Safari') > -1 && ua.indexOf('Chrome') === -1) {
        browser.name = 'Safari';
        browser.version = ua.match(/Version\/([0-9.]+)/)[1];
    } else if (ua.indexOf('Edge') > -1) {
        browser.name = 'Edge';
        browser.version = ua.match(/Edge\/([0-9.]+)/)[1];
    } else if (ua.indexOf('MSIE') > -1 || ua.indexOf('Trident') > -1) {
        browser.name = 'Internet Explorer';
        browser.version = ua.match(/(?:MSIE |rv:)([0-9.]+)/)[1];
    }
    
    return browser;
}

function loadFeatureSupport() {
    var features = [
        {name: 'Flexbox', supported: CSS.supports('display', 'flex')},
        {name: 'CSS Grid', supported: CSS.supports('display', 'grid')},
        {name: 'Local Storage', supported: typeof(Storage) !== 'undefined'},
        {name: 'Session Storage', supported: typeof(Storage) !== 'undefined'},
        {name: 'Geolocation', supported: 'geolocation' in navigator},
        {name: 'Canvas', supported: !!document.createElement('canvas').getContext},
        {name: 'WebGL', supported: !!window.WebGLRenderingContext},
        {name: 'Touch Events', supported: 'ontouchstart' in window},
        {name: 'Service Workers', supported: 'serviceWorker' in navigator},
        {name: 'Web Workers', supported: typeof(Worker) !== 'undefined'}
    ];
    
    var html = '<div class="row">';
    features.forEach(function(feature, index) {
        var icon = feature.supported ? 'fas fa-check text-success' : 'fas fa-times text-danger';
        var status = feature.supported ? 'Supported' : 'Not Supported';
        
        html += '<div class="col-md-6 mb-2">';
        html += '<i class="' + icon + '"></i> ';
        html += '<strong>' + feature.name + ':</strong> ' + status;
        html += '</div>';
        
        if ((index + 1) % 2 === 0) {
            html += '</div><div class="row">';
        }
    });
    html += '</div>';
    
    document.getElementById('featureSupport').innerHTML = html;
}

function runJavaScriptTests() {
    var tests = [
        {name: 'Array.forEach', test: function() { return Array.prototype.forEach !== undefined; }},
        {name: 'Array.indexOf', test: function() { return Array.prototype.indexOf !== undefined; }},
        {name: 'Object.keys', test: function() { return Object.keys !== undefined; }},
        {name: 'JSON.parse', test: function() { return JSON && JSON.parse !== undefined; }},
        {name: 'JSON.stringify', test: function() { return JSON && JSON.stringify !== undefined; }},
        {name: 'addEventListener', test: function() { return window.addEventListener !== undefined; }},
        {name: 'querySelector', test: function() { return document.querySelector !== undefined; }},
        {name: 'querySelectorAll', test: function() { return document.querySelectorAll !== undefined; }},
        {name: 'classList', test: function() { return document.createElement('div').classList !== undefined; }},
        {name: 'Promise', test: function() { return window.Promise !== undefined; }},
        {name: 'Fetch API', test: function() { return window.fetch !== undefined; }},
        {name: 'Arrow Functions', test: function() { try { eval('() => {}'); return true; } catch(e) { return false; } }}
    ];
    
    var html = '<div class="row">';
    var passedTests = 0;
    
    tests.forEach(function(test, index) {
        var passed = test.test();
        if (passed) passedTests++;
        
        var icon = passed ? 'fas fa-check text-success' : 'fas fa-times text-danger';
        var status = passed ? 'Pass' : 'Fail';
        
        html += '<div class="col-md-6 mb-2">';
        html += '<i class="' + icon + '"></i> ';
        html += '<strong>' + test.name + ':</strong> ' + status;
        html += '</div>';
        
        if ((index + 1) % 2 === 0) {
            html += '</div><div class="row">';
        }
    });
    html += '</div>';
    
    html += '<div class="mt-3 p-3 bg-light rounded">';
    html += '<strong>JavaScript Compatibility Score: ' + passedTests + '/' + tests.length + ' (' + Math.round((passedTests/tests.length)*100) + '%)</strong>';
    html += '</div>';
    
    document.getElementById('jsTestResults').innerHTML = html;
}

function runAllTests() {
    loadBrowserInfo();
    loadFeatureSupport();
    runJavaScriptTests();
    
    // Generate summary
    var browserInfo = getBrowserInfo();
    var summary = '<div class="alert alert-info">';
    summary += '<h6>Browser Compatibility Summary</h6>';
    summary += '<p><strong>Browser:</strong> ' + browserInfo.name + ' ' + browserInfo.version + '</p>';
    summary += '<p><strong>Overall Compatibility:</strong> ';
    
    if (browserInfo.name === 'Chrome' || browserInfo.name === 'Firefox' || browserInfo.name === 'Safari' || browserInfo.name === 'Edge') {
        summary += '<span class="badge bg-success">Excellent</span>';
    } else if (browserInfo.name === 'Internet Explorer') {
        summary += '<span class="badge bg-warning">Limited (Polyfills Active)</span>';
    } else {
        summary += '<span class="badge bg-secondary">Unknown</span>';
    }
    
    summary += '</p>';
    summary += '<p class="mb-0">All tests completed successfully. The inventory management system should work properly in your browser.</p>';
    summary += '</div>';
    
    document.getElementById('testSummary').innerHTML = summary;
}
</script>
</body>
</html>
