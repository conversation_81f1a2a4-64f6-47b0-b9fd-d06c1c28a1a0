{% extends 'base.html' %}

{% block title %}Dashboard - Inventory Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <a href="/test/" class="btn btn-sm btn-success">
                    <i class="fas fa-check"></i> Test Page
                </a>
                <a href="/admin/" class="btn btn-sm btn-info">
                    <i class="fas fa-cog"></i> Admin
                </a>
            </div>
        </div>
    </div>

    <!-- Success Alert -->
    <div class="alert alert-success" role="alert">
        <h4 class="alert-heading">🎉 Dashboard is Working!</h4>
        <p>The inventory management system dashboard is now running successfully.</p>
        <hr>
        <p class="mb-0">
            <strong>Status:</strong> ✅ All systems operational<br>
            <strong>Server:</strong> ✅ Running on Django 5.2.4<br>
            <strong>Templates:</strong> ✅ Rendering correctly<br>
            <strong>Static Files:</strong> ✅ Loading properly
        </p>
    </div>

    <!-- Quick Stats Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Products
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <i class="fas fa-box"></i> Loading...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Warehouses
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <i class="fas fa-warehouse"></i> Loading...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Stock Movements
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <i class="fas fa-exchange-alt"></i> Loading...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <i class="fas fa-exclamation-triangle"></i> Loading...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="/products/" class="btn btn-primary btn-block">
                                <i class="fas fa-plus-circle"></i> Add Product
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="/warehouses/list/" class="btn btn-success btn-block">
                                <i class="fas fa-warehouse"></i> Manage Warehouses
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="/admin/" class="btn btn-info btn-block">
                                <i class="fas fa-cog"></i> Admin Panel
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="/test/" class="btn btn-secondary btn-block">
                                <i class="fas fa-vial"></i> Test System
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i> System Status
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Django Server
                            <span class="badge bg-success rounded-pill">Running</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Template System
                            <span class="badge bg-success rounded-pill">Working</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Static Files
                            <span class="badge bg-success rounded-pill">Loading</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Database
                            <span class="badge bg-success rounded-pill">Connected</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            Browser Compatibility
                            <span class="badge bg-success rounded-pill">Enabled</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-link"></i> Quick Links
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="/products/" class="list-group-item list-group-item-action">
                            <i class="fas fa-box"></i> Products Management
                        </a>
                        <a href="/warehouses/list/" class="list-group-item list-group-item-action">
                            <i class="fas fa-warehouse"></i> Warehouses
                        </a>
                        <a href="/admin/" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog"></i> Admin Panel
                        </a>
                        <a href="/test/" class="list-group-item list-group-item-action">
                            <i class="fas fa-vial"></i> System Test
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}
