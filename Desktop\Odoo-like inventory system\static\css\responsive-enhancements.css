/* Responsive Design Enhancements for All Devices */

/* Base responsive utilities */
.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* Mobile-first approach */
@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
}

@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
}

/* Mobile optimizations */
@media (max-width: 767.98px) {
    /* Stack navigation items */
    .navbar-nav {
        flex-direction: column;
    }
    
    /* Full width buttons on mobile */
    .btn-mobile-full {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    /* Responsive tables */
    .table-responsive-mobile {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .table-responsive-mobile table {
        min-width: 600px;
    }
    
    /* Card adjustments */
    .card {
        margin-bottom: 1rem;
    }
    
    .card-columns {
        column-count: 1;
    }
    
    /* Form adjustments */
    .form-row {
        flex-direction: column;
    }
    
    .form-row .col {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    /* Hide less important columns on mobile */
    .d-mobile-none {
        display: none !important;
    }
    
    /* Sidebar adjustments */
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        transition: left 0.3s ease;
        z-index: 1050;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 1rem;
    }
    
    /* Export buttons mobile optimization */
    .export-buttons .dropdown-menu {
        position: static;
        float: none;
        width: 100%;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 991.98px) {
    .col-tablet-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .col-tablet-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
    
    .col-tablet-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    
    /* Adjust card columns for tablets */
    .card-columns {
        column-count: 2;
    }
}

/* Desktop optimizations */
@media (min-width: 992px) {
    .card-columns {
        column-count: 3;
    }
    
    /* Show desktop-only elements */
    .d-desktop-block {
        display: block !important;
    }
    
    .d-desktop-inline {
        display: inline !important;
    }
    
    .d-desktop-inline-block {
        display: inline-block !important;
    }
}

/* Large screen optimizations */
@media (min-width: 1200px) {
    .container-xl {
        max-width: 1140px;
    }
    
    .card-columns {
        column-count: 4;
    }
}

/* Ultra-wide screen optimizations */
@media (min-width: 1400px) {
    .container-xxl {
        max-width: 1320px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Crisp images and icons */
    .icon, .logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Larger touch targets */
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .form-control {
        min-height: 44px;
    }
    
    /* Remove hover effects on touch devices */
    .btn:hover {
        transform: none;
    }
    
    /* Improve dropdown usability */
    .dropdown-menu {
        font-size: 1.1rem;
    }
    
    .dropdown-item {
        padding: 0.75rem 1rem;
    }
}

/* Landscape phone optimizations */
@media (max-width: 767.98px) and (orientation: landscape) {
    .navbar {
        padding: 0.25rem 1rem;
    }
    
    .main-content {
        padding-top: 0.5rem;
    }
}

/* Print optimizations */
@media print {
    /* Hide interactive elements */
    .btn, .dropdown, .navbar, .sidebar {
        display: none !important;
    }
    
    /* Optimize layout for print */
    .container, .container-fluid {
        width: 100% !important;
        max-width: none !important;
    }
    
    .row {
        display: block !important;
    }
    
    .col-print-12 {
        width: 100% !important;
    }
    
    .col-print-6 {
        width: 50% !important;
        float: left !important;
    }
    
    .col-print-4 {
        width: 33.333% !important;
        float: left !important;
    }
    
    .col-print-3 {
        width: 25% !important;
        float: left !important;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

@media (prefers-contrast: high) {
    .btn {
        border-width: 2px;
    }
    
    .form-control {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bs-body-bg: #121212;
        --bs-body-color: #ffffff;
        --bs-card-bg: #1e1e1e;
        --bs-border-color: #404040;
    }
    
    .auto-dark-mode {
        background-color: var(--bs-body-bg);
        color: var(--bs-body-color);
    }
    
    .auto-dark-mode .card {
        background-color: var(--bs-card-bg);
        border-color: var(--bs-border-color);
    }
    
    .auto-dark-mode .form-control {
        background-color: var(--bs-card-bg);
        border-color: var(--bs-border-color);
        color: var(--bs-body-color);
    }
    
    .auto-dark-mode .table {
        color: var(--bs-body-color);
    }
    
    .auto-dark-mode .table-striped > tbody > tr:nth-of-type(odd) > td {
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* Flexible grid system */
.flex-grid {
    display: flex;
    flex-wrap: wrap;
    margin: -0.5rem;
}

.flex-grid-item {
    flex: 1 1 300px;
    margin: 0.5rem;
    min-width: 0;
}

/* Responsive utilities */
.text-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem);
}

.heading-responsive {
    font-size: clamp(1.25rem, 4vw, 2rem);
}

/* Container queries fallback */
.container-query-fallback {
    container-type: inline-size;
}

@supports (container-type: inline-size) {
    @container (max-width: 400px) {
        .cq-small {
            font-size: 0.875rem;
        }
    }
    
    @container (min-width: 401px) {
        .cq-large {
            font-size: 1.125rem;
        }
    }
}

/* Responsive images */
.img-responsive {
    max-width: 100%;
    height: auto;
}

/* Responsive embeds */
.embed-responsive {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    overflow: hidden;
}

.embed-responsive::before {
    display: block;
    content: "";
}

.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

.embed-responsive-21by9::before {
    padding-top: 42.857143%;
}

.embed-responsive-16by9::before {
    padding-top: 56.25%;
}

.embed-responsive-4by3::before {
    padding-top: 75%;
}

.embed-responsive-1by1::before {
    padding-top: 100%;
}
