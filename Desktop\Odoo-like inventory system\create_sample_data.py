#!/usr/bin/env python
"""
Sample Data Creation Script for Inventory Management System
This script creates sample data to demonstrate the system functionality.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from products.models import Category, Brand, Product
from warehouses.models import Warehouse, Location
from sales.models import Customer, SalesOrder, SalesOrderLine
from users.models import User

def create_sample_data():
    print("🚀 Creating sample data for your inventory system...")
    
    # Create Categories
    print("📦 Creating product categories...")
    electronics = Category.objects.get_or_create(
        name="Electronics",
        defaults={
            'description': "Electronic devices and components",
            'is_active': True
        }
    )[0]
    
    furniture = Category.objects.get_or_create(
        name="Furniture",
        defaults={
            'description': "Office and home furniture",
            'is_active': True
        }
    )[0]
    
    clothing = Category.objects.get_or_create(
        name="Clothing",
        defaults={
            'description': "Apparel and accessories",
            'is_active': True
        }
    )[0]
    
    # Create Brands
    print("🏷️ Creating brands...")
    apple = Brand.objects.get_or_create(
        name="Apple",
        defaults={
            'description': "Premium technology products",
            'is_active': True
        }
    )[0]
    
    samsung = Brand.objects.get_or_create(
        name="Samsung",
        defaults={
            'description': "Innovative electronics",
            'is_active': True
        }
    )[0]
    
    ikea = Brand.objects.get_or_create(
        name="IKEA",
        defaults={
            'description': "Affordable furniture solutions",
            'is_active': True
        }
    )[0]
    
    # Create Warehouses
    print("🏢 Creating warehouses...")
    main_warehouse = Warehouse.objects.get_or_create(
        code="WH001",
        defaults={
            'name': "Main Distribution Center",
            'address_line1': "123 Industrial Blvd",
            'city': "New York",
            'state': "NY",
            'postal_code': "10001",
            'country': "US",
            'warehouse_type': 'main',
            'is_active': True
        }
    )[0]

    retail_warehouse = Warehouse.objects.get_or_create(
        code="WH002",
        defaults={
            'name': "Retail Store Warehouse",
            'address_line1': "456 Commerce St",
            'city': "Los Angeles",
            'state': "CA",
            'postal_code': "90001",
            'country': "US",
            'warehouse_type': 'retail',
            'is_active': True
        }
    )[0]
    
    # Create Products
    print("📱 Creating products...")
    iphone = Product.objects.get_or_create(
        sku="IPHONE15-128-BLK",
        defaults={
            'name': "iPhone 15 128GB Black",
            'description': "Latest iPhone with advanced features",
            'category': electronics,
            'brand': apple,
            'selling_price': Decimal('999.00'),
            'cost_price': Decimal('750.00'),
            'weight': Decimal('0.171'),
            'barcode': "123456789012",
            'track_inventory': True,
            'min_stock_level': 10,
            'max_stock_level': 100
        }
    )[0]

    samsung_tv = Product.objects.get_or_create(
        sku="SAMSUNG-55-4K-TV",
        defaults={
            'name': "Samsung 55\" 4K Smart TV",
            'description': "Ultra HD Smart TV with HDR",
            'category': electronics,
            'brand': samsung,
            'selling_price': Decimal('799.00'),
            'cost_price': Decimal('600.00'),
            'weight': Decimal('15.5'),
            'barcode': "123456789013",
            'track_inventory': True,
            'min_stock_level': 5,
            'max_stock_level': 50
        }
    )[0]

    office_chair = Product.objects.get_or_create(
        sku="IKEA-CHAIR-001",
        defaults={
            'name': "IKEA Office Chair",
            'description': "Ergonomic office chair with lumbar support",
            'category': furniture,
            'brand': ikea,
            'selling_price': Decimal('199.00'),
            'cost_price': Decimal('120.00'),
            'weight': Decimal('12.0'),
            'barcode': "123456789014",
            'track_inventory': True,
            'min_stock_level': 20,
            'max_stock_level': 200
        }
    )[0]
    
    # Create Customers
    print("👥 Creating customers...")
    customer1 = Customer.objects.get_or_create(
        customer_code="CUST001",
        defaults={
            'name': "John Smith",
            'customer_type': "business",
            'email': "<EMAIL>",
            'phone': "******-0123",
            'billing_address_line1': "789 Business Ave",
            'billing_city': "Chicago",
            'billing_state': "IL",
            'billing_postal_code': "60601",
            'billing_country': "US",
            'is_active': True
        }
    )[0]

    customer2 = Customer.objects.get_or_create(
        customer_code="CUST002",
        defaults={
            'name': "Sarah Johnson",
            'customer_type': "business",
            'email': "<EMAIL>",
            'phone': "******-0124",
            'billing_address_line1': "321 Creative Blvd",
            'billing_city': "San Francisco",
            'billing_state': "CA",
            'billing_postal_code': "94102",
            'billing_country': "US",
            'is_active': True
        }
    )[0]

    customer3 = Customer.objects.get_or_create(
        customer_code="CUST003",
        defaults={
            'name': "Mike Wilson",
            'customer_type': "business",
            'email': "<EMAIL>",
            'phone': "******-0125",
            'billing_address_line1': "654 Enterprise Way",
            'billing_city': "Miami",
            'billing_state': "FL",
            'billing_postal_code': "33101",
            'billing_country': "US",
            'is_active': True
        }
    )[0]
    
    # Create Sales Orders
    print("🛒 Creating sales orders...")
    order1 = SalesOrder.objects.get_or_create(
        order_number="SO-2025-001",
        defaults={
            'customer': customer1,
            'order_date': datetime.now(),
            'status': 'confirmed',
            'notes': "Bulk order for office setup",
            'total_amount': Decimal('2397.00')
        }
    )[0]
    
    # Create Sales Order Lines
    SalesOrderLine.objects.get_or_create(
        order=order1,
        product=iphone,
        defaults={
            'quantity': 2,
            'unit_price': iphone.selling_price
        }
    )

    SalesOrderLine.objects.get_or_create(
        order=order1,
        product=office_chair,
        defaults={
            'quantity': 2,
            'unit_price': office_chair.selling_price
        }
    )
    
    order2 = SalesOrder.objects.get_or_create(
        order_number="SO-2025-002",
        defaults={
            'customer': customer2,
            'order_date': datetime.now() - timedelta(days=1),
            'status': 'processing',
            'notes': "Design studio equipment",
            'total_amount': Decimal('799.00')
        }
    )[0]
    
    SalesOrderLine.objects.get_or_create(
        order=order2,
        product=samsung_tv,
        defaults={
            'quantity': 1,
            'unit_price': samsung_tv.selling_price
        }
    )
    
    order3 = SalesOrder.objects.get_or_create(
        order_number="SO-2025-003",
        defaults={
            'customer': customer3,
            'order_date': datetime.now() - timedelta(days=2),
            'status': 'delivered',
            'notes': "Office furniture order",
            'total_amount': Decimal('597.00')
        }
    )[0]
    
    SalesOrderLine.objects.get_or_create(
        order=order3,
        product=office_chair,
        defaults={
            'quantity': 3,
            'unit_price': office_chair.selling_price
        }
    )
    
    print("✅ Sample data created successfully!")
    print("\n🎉 Your inventory system now has:")
    print(f"   📦 {Category.objects.count()} Categories")
    print(f"   🏷️ {Brand.objects.count()} Brands")
    print(f"   📱 {Product.objects.count()} Products")
    print(f"   🏢 {Warehouse.objects.count()} Warehouses")
    print(f"   👥 {Customer.objects.count()} Customers")
    print(f"   🛒 {SalesOrder.objects.count()} Sales Orders")
    print(f"   📋 {SalesOrderLine.objects.count()} Order Lines")
    
    print("\n🚀 Ready to explore your system!")
    print("   🌐 Dashboard: http://127.0.0.1:8000/dashboard/")
    print("   📦 Products: http://127.0.0.1:8000/products/")
    print("   🏢 Warehouses: http://127.0.0.1:8000/warehouses/list/")
    print("   🛒 Sales Orders: http://127.0.0.1:8000/sales/orders/")
    print("   👥 Customers: http://127.0.0.1:8000/sales/customers/")
    print("   ⚙️ Admin: http://127.0.0.1:8000/admin/")

if __name__ == "__main__":
    create_sample_data()
