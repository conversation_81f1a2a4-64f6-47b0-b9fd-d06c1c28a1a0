{% extends 'base.html' %}

{% block title %}{% if object %}Edit Warehouse{% else %}Add Warehouse{% endif %} - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{% if object %}Edit Warehouse{% else %}Add Warehouse{% endif %}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'warehouses:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Warehouses
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">Warehouse Name *</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            {% if object %}
                            <div class="mb-3">
                                <label class="form-label">Warehouse Code</label>
                                <input type="text" class="form-control" value="{{ object.code }}" readonly>
                                <small class="text-muted">Auto-generated unique identifier</small>
                            </div>
                            {% else %}
                            <div class="mb-3">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Warehouse Code:</strong> Will be auto-generated after saving
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.warehouse_type.id_for_label }}" class="form-label">Type</label>
                                {{ form.warehouse_type }}
                                {% if form.warehouse_type.errors %}
                                    <div class="text-danger small">{{ form.warehouse_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.manager.id_for_label }}" class="form-label">Manager</label>
                                {{ form.manager }}
                                {% if form.manager.errors %}
                                    <div class="text-danger small">{{ form.manager.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small">{{ form.description.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Contact Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">Phone</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger small">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Address</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.address_line1.id_for_label }}" class="form-label">Address Line 1 *</label>
                        {{ form.address_line1 }}
                        {% if form.address_line1.errors %}
                            <div class="text-danger small">{{ form.address_line1.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.address_line2.id_for_label }}" class="form-label">Address Line 2</label>
                        {{ form.address_line2 }}
                        {% if form.address_line2.errors %}
                            <div class="text-danger small">{{ form.address_line2.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.city.id_for_label }}" class="form-label">City *</label>
                                {{ form.city }}
                                {% if form.city.errors %}
                                    <div class="text-danger small">{{ form.city.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.state.id_for_label }}" class="form-label">State *</label>
                                {{ form.state }}
                                {% if form.state.errors %}
                                    <div class="text-danger small">{{ form.state.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.postal_code.id_for_label }}" class="form-label">Postal Code</label>
                                {{ form.postal_code }}
                                {% if form.postal_code.errors %}
                                    <div class="text-danger small">{{ form.postal_code.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.country.id_for_label }}" class="form-label">Country *</label>
                        {{ form.country }}
                        {% if form.country.errors %}
                            <div class="text-danger small">{{ form.country.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.capacity.id_for_label }}" class="form-label">Capacity</label>
                        {{ form.capacity }}
                        {% if form.capacity.errors %}
                            <div class="text-danger small">{{ form.capacity.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">Maximum storage capacity</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3 form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3 form-check">
                                {{ form.is_default }}
                                <label class="form-check-label" for="{{ form.is_default.id_for_label }}">
                                    Default Warehouse
                                </label>
                                {% if form.is_default.errors %}
                                    <div class="text-danger small">{{ form.is_default.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between mt-3">
                <a href="{% url 'warehouses:list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> {% if object %}Update Warehouse{% else %}Create Warehouse{% endif %}
                </button>
            </div>
        </form>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>Warehouse Guidelines:</h6>
                    <ul class="mb-0">
                        <li>Use descriptive names and unique codes</li>
                        <li>Assign a manager for each warehouse</li>
                        <li>Only one warehouse can be set as default</li>
                        <li>Complete address information is required</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
