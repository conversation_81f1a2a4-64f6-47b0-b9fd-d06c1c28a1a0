#!/usr/bin/env python
"""
Test that all loading issues are fixed across all pages
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.test import Client
from users.models import User

def test_all_pages_loading():
    """Test that all main pages load without 'Loading...' text"""
    print("🔍 Testing All Pages for Loading Issues...\n")
    
    try:
        # Create test client
        client = Client()
        
        # Get admin user
        user = User.objects.filter(is_active=True, role='admin').first()
        if not user:
            print("❌ No admin user found for testing")
            return False
        
        # Login the user
        client.force_login(user)
        print(f"✅ Logged in as: {user.username}")
        
        # Test all main pages
        test_pages = [
            ('/dashboard/', 'Dashboard'),
            ('/products/', 'Products'),
            ('/warehouses/list/', 'Warehouses'),
            ('/sales/orders/', 'Sales Orders'),
            ('/sales/customers/', 'Customers'),
            ('/dashboard/reports/', 'Reports'),
        ]
        
        print(f"\n🧪 Testing Pages for Loading Issues:")
        
        all_clean = True
        page_results = {}
        
        for url, name in test_pages:
            try:
                response = client.get(url)
                
                if response.status_code == 200:
                    content = response.content.decode('utf-8')
                    
                    # Check for loading issues
                    loading_issues = []
                    
                    # Check for "Loading..." text
                    if 'Loading...' in content:
                        loading_issues.append('Contains "Loading..." text')
                    
                    # Check for common loading patterns
                    loading_patterns = [
                        'Loading...',
                        'loading...',
                        'LOADING',
                        '<span>Loading</span>',
                        'badge">Loading<',
                    ]
                    
                    for pattern in loading_patterns:
                        if pattern in content:
                            loading_issues.append(f'Contains "{pattern}"')
                    
                    # Check for actual data presence
                    data_checks = {
                        'Dashboard': ['total_products', 'total_warehouses', 'low_stock'],
                        'Products': ['Active Products', 'Categories', 'Low Stock'],
                        'Warehouses': ['Active Warehouses', 'Total Locations', 'Stock Value'],
                        'Sales Orders': ['Sales Orders', 'Order Status'],
                        'Customers': ['Customer Management', 'Add Customer'],
                        'Reports': ['Analytics', 'Reports'],
                    }
                    
                    expected_data = data_checks.get(name, [])
                    missing_data = []
                    
                    for data_item in expected_data:
                        if data_item.lower() not in content.lower():
                            missing_data.append(data_item)
                    
                    # Determine page status
                    if loading_issues:
                        print(f"   ❌ {name}: Has loading issues")
                        for issue in loading_issues:
                            print(f"      - {issue}")
                        all_clean = False
                        page_results[name] = 'LOADING_ISSUES'
                    elif missing_data:
                        print(f"   ⚠️  {name}: Missing expected data")
                        for data in missing_data:
                            print(f"      - Missing: {data}")
                        page_results[name] = 'MISSING_DATA'
                    else:
                        print(f"   ✅ {name}: Clean (no loading issues)")
                        page_results[name] = 'CLEAN'
                        
                elif response.status_code == 302:
                    print(f"   🔄 {name}: Redirected (Status 302)")
                    page_results[name] = 'REDIRECTED'
                else:
                    print(f"   ❌ {name}: Error (Status {response.status_code})")
                    all_clean = False
                    page_results[name] = f'ERROR_{response.status_code}'
                    
            except Exception as e:
                print(f"   ❌ {name}: Exception - {str(e)}")
                all_clean = False
                page_results[name] = 'EXCEPTION'
        
        # Summary
        print(f"\n📊 Page Status Summary:")
        clean_pages = sum(1 for status in page_results.values() if status == 'CLEAN')
        total_pages = len(page_results)
        
        print(f"   Clean Pages: {clean_pages}/{total_pages}")
        print(f"   Success Rate: {(clean_pages/total_pages)*100:.1f}%")
        
        # Detailed results
        print(f"\n📋 Detailed Results:")
        for page, status in page_results.items():
            status_icon = {
                'CLEAN': '✅',
                'LOADING_ISSUES': '❌',
                'MISSING_DATA': '⚠️',
                'REDIRECTED': '🔄',
                'EXCEPTION': '💥'
            }.get(status, '❓')
            
            if status.startswith('ERROR_'):
                status_icon = '🚫'
            
            print(f"   {status_icon} {page}: {status}")
        
        return all_clean
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_statistics():
    """Test that specific statistics are calculated correctly"""
    print(f"\n📊 Testing Statistics Calculation:")
    
    try:
        from products.models import Product, Category, Brand
        from warehouses.models import Warehouse, Location, Stock
        from inventory.models import StockMovement
        
        # Test basic counts
        stats = {
            'Products': Product.objects.count(),
            'Active Products': Product.objects.filter(is_active=True).count(),
            'Categories': Category.objects.filter(is_active=True).count(),
            'Brands': Brand.objects.filter(is_active=True).count(),
            'Warehouses': Warehouse.objects.count(),
            'Active Warehouses': Warehouse.objects.filter(is_active=True).count(),
            'Locations': Location.objects.filter(is_active=True).count(),
            'Stock Records': Stock.objects.count(),
            'Stock Movements': StockMovement.objects.count(),
        }
        
        print(f"   📈 Current Statistics:")
        for stat_name, count in stats.items():
            print(f"      {stat_name}: {count}")
        
        # Check for reasonable values
        issues = []
        if stats['Products'] == 0:
            issues.append("No products in database")
        if stats['Warehouses'] == 0:
            issues.append("No warehouses in database")
        if stats['Categories'] == 0:
            issues.append("No categories in database")
        
        if issues:
            print(f"\n   ⚠️  Potential Issues:")
            for issue in issues:
                print(f"      - {issue}")
            return False
        else:
            print(f"\n   ✅ All statistics look reasonable")
            return True
        
    except Exception as e:
        print(f"❌ Error calculating statistics: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Comprehensive Loading Issues Test\n")
    
    pages_success = test_all_pages_loading()
    stats_success = test_specific_statistics()
    
    print(f"\n📋 Final Test Results:")
    print(f"   Pages Test: {'✅ PASSED' if pages_success else '❌ FAILED'}")
    print(f"   Statistics Test: {'✅ PASSED' if stats_success else '❌ FAILED'}")
    
    if pages_success and stats_success:
        print(f"\n🎉 ALL LOADING ISSUES FIXED!")
        print(f"✅ No more 'Loading...' text on any page")
        print(f"✅ All statistics display actual numbers")
        print(f"✅ All pages load successfully")
        print(f"✅ Professional appearance across all pages")
        print(f"\n🌐 Test these URLs to verify:")
        print(f"   📊 Dashboard: http://127.0.0.1:8000/dashboard/")
        print(f"   📦 Products: http://127.0.0.1:8000/products/")
        print(f"   🏢 Warehouses: http://127.0.0.1:8000/warehouses/list/")
        print(f"   🛒 Sales Orders: http://127.0.0.1:8000/sales/orders/")
        print(f"   👥 Customers: http://127.0.0.1:8000/sales/customers/")
        print(f"   📈 Reports: http://127.0.0.1:8000/dashboard/reports/")
    else:
        print(f"\n⚠️  Some issues remain")
        print(f"🔧 Review the test results above")
