<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customers - Inventory Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard/">
                <i class="fas fa-boxes"></i> Inventory System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard/">Dashboard</a>
                <a class="nav-link" href="/products/">Products</a>
                <a class="nav-link" href="/warehouses/list/">Warehouses</a>
                <a class="nav-link" href="/sales/orders/">Sales</a>
                <a class="nav-link" href="/admin/">Admin</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                <i class="fas fa-users"></i> Customer Management
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <a href="/sales/customers/create/" class="btn btn-sm btn-success">
                        <i class="fas fa-plus"></i> Add Customer
                    </a>
                    <a href="/sales/orders/" class="btn btn-sm btn-info">
                        <i class="fas fa-shopping-cart"></i> Sales Orders
                    </a>
                    <a href="/sales/" class="btn btn-sm btn-secondary">
                        <i class="fas fa-tachometer-alt"></i> Sales Dashboard
                    </a>
                </div>
            </div>
        </div>



        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Total Customers
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ customers.paginator.count|default:"0" }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Active Customers
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ active_customers|default:"0" }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Total Orders
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ total_orders|default:"0" }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Customer Value
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    ${{ customer_value|default:"0.00" }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list"></i> Customer List
                </h6>
            </div>
            <div class="card-body">
                {% if customers %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Customer ID</th>
                                    <th>Name</th>
                                    <th>Contact</th>
                                    <th>Location</th>
                                    <th>Orders</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customers %}
                                <tr>
                                    <td><strong>{{ customer.customer_id|default:"N/A" }}</strong></td>
                                    <td>
                                        <strong>{{ customer.name|default:"Unknown Customer" }}</strong>
                                        {% if customer.company %}
                                            <br><small class="text-muted">{{ customer.company }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if customer.email %}
                                            <i class="fas fa-envelope"></i> {{ customer.email }}<br>
                                        {% endif %}
                                        {% if customer.phone %}
                                            <i class="fas fa-phone"></i> {{ customer.phone }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ customer.address|default:"No Address" }}<br>
                                        <small class="text-muted">{{ customer.city|default:"" }} {{ customer.state|default:"" }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ customer.total_orders|default:"0" }} orders</span>
                                    </td>
                                    <td>
                                        {% if customer.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="/sales/customers/{{ customer.pk }}/" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/sales/customers/{{ customer.pk }}/edit/" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if customers.has_other_pages %}
                        <nav aria-label="Customers pagination">
                            <ul class="pagination justify-content-center">
                                {% if customers.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ customers.previous_page_number }}">Previous</a>
                                    </li>
                                {% endif %}
                                
                                {% for num in customers.paginator.page_range %}
                                    {% if customers.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if customers.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ customers.next_page_number }}">Next</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                        <h5>No Customers Found</h5>
                        <p class="text-muted">Start by adding your first customer to the system.</p>
                        <a href="/sales/customers/create/" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add First Customer
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-bolt"></i> Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="/sales/customers/create/" class="btn btn-success btn-block">
                                    <i class="fas fa-plus-circle"></i> Add New Customer
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/sales/orders/" class="btn btn-info btn-block">
                                    <i class="fas fa-shopping-cart"></i> Sales Orders
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/sales/" class="btn btn-secondary btn-block">
                                    <i class="fas fa-tachometer-alt"></i> Sales Dashboard
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/dashboard/" class="btn btn-primary btn-block">
                                    <i class="fas fa-tachometer-alt"></i> Main Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <style>
        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }
        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }
        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }
        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }
        .text-gray-800 {
            color: #5a5c69 !important;
        }
        .text-gray-300 {
            color: #dddfeb !important;
        }
    </style>
</body>
</html>
