{% extends 'base.html' %}

{% block title %}Warehouse Locations - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ warehouse.name }} - Locations</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'warehouses:detail' warehouse.pk %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Warehouse
        </a>
        {% if user.can_manage_inventory %}
        <a href="{% url 'warehouses:location_create' warehouse.pk %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Location
        </a>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Locations ({{ locations|length }})</h5>
    </div>
    <div class="card-body">
        {% if locations %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Code</th>
                            <th>Type</th>
                            <th>Capacity</th>
                            <th>Current Stock</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for location in locations %}
                            <tr>
                                <td><strong>{{ location.name }}</strong></td>
                                <td><code>{{ location.code }}</code></td>
                                <td>{{ location.get_location_type_display }}</td>
                                <td>{{ location.capacity|default:"-" }}</td>
                                <td>{{ location.current_stock|default:0 }}</td>
                                <td>
                                    {% if location.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'warehouses:location_detail' warehouse.pk location.pk %}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if user.can_manage_inventory %}
                                            <a href="{% url 'warehouses:location_edit' warehouse.pk location.pk %}" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No locations found</h5>
                <p class="text-muted">Create locations to organize your warehouse storage.</p>
                {% if user.can_manage_inventory %}
                    <a href="{% url 'warehouses:location_create' warehouse.pk %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Location
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}
