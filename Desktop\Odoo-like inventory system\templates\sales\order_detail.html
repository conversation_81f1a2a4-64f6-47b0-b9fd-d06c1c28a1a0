{% extends 'base.html' %}

{% block title %}Order {{ order.order_number }} - Sales Order Details{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Order {{ order.order_number }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'sales:order_list' %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Orders
        </a>
        {% if user.can_manage_sales and order.status != 'cancelled' %}
        <div class="btn-group">
            <a href="{% url 'sales:order_edit' order.pk %}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Order
            </a>
            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog"></i> Actions
            </button>
            <ul class="dropdown-menu">
                {% if order.status == 'pending' %}
                    <li><a class="dropdown-item" href="#"><i class="fas fa-check"></i> Confirm Order</a></li>
                {% endif %}
                {% if order.status == 'confirmed' %}
                    <li><a class="dropdown-item" href="#"><i class="fas fa-shipping-fast"></i> Mark as Shipped</a></li>
                {% endif %}
                {% if order.status == 'shipped' %}
                    <li><a class="dropdown-item" href="#"><i class="fas fa-check-circle"></i> Mark as Delivered</a></li>
                {% endif %}
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-times"></i> Cancel Order</a></li>
            </ul>
        </div>
        {% endif %}
    </div>
</div>

<!-- Order Status Banner -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-{% if order.status == 'pending' %}warning{% elif order.status == 'confirmed' %}info{% elif order.status == 'shipped' %}primary{% elif order.status == 'delivered' %}success{% elif order.status == 'cancelled' %}danger{% else %}secondary{% endif %} d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-{% if order.status == 'pending' %}clock{% elif order.status == 'confirmed' %}check{% elif order.status == 'shipped' %}shipping-fast{% elif order.status == 'delivered' %}check-circle{% elif order.status == 'cancelled' %}times{% else %}question{% endif %}"></i>
                    Order Status: {{ order.get_status_display }}
                </h5>
                <small>
                    {% if order.status == 'pending' %}
                        Order is awaiting confirmation
                    {% elif order.status == 'confirmed' %}
                        Order confirmed and ready for processing
                    {% elif order.status == 'shipped' %}
                        Order has been shipped
                    {% elif order.status == 'delivered' %}
                        Order has been delivered
                    {% elif order.status == 'cancelled' %}
                        Order has been cancelled
                    {% endif %}
                </small>
            </div>
            {% if order.priority == 'high' %}
                <span class="badge bg-danger fs-6">High Priority</span>
            {% elif order.priority == 'medium' %}
                <span class="badge bg-warning fs-6">Medium Priority</span>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <!-- Order Information -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Order Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Order Number:</th>
                                <td><code>{{ order.order_number }}</code></td>
                            </tr>
                            <tr>
                                <th>Customer:</th>
                                <td>
                                    <a href="{% url 'sales:customer_detail' order.customer.pk %}" class="text-decoration-none">
                                        {{ order.customer.name }}
                                    </a>
                                    {% if order.customer.company %}
                                        <br><small class="text-muted">{{ order.customer.company }}</small>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Order Date:</th>
                                <td>{{ order.order_date|date:"M d, Y" }}</td>
                            </tr>
                            <tr>
                                <th>Expected Delivery:</th>
                                <td>
                                    {% if order.expected_delivery_date %}
                                        {{ order.expected_delivery_date|date:"M d, Y" }}
                                    {% else %}
                                        Not specified
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Subtotal:</th>
                                <td>${{ order.subtotal|floatformat:2 }}</td>
                            </tr>
                            <tr>
                                <th>Tax:</th>
                                <td>${{ order.tax_amount|floatformat:2 }}</td>
                            </tr>
                            <tr>
                                <th>Shipping:</th>
                                <td>${{ order.shipping_cost|floatformat:2 }}</td>
                            </tr>
                            <tr>
                                <th><strong>Total:</strong></th>
                                <td><strong>${{ order.total_amount|floatformat:2 }}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if order.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Order Notes:</h6>
                        <p class="text-muted">{{ order.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Order Items -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Order Items ({{ order.items.count }})</h5>
            </div>
            <div class="card-body">
                {% if order.items.exists %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th class="text-center">Quantity</th>
                                    <th class="text-end">Unit Price</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order.items.all %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if item.product.image %}
                                                    <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                {% else %}
                                                    <div class="bg-light me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                {% endif %}
                                                <div>
                                                    <strong>{{ item.product.name }}</strong>
                                                    <br><small class="text-muted">SKU: {{ item.product.sku }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">{{ item.quantity }}</td>
                                        <td class="text-end">${{ item.unit_price|floatformat:2 }}</td>
                                        <td class="text-end">${{ item.total_price|floatformat:2 }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-end">Subtotal:</th>
                                    <th class="text-end">${{ order.subtotal|floatformat:2 }}</th>
                                </tr>
                                {% if order.tax_amount %}
                                <tr>
                                    <th colspan="3" class="text-end">Tax:</th>
                                    <th class="text-end">${{ order.tax_amount|floatformat:2 }}</th>
                                </tr>
                                {% endif %}
                                {% if order.shipping_cost %}
                                <tr>
                                    <th colspan="3" class="text-end">Shipping:</th>
                                    <th class="text-end">${{ order.shipping_cost|floatformat:2 }}</th>
                                </tr>
                                {% endif %}
                                <tr class="table-active">
                                    <th colspan="3" class="text-end">Total:</th>
                                    <th class="text-end">${{ order.total_amount|floatformat:2 }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No items in this order</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Customer and Shipping Info -->
    <div class="col-lg-4">
        <!-- Customer Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Customer Information</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                        <i class="fas fa-user fa-lg text-muted"></i>
                    </div>
                    <h6 class="mt-2">{{ order.customer.name }}</h6>
                    {% if order.customer.company %}
                        <p class="text-muted mb-1">{{ order.customer.company }}</p>
                    {% endif %}
                    <span class="badge bg-{% if order.customer.customer_type == 'business' %}info{% else %}secondary{% endif %}">
                        {{ order.customer.get_customer_type_display }}
                    </span>
                </div>
                
                <hr>
                
                <div class="small">
                    <p class="mb-1"><strong>Email:</strong> {{ order.customer.email }}</p>
                    {% if order.customer.phone %}
                        <p class="mb-1"><strong>Phone:</strong> {{ order.customer.phone }}</p>
                    {% endif %}
                    <p class="mb-0">
                        <strong>Address:</strong><br>
                        {% if order.shipping_address %}
                            {{ order.shipping_address }}
                        {% elif order.customer.address %}
                            {{ order.customer.address }}<br>
                            {% if order.customer.city %}{{ order.customer.city }}, {% endif %}
                            {% if order.customer.state %}{{ order.customer.state }} {% endif %}
                            {% if order.customer.postal_code %}{{ order.customer.postal_code }}{% endif %}
                        {% else %}
                            No address provided
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>

        <!-- Order Timeline -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Order Timeline</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Order Created</h6>
                            <p class="timeline-text">{{ order.created_at|date:"M d, Y H:i" }}</p>
                        </div>
                    </div>
                    {% if order.status != 'pending' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Order Confirmed</h6>
                            <p class="timeline-text">{{ order.updated_at|date:"M d, Y H:i" }}</p>
                        </div>
                    </div>
                    {% endif %}
                    {% if order.status == 'shipped' or order.status == 'delivered' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Order Shipped</h6>
                            <p class="timeline-text">Tracking information available</p>
                        </div>
                    </div>
                    {% endif %}
                    {% if order.status == 'delivered' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">Order Delivered</h6>
                            <p class="timeline-text">Successfully delivered</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 8px);
    background-color: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.timeline-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0;
}
</style>

{% endblock %}
