from django.db import models
from django.core.validators import RegexValidator


class Warehouse(models.Model):
    """
    Warehouse/Storage facility model
    """
    WAREHOUSE_TYPES = [
        ('main', 'Main Warehouse'),
        ('branch', 'Branch Warehouse'),
        ('retail', 'Retail Store'),
        ('virtual', 'Virtual Warehouse'),
        ('transit', 'Transit Warehouse'),
    ]
    
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(
        max_length=10, 
        unique=True,
        validators=[RegexValidator(r'^[A-Z0-9]+$', 'Code must contain only uppercase letters and numbers')]
    )
    warehouse_type = models.CharField(max_length=20, choices=WAREHOUSE_TYPES, default='main')
    
    # Address Information
    address_line1 = models.CharField(max_length=200)
    address_line2 = models.CharField(max_length=200, blank=True, null=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    postal_code = models.Char<PERSON><PERSON>(max_length=20)
    country = models.CharField(max_length=100, default='USA')
    
    # Contact Information
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    manager = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_warehouses')
    
    # Operational Details
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)
    capacity = models.PositiveIntegerField(blank=True, null=True, help_text="Storage capacity in cubic meters")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    def get_full_address(self):
        """Get formatted full address"""
        address_parts = [self.address_line1]
        if self.address_line2:
            address_parts.append(self.address_line2)
        address_parts.extend([self.city, self.state, self.postal_code, self.country])
        return ', '.join(address_parts)
    
    @property
    def total_products(self):
        """Get total number of different products in this warehouse"""
        return self.stock_set.filter(quantity__gt=0).count()
    
    @property
    def total_stock_value(self):
        """Calculate total value of stock in this warehouse"""
        total = 0
        for stock in self.stock_set.select_related('product').all():
            total += stock.quantity * stock.product.cost_price
        return total


class Location(models.Model):
    """
    Storage locations within warehouses (aisles, shelves, bins)
    """
    LOCATION_TYPES = [
        ('zone', 'Zone'),
        ('aisle', 'Aisle'),
        ('rack', 'Rack'),
        ('shelf', 'Shelf'),
        ('bin', 'Bin'),
        ('floor', 'Floor'),
    ]
    
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, related_name='locations')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    location_type = models.CharField(max_length=20, choices=LOCATION_TYPES, default='bin')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='children')
    
    # Physical properties
    capacity = models.PositiveIntegerField(blank=True, null=True, help_text="Storage capacity")
    length = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, help_text="Length in meters")
    width = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, help_text="Width in meters")
    height = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, help_text="Height in meters")
    
    # Status
    is_active = models.BooleanField(default=True)
    is_pickable = models.BooleanField(default=True, help_text="Can products be picked from this location?")
    is_receivable = models.BooleanField(default=True, help_text="Can products be received into this location?")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['warehouse', 'code']
        unique_together = ['warehouse', 'code']
        indexes = [
            models.Index(fields=['warehouse', 'code']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.warehouse.code}-{self.code}"
    
    def get_full_path(self):
        """Get the full location path"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return f"{self.warehouse.name} > {self.name}"
    
    @property
    def current_utilization(self):
        """Calculate current utilization percentage"""
        if not self.capacity:
            return 0
        current_stock = sum(stock.quantity for stock in self.stock_set.all())
        return (current_stock / self.capacity) * 100 if self.capacity > 0 else 0


class Stock(models.Model):
    """
    Stock levels for products in specific warehouses/locations
    """
    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    product_variant = models.ForeignKey('products.ProductVariant', on_delete=models.CASCADE, blank=True, null=True)
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE)
    location = models.ForeignKey(Location, on_delete=models.CASCADE, blank=True, null=True)
    
    quantity = models.IntegerField(default=0)
    reserved_quantity = models.IntegerField(default=0, help_text="Quantity reserved for orders")
    
    # Stock tracking
    last_counted = models.DateTimeField(blank=True, null=True)
    last_movement = models.DateTimeField(auto_now=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['product', 'product_variant', 'warehouse', 'location']
        indexes = [
            models.Index(fields=['product', 'warehouse']),
            models.Index(fields=['warehouse']),
            models.Index(fields=['quantity']),
        ]
    
    def __str__(self):
        variant_info = f" - {self.product_variant.name}" if self.product_variant else ""
        location_info = f" @ {self.location.code}" if self.location else ""
        return f"{self.product.name}{variant_info} in {self.warehouse.name}{location_info}: {self.quantity}"
    
    @property
    def available_quantity(self):
        """Get available quantity (total - reserved)"""
        return max(0, self.quantity - self.reserved_quantity)
    
    @property
    def is_low_stock(self):
        """Check if stock is below minimum level"""
        return self.quantity <= self.product.min_stock_level
    
    @property
    def stock_value(self):
        """Calculate total value of this stock"""
        if self.product_variant:
            return self.quantity * self.product_variant.effective_cost_price
        return self.quantity * self.product.cost_price
