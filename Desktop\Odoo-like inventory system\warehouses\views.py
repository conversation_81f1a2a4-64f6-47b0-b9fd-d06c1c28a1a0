from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, Sum, Count, F
from django.http import JsonResponse
from users.views import inventory_access_required, manager_required
from .models import Warehouse, Location, Stock
from .forms import WarehouseForm, LocationForm, StockForm, WarehouseSearchForm
from products.models import Product


class WarehouseListView(LoginRequiredMixin, ListView):
    """List all warehouses"""
    model = Warehouse
    template_name = 'warehouses/warehouse_list.html'
    context_object_name = 'warehouses'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Warehouse.objects.select_related('manager').annotate(
            total_products=Count('stock', distinct=True),
            total_locations=Count('locations', distinct=True)
        )
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search) |
                Q(city__icontains=search)
            )
        
        # Filter by type
        warehouse_type = self.request.GET.get('type')
        if warehouse_type:
            queryset = queryset.filter(warehouse_type=warehouse_type)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        
        return queryset.order_by('name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = WarehouseSearchForm(self.request.GET)
        context['warehouse_types'] = Warehouse.WAREHOUSE_TYPES
        return context


class WarehouseDetailView(LoginRequiredMixin, DetailView):
    """Warehouse detail view with stock and location information"""
    model = Warehouse
    template_name = 'warehouses/warehouse_detail.html'
    context_object_name = 'warehouse'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        warehouse = self.get_object()
        
        # Get locations
        context['locations'] = warehouse.locations.filter(is_active=True).order_by('code')
        
        # Get stock summary
        stock_records = warehouse.stock_set.select_related('product').filter(quantity__gt=0)
        context['stock_records'] = stock_records[:20]  # Show first 20
        context['total_stock_records'] = stock_records.count()
        
        # Calculate warehouse metrics
        context['total_stock_value'] = warehouse.total_stock_value
        context['total_products'] = warehouse.total_products
        context['low_stock_items'] = stock_records.filter(
            quantity__lte=F('product__min_stock_level')
        ).count()
        
        # Recent stock movements
        from inventory.models import StockMovement
        context['recent_movements'] = StockMovement.objects.filter(
            Q(from_warehouse=warehouse) | Q(to_warehouse=warehouse)
        ).select_related('product').order_by('-created_at')[:10]
        
        return context


class WarehouseCreateView(LoginRequiredMixin, CreateView):
    """Create new warehouse"""
    model = Warehouse
    form_class = WarehouseForm
    template_name = 'warehouses/warehouse_form.html'
    success_url = reverse_lazy('warehouses:list')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_manager:
            messages.error(request, 'You do not have permission to create warehouses.')
            return redirect('warehouses:list')
        return super().dispatch(request, *args, **kwargs)
    
    def form_valid(self, form):
        messages.success(self.request, f'Warehouse "{form.instance.name}" created successfully.')
        return super().form_valid(form)


class WarehouseUpdateView(LoginRequiredMixin, UpdateView):
    """Update existing warehouse"""
    model = Warehouse
    form_class = WarehouseForm
    template_name = 'warehouses/warehouse_form.html'
    success_url = reverse_lazy('warehouses:list')
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_manager:
            messages.error(request, 'You do not have permission to edit warehouses.')
            return redirect('warehouses:list')
        return super().dispatch(request, *args, **kwargs)
    
    def form_valid(self, form):
        messages.success(self.request, f'Warehouse "{form.instance.name}" updated successfully.')
        return super().form_valid(form)


@login_required
@manager_required
def warehouse_toggle_status(request, pk):
    """Toggle warehouse active status"""
    warehouse = get_object_or_404(Warehouse, pk=pk)
    warehouse.is_active = not warehouse.is_active
    warehouse.save()
    
    status = "activated" if warehouse.is_active else "deactivated"
    messages.success(request, f'Warehouse "{warehouse.name}" has been {status}.')
    return redirect('warehouses:list')


# Location Views
class LocationListView(LoginRequiredMixin, ListView):
    """List locations for a warehouse"""
    model = Location
    template_name = 'warehouses/location_list.html'
    context_object_name = 'locations'
    paginate_by = 50
    
    def get_queryset(self):
        self.warehouse = get_object_or_404(Warehouse, pk=self.kwargs['warehouse_pk'])
        queryset = self.warehouse.locations.select_related('parent').annotate(
            stock_count=Count('stock')
        )
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search)
            )
        
        # Filter by type
        location_type = self.request.GET.get('type')
        if location_type:
            queryset = queryset.filter(location_type=location_type)
        
        return queryset.order_by('code')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['warehouse'] = self.warehouse
        context['location_types'] = Location.LOCATION_TYPES
        return context


class LocationDetailView(LoginRequiredMixin, DetailView):
    """Location detail view with stock information"""
    model = Location
    template_name = 'warehouses/location_detail.html'
    context_object_name = 'location'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        location = self.get_object()
        
        # Get stock in this location
        context['stock_records'] = location.stock_set.select_related('product').filter(quantity__gt=0)
        context['total_stock_value'] = sum(
            stock.stock_value for stock in context['stock_records']
        )
        
        # Get child locations
        context['child_locations'] = location.children.filter(is_active=True)
        
        # Calculate utilization
        context['utilization'] = location.current_utilization
        
        return context


@login_required
@inventory_access_required
def location_create(request, warehouse_pk):
    """Create new location in warehouse"""
    warehouse = get_object_or_404(Warehouse, pk=warehouse_pk)
    
    if request.method == 'POST':
        form = LocationForm(request.POST, warehouse=warehouse)
        if form.is_valid():
            location = form.save(commit=False)
            location.warehouse = warehouse
            location.save()
            messages.success(request, f'Location "{location.name}" created successfully.')
            return redirect('warehouses:location_list', warehouse_pk=warehouse.pk)
    else:
        form = LocationForm(warehouse=warehouse)
    
    context = {
        'form': form,
        'warehouse': warehouse,
    }
    return render(request, 'warehouses/location_form.html', context)


@login_required
@inventory_access_required
def location_update(request, warehouse_pk, pk):
    """Update existing location"""
    warehouse = get_object_or_404(Warehouse, pk=warehouse_pk)
    location = get_object_or_404(Location, pk=pk, warehouse=warehouse)
    
    if request.method == 'POST':
        form = LocationForm(request.POST, instance=location, warehouse=warehouse)
        if form.is_valid():
            form.save()
            messages.success(request, f'Location "{location.name}" updated successfully.')
            return redirect('warehouses:location_detail', warehouse_pk=warehouse.pk, pk=location.pk)
    else:
        form = LocationForm(instance=location, warehouse=warehouse)
    
    context = {
        'form': form,
        'warehouse': warehouse,
        'location': location,
    }
    return render(request, 'warehouses/location_form.html', context)


# Stock Views
class StockListView(LoginRequiredMixin, ListView):
    """List stock records for a warehouse"""
    model = Stock
    template_name = 'warehouses/stock_list.html'
    context_object_name = 'stock_records'
    paginate_by = 50
    
    def get_queryset(self):
        self.warehouse = get_object_or_404(Warehouse, pk=self.kwargs['warehouse_pk'])
        queryset = self.warehouse.stock_set.select_related('product', 'location').filter(quantity__gt=0)
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(product__name__icontains=search) |
                Q(product__sku__icontains=search) |
                Q(product__barcode__icontains=search)
            )
        
        # Filter by location
        location = self.request.GET.get('location')
        if location:
            queryset = queryset.filter(location_id=location)
        
        # Filter by stock status
        status = self.request.GET.get('status')
        if status == 'low_stock':
            queryset = queryset.filter(quantity__lte=F('product__min_stock_level'))
        elif status == 'out_of_stock':
            queryset = queryset.filter(quantity=0)
        elif status == 'reserved':
            queryset = queryset.filter(reserved_quantity__gt=0)
        
        return queryset.order_by('product__name')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['warehouse'] = self.warehouse
        context['locations'] = self.warehouse.locations.filter(is_active=True)
        
        # Calculate totals
        all_stock = self.warehouse.stock_set.filter(quantity__gt=0)
        context['total_products'] = all_stock.count()
        context['total_stock_value'] = sum(stock.stock_value for stock in all_stock)
        context['low_stock_count'] = all_stock.filter(
            quantity__lte=F('product__min_stock_level')
        ).count()
        
        return context


@login_required
@inventory_access_required
def stock_detail(request, warehouse_pk, pk):
    """Stock detail view"""
    warehouse = get_object_or_404(Warehouse, pk=warehouse_pk)
    stock = get_object_or_404(Stock, pk=pk, warehouse=warehouse)
    
    # Get recent movements for this product in this warehouse
    from inventory.models import StockMovement
    recent_movements = StockMovement.objects.filter(
        product=stock.product
    ).filter(
        Q(from_warehouse=warehouse) | Q(to_warehouse=warehouse)
    ).order_by('-created_at')[:20]
    
    context = {
        'warehouse': warehouse,
        'stock': stock,
        'recent_movements': recent_movements,
    }
    return render(request, 'warehouses/stock_detail.html', context)


# AJAX Views
@login_required
def warehouse_search_ajax(request):
    """AJAX search for warehouses"""
    query = request.GET.get('q', '')
    warehouses = Warehouse.objects.filter(
        Q(name__icontains=query) | Q(code__icontains=query),
        is_active=True
    )[:10]
    
    results = []
    for warehouse in warehouses:
        results.append({
            'id': warehouse.id,
            'code': warehouse.code,
            'name': warehouse.name,
            'type': warehouse.get_warehouse_type_display(),
        })
    
    return JsonResponse({'results': results})


@login_required
def location_search_ajax(request):
    """AJAX search for locations in a warehouse"""
    warehouse_id = request.GET.get('warehouse_id')
    query = request.GET.get('q', '')
    
    if not warehouse_id:
        return JsonResponse({'results': []})
    
    locations = Location.objects.filter(
        warehouse_id=warehouse_id,
        is_active=True
    ).filter(
        Q(name__icontains=query) | Q(code__icontains=query)
    )[:10]
    
    results = []
    for location in locations:
        results.append({
            'id': location.id,
            'code': location.code,
            'name': location.name,
            'type': location.get_location_type_display(),
            'full_path': location.get_full_path(),
        })
    
    return JsonResponse({'results': results})


@login_required
def check_warehouse_code(request):
    """Check if warehouse code is available"""
    code = request.GET.get('code', '')
    warehouse_id = request.GET.get('warehouse_id', None)
    
    query = Warehouse.objects.filter(code=code)
    if warehouse_id:
        query = query.exclude(id=warehouse_id)
    
    is_available = not query.exists()
    
    return JsonResponse({'available': is_available})


# Dashboard and Reports
@login_required
def warehouse_dashboard(request):
    """Warehouse dashboard with key metrics"""
    warehouses = Warehouse.objects.filter(is_active=True).annotate(
        total_products=Count('stock', distinct=True),
        total_stock_value=Sum('stock__quantity') * Sum('stock__product__cost_price')
    )
    
    # Overall metrics
    total_warehouses = warehouses.count()
    total_products = Stock.objects.filter(quantity__gt=0).values('product').distinct().count()
    total_stock_value = sum(
        stock.stock_value for stock in Stock.objects.select_related('product').filter(quantity__gt=0)
    )
    
    # Low stock alerts
    low_stock_items = Stock.objects.filter(
        quantity__lte=F('product__min_stock_level'),
        quantity__gt=0
    ).select_related('product', 'warehouse')[:10]
    
    context = {
        'warehouses': warehouses,
        'total_warehouses': total_warehouses,
        'total_products': total_products,
        'total_stock_value': total_stock_value,
        'low_stock_items': low_stock_items,
    }
    return render(request, 'warehouses/dashboard.html', context)


@login_required
def warehouse_report(request, pk):
    """Generate warehouse report"""
    warehouse = get_object_or_404(Warehouse, pk=pk)
    
    # Stock summary by category
    from django.db.models import F
    stock_by_category = Stock.objects.filter(
        warehouse=warehouse,
        quantity__gt=0
    ).values(
        'product__category__name'
    ).annotate(
        total_quantity=Sum('quantity'),
        total_value=Sum(F('quantity') * F('product__cost_price')),
        product_count=Count('product', distinct=True)
    ).order_by('-total_value')
    
    # Low stock items
    low_stock = Stock.objects.filter(
        warehouse=warehouse,
        quantity__lte=F('product__min_stock_level'),
        quantity__gt=0
    ).select_related('product')
    
    context = {
        'warehouse': warehouse,
        'stock_by_category': stock_by_category,
        'low_stock': low_stock,
        'total_stock_value': warehouse.total_stock_value,
        'total_products': warehouse.total_products,
    }
    return render(request, 'warehouses/warehouse_report.html', context)
