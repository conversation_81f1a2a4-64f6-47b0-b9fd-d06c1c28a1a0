#!/usr/bin/env python
"""
Test users app functionality
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

try:
    from users.models import User, UserProfile
    from users.views import inventory_access_required
    from django.contrib.auth import authenticate
    
    print("👥 Testing Users App...")
    
    # Test 1: Check if User model is working
    print(f"✅ User model imported successfully")
    print(f"✅ UserProfile model imported successfully")
    print(f"✅ inventory_access_required decorator imported successfully")
    
    # Test 2: Check existing users
    users = User.objects.all()
    print(f"\n📊 Users in database: {users.count()}")
    
    for user in users:
        print(f"   - {user.username} (Role: {user.get_role_display()}, Active: {user.is_active})")
        print(f"     Can manage inventory: {user.can_manage_inventory}")
        print(f"     Can manage sales: {user.can_manage_sales}")
        print(f"     Is active employee: {user.is_active_employee}")
    
    # Test 3: Check if we can create a test user
    test_user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'role': 'manager',
            'is_active': True,
            'is_active_employee': True
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"\n✅ Test user created: {test_user.username}")
    else:
        print(f"\n✅ Test user already exists: {test_user.username}")
    
    # Test 4: Test authentication
    auth_user = authenticate(username='testuser', password='testpass123')
    if auth_user:
        print(f"✅ Authentication test successful for: {auth_user.username}")
    else:
        print(f"❌ Authentication test failed")
    
    # Test 5: Check superuser
    superusers = User.objects.filter(is_superuser=True)
    print(f"\n🔑 Superusers: {superusers.count()}")
    for su in superusers:
        print(f"   - {su.username} (Email: {su.email})")
    
    print("\n🎉 Users app is working correctly!")
    print("\n📋 AVAILABLE USERS:")
    for user in User.objects.filter(is_active=True):
        print(f"   Username: {user.username}")
        print(f"   Role: {user.get_role_display()}")
        print(f"   Permissions: Inventory={user.can_manage_inventory}, Sales={user.can_manage_sales}")
        print()
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
