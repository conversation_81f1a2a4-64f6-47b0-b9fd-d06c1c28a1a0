{% extends 'base.html' %}

{% block title %}{{ customer.name }} - Customer Details{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{{ customer.name }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Customers
        </a>
        {% if user.can_manage_sales %}
        <div class="btn-group">
            <a href="{% url 'sales:customer_edit' customer.pk %}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Customer
            </a>
            <a href="{% url 'sales:order_create' %}?customer={{ customer.pk }}" class="btn btn-success">
                <i class="fas fa-plus"></i> New Order
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Customer Info Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_orders }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Spent</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ total_spent|floatformat:2 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Avg Order Value</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">${{ avg_order_value|floatformat:2 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Last Order</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% if last_order_date %}
                                {{ last_order_date|date:"M d" }}
                            {% else %}
                                Never
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Customer Information -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Customer Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Name:</th>
                                <td>{{ customer.name }}</td>
                            </tr>
                            <tr>
                                <th>Email:</th>
                                <td>{{ customer.email }}</td>
                            </tr>
                            <tr>
                                <th>Phone:</th>
                                <td>{{ customer.phone|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td>
                                    <span class="badge bg-{% if customer.customer_type == 'business' %}info{% else %}secondary{% endif %}">
                                        {{ customer.get_customer_type_display }}
                                    </span>
                                </td>
                            </tr>
                            {% if customer.company %}
                            <tr>
                                <th>Company:</th>
                                <td>{{ customer.company }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">Address:</th>
                                <td>
                                    {% if customer.address %}
                                        {{ customer.address }}<br>
                                        {% if customer.city %}{{ customer.city }}, {% endif %}
                                        {% if customer.state %}{{ customer.state }} {% endif %}
                                        {% if customer.postal_code %}{{ customer.postal_code }}{% endif %}
                                        {% if customer.country %}<br>{{ customer.country }}{% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    {% if customer.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Created:</th>
                                <td>{{ customer.created_at|date:"M d, Y" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if customer.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Notes:</h6>
                        <p class="text-muted">{{ customer.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Orders -->
        {% if recent_orders %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Orders</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Date</th>
                                <th>Items</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                                <tr>
                                    <td><code>{{ order.order_number }}</code></td>
                                    <td>{{ order.order_date|date:"M d, Y" }}</td>
                                    <td>{{ order.items.count }} items</td>
                                    <td>${{ order.total_amount|floatformat:2 }}</td>
                                    <td>
                                        <span class="badge bg-{% if order.status == 'pending' %}warning{% elif order.status == 'confirmed' %}info{% elif order.status == 'shipped' %}primary{% elif order.status == 'delivered' %}success{% elif order.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                            {{ order.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'sales:order_detail' order.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <a href="{% url 'sales:order_list' %}?customer={{ customer.pk }}" class="btn btn-sm btn-outline-primary">View All Orders</a>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Quick Actions and Summary -->
    <div class="col-lg-4">
        {% if user.can_manage_sales %}
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'sales:customer_edit' customer.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> Edit Customer
                    </a>
                    <a href="{% url 'sales:order_create' %}?customer={{ customer.pk }}" class="btn btn-outline-success">
                        <i class="fas fa-plus"></i> Create Order
                    </a>
                    <a href="{% url 'sales:order_list' %}?customer={{ customer.pk }}" class="btn btn-outline-info">
                        <i class="fas fa-list"></i> View All Orders
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Customer Summary -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Customer Summary</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                        <i class="fas fa-user fa-2x text-muted"></i>
                    </div>
                    <h6 class="mt-2">{{ customer.name }}</h6>
                    <p class="text-muted">{{ customer.get_customer_type_display }}</p>
                </div>
                
                <hr>
                
                <div class="text-start">
                    <small class="text-muted">
                        <strong>Member since:</strong> {{ customer.created_at|date:"M Y" }}<br>
                        <strong>Last order:</strong> 
                        {% if last_order_date %}
                            {{ last_order_date|date:"M d, Y" }}
                        {% else %}
                            Never
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
