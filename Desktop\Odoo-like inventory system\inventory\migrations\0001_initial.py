# Generated by Django 5.2.4 on 2025-07-17 08:45

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='StockAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference', models.CharField(editable=False, max_length=50, unique=True)),
                ('adjustment_type', models.CharField(choices=[('count', 'Physical Count'), ('damage', 'Damage'), ('theft', 'Theft/Loss'), ('expired', 'Expired'), ('found', 'Found Stock'), ('correction', 'Data Correction')], max_length=20)),
                ('reason', models.TextField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('is_approved', models.BooleanField(default=False)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockAdjustmentLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expected_quantity', models.IntegerField(default=0)),
                ('actual_quantity', models.IntegerField()),
                ('difference', models.IntegerField(editable=False)),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('total_cost_impact', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference', models.CharField(editable=False, max_length=50, unique=True)),
                ('movement_type', models.CharField(choices=[('in', 'Stock In'), ('out', 'Stock Out'), ('transfer', 'Transfer'), ('adjustment', 'Adjustment'), ('return', 'Return'), ('damaged', 'Damaged'), ('expired', 'Expired')], max_length=20)),
                ('reason', models.CharField(choices=[('purchase', 'Purchase'), ('sale', 'Sale'), ('transfer', 'Warehouse Transfer'), ('adjustment', 'Stock Adjustment'), ('return_customer', 'Customer Return'), ('return_supplier', 'Return to Supplier'), ('damaged', 'Damaged Goods'), ('expired', 'Expired Goods'), ('theft', 'Theft/Loss'), ('production', 'Production'), ('sample', 'Sample'), ('promotion', 'Promotional')], max_length=20)),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('total_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('is_processed', models.BooleanField(default=False)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
