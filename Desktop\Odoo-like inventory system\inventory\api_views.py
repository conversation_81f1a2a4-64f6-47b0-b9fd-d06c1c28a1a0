from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Sum, Count, F
from django.utils import timezone
from datetime import timedelta
from .models import StockMovement, StockAdjustment
from .serializers import StockMovementSerializer, StockAdjustmentSerializer
from warehouses.models import Stock


class StockMovementViewSet(viewsets.ModelViewSet):
    """API ViewSet for stock movements"""
    queryset = StockMovement.objects.all()
    serializer_class = StockMovementSerializer


class StockAdjustmentViewSet(viewsets.ModelViewSet):
    """API ViewSet for stock adjustments"""
    queryset = StockAdjustment.objects.all()
    serializer_class = StockAdjustmentSerializer


class DashboardStatsAPIView(APIView):
    """API for dashboard statistics"""
    
    def get(self, request):
        days = int(request.query_params.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)
        
        # Calculate stats
        total_movements = StockMovement.objects.filter(
            created_at__gte=start_date
        ).count()
        
        movements_in = StockMovement.objects.filter(
            movement_type='in',
            created_at__gte=start_date
        ).aggregate(total=Sum('quantity'))['total'] or 0
        
        movements_out = StockMovement.objects.filter(
            movement_type='out',
            created_at__gte=start_date
        ).aggregate(total=Sum('quantity'))['total'] or 0
        
        low_stock_count = Stock.objects.filter(
            quantity__lte=F('product__min_stock_level'),
            quantity__gt=0
        ).count()
        
        return Response({
            'total_movements': total_movements,
            'movements_in': movements_in,
            'movements_out': movements_out,
            'low_stock_count': low_stock_count,
            'period_days': days
        })


class MovementStatsAPIView(APIView):
    """API for movement statistics"""
    
    def get(self, request):
        days = int(request.query_params.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)
        
        # Daily movement data
        daily_data = []
        for i in range(days):
            date = start_date + timedelta(days=i)
            movements_in = StockMovement.objects.filter(
                movement_type='in',
                created_at__date=date.date()
            ).aggregate(total=Sum('quantity'))['total'] or 0
            
            movements_out = StockMovement.objects.filter(
                movement_type='out',
                created_at__date=date.date()
            ).aggregate(total=Sum('quantity'))['total'] or 0
            
            daily_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'in': movements_in,
                'out': movements_out,
            })
        
        return Response({'daily_data': daily_data})


class LowStockAlertsAPIView(APIView):
    """API for low stock alerts"""
    
    def get(self, request):
        low_stock_items = Stock.objects.filter(
            quantity__lte=F('product__min_stock_level'),
            quantity__gt=0
        ).select_related('product', 'warehouse')[:20]
        
        alerts = []
        for stock in low_stock_items:
            alerts.append({
                'product_name': stock.product.name,
                'product_sku': stock.product.sku,
                'warehouse_name': stock.warehouse.name,
                'current_quantity': stock.quantity,
                'min_level': stock.product.min_stock_level,
                'shortage': stock.product.min_stock_level - stock.quantity
            })
        
        return Response({'alerts': alerts})


class QuickStockInAPIView(APIView):
    """API for quick stock in"""
    
    def post(self, request):
        # Implementation for quick stock in
        return Response({'message': 'Stock added successfully'})


class QuickStockOutAPIView(APIView):
    """API for quick stock out"""
    
    def post(self, request):
        # Implementation for quick stock out
        return Response({'message': 'Stock removed successfully'})


class StockTransferAPIView(APIView):
    """API for stock transfer"""
    
    def post(self, request):
        # Implementation for stock transfer
        return Response({'message': 'Stock transferred successfully'})
