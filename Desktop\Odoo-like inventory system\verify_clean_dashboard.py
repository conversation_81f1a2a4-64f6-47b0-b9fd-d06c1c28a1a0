#!/usr/bin/env python
"""
Verify that system status comments have been removed from dashboard
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.test import Client
from users.models import User

def verify_clean_dashboard():
    """Verify dashboard is clean without status messages"""
    print("🧹 Verifying Clean Dashboard (No Status Messages)...\n")
    
    try:
        # Create test client
        client = Client()
        
        # Get admin user
        user = User.objects.filter(is_active=True, role='admin').first()
        if not user:
            print("❌ No admin user found for testing")
            return False
        
        # Login the user
        client.force_login(user)
        print(f"✅ Logged in as: {user.username}")
        
        # Test dashboard
        print("\n🏠 Testing Clean Dashboard:")
        response = client.get('/dashboard/')
        
        if response.status_code == 200:
            print("✅ Dashboard loads successfully")
            
            content = response.content.decode('utf-8')
            
            # Check that status messages are REMOVED
            removed_elements = [
                ('Success Alert', 'Dashboard Loaded Successfully' not in content),
                ('Error Alert', 'Dashboard Loading Issue' not in content),
                ('Status Messages', 'All systems operational' not in content),
                ('Loading Messages', 'Successfully loaded' not in content),
                ('Template Status', 'Rendering correctly' not in content),
                ('Static Files Status', 'Loading properly' not in content),
            ]
            
            print("\n🚫 Checking Removed Status Elements:")
            all_removed = True
            for element_name, is_removed in removed_elements:
                status = "✅" if is_removed else "❌"
                print(f"   {status} {element_name}: {'Removed' if is_removed else 'Still Present'}")
                if not is_removed:
                    all_removed = False
            
            # Check that functional elements are PRESENT
            functional_elements = [
                ('Dashboard Title', 'Dashboard' in content),
                ('Analytics Button', 'Analytics' in content),
                ('Products Link', 'Products' in content),
                ('Quick Links', 'Quick Links' in content),
                ('Navigation', 'navbar' in content.lower()),
                ('Cards/Metrics', 'card' in content.lower()),
            ]
            
            print("\n✅ Checking Functional Elements:")
            all_functional = True
            for element_name, is_present in functional_elements:
                status = "✅" if is_present else "❌"
                print(f"   {status} {element_name}: {'Present' if is_present else 'Missing'}")
                if not is_present:
                    all_functional = False
            
            if all_removed and all_functional:
                print("\n🎉 Dashboard is perfectly clean!")
                print("✅ All status messages removed")
                print("✅ All functional elements present")
                print("✅ Dashboard shows only business functionality")
                return True
            else:
                if not all_removed:
                    print("\n⚠️  Some status messages are still present")
                if not all_functional:
                    print("\n⚠️  Some functional elements are missing")
                return False
                
        else:
            print(f"❌ Dashboard failed to load (Status: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_template_structure():
    """Check template file structure"""
    print("\n📄 Checking Template File Structure:")
    
    try:
        template_path = r"Desktop\Odoo-like inventory system\templates\inventory\dashboard.html"
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for removed elements
        status_checks = [
            ('Success Alert Block', 'Dashboard Loaded Successfully' not in content),
            ('Error Alert Block', 'Dashboard Loading Issue' not in content),
            ('Status Variables', 'dashboard_loaded' not in content),
            ('Error Variables', 'dashboard_error' not in content),
        ]
        
        print("🔍 Template Content Analysis:")
        all_clean = True
        for check_name, is_clean in status_checks:
            status = "✅" if is_clean else "❌"
            print(f"   {status} {check_name}: {'Removed' if is_clean else 'Still Present'}")
            if not is_clean:
                all_clean = False
        
        return all_clean
        
    except Exception as e:
        print(f"❌ Template check error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Clean Dashboard Verification\n")
    
    template_ok = check_template_structure()
    dashboard_ok = verify_clean_dashboard()
    
    print(f"\n📋 Final Results:")
    print(f"   Template Structure: {'✅ CLEAN' if template_ok else '❌ NEEDS CLEANUP'}")
    print(f"   Dashboard Functionality: {'✅ CLEAN' if dashboard_ok else '❌ NEEDS CLEANUP'}")
    
    if template_ok and dashboard_ok:
        print(f"\n🎉 Dashboard is completely clean!")
        print(f"✅ No system status messages")
        print(f"✅ Only functional business elements")
        print(f"✅ Professional appearance")
        print(f"🌐 Access at: http://127.0.0.1:8000/dashboard/")
    else:
        print(f"\n⚠️  Dashboard cleanup needs attention")
