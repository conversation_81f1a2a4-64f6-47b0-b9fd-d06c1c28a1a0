#!/usr/bin/env python
"""
Quick test to verify the Product model fields are working correctly
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

try:
    from products.models import Category, Brand, Product
    print("✅ Models imported successfully!")
    
    # Test Product creation with correct field names
    category = Category.objects.get_or_create(name="Electronics", defaults={'description': "Electronic devices"})[0]
    brand = Brand.objects.get_or_create(name="Apple", defaults={'description': "Apple Inc."})[0]
    
    product = Product.objects.get_or_create(
        sku="IPHONE-TEST",
        defaults={
            'name': "iPhone Test",
            'description': "Test iPhone product",
            'category': category,
            'brand': brand,
            'selling_price': Decimal('999.99'),  # Correct field name
            'cost_price': Decimal('750.00'),
            'track_inventory': True,
            'min_stock_level': 5
        }
    )[0]
    
    print(f"✅ Product created successfully!")
    print(f"   Name: {product.name}")
    print(f"   SKU: {product.sku}")
    print(f"   Selling Price: ${product.selling_price}")
    print(f"   Cost Price: ${product.cost_price}")
    print(f"   Category: {product.category.name}")
    print(f"   Brand: {product.brand.name}")
    
    # Test accessing the selling_price field
    profit_margin = ((product.selling_price - product.cost_price) / product.cost_price) * 100
    print(f"   Profit Margin: {profit_margin:.2f}%")
    
    print("\n🎉 All Product model fields are working correctly!")
    print("✅ selling_price field is properly configured")
    print("✅ cost_price field is properly configured")
    print("✅ Product creation and field access successful")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
