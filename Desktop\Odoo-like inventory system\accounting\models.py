from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
import uuid


class Account(models.Model):
    """
    Chart of accounts for basic accounting
    """
    ACCOUNT_TYPES = [
        ('asset', 'Asset'),
        ('liability', 'Liability'),
        ('equity', 'Equity'),
        ('revenue', 'Revenue'),
        ('expense', 'Expense'),
    ]
    
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPES)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='children')
    
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['code']
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def balance(self):
        """Calculate current account balance"""
        credit_total = sum(entry.credit_amount for entry in self.journal_entries.all())
        debit_total = sum(entry.debit_amount for entry in self.journal_entries.all())
        
        if self.account_type in ['asset', 'expense']:
            return debit_total - credit_total
        else:  # liability, equity, revenue
            return credit_total - debit_total


class JournalEntry(models.Model):
    """
    Journal entries for double-entry bookkeeping
    """
    ENTRY_TYPES = [
        ('manual', 'Manual Entry'),
        ('sales', 'Sales'),
        ('purchase', 'Purchase'),
        ('payment', 'Payment'),
        ('receipt', 'Receipt'),
        ('adjustment', 'Adjustment'),
    ]
    
    reference = models.CharField(max_length=50, unique=True, editable=False)
    entry_type = models.CharField(max_length=20, choices=ENTRY_TYPES, default='manual')
    date = models.DateField(default=timezone.now)
    description = models.CharField(max_length=500)
    
    # Related documents
    sales_order = models.ForeignKey('sales.SalesOrder', on_delete=models.SET_NULL, blank=True, null=True)
    purchase_order = models.ForeignKey('sales.PurchaseOrder', on_delete=models.SET_NULL, blank=True, null=True)
    
    # Status
    is_posted = models.BooleanField(default=False)
    posted_at = models.DateTimeField(blank=True, null=True)
    posted_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='posted_entries')
    
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, related_name='created_entries')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-date', '-created_at']
        verbose_name_plural = "Journal Entries"
    
    def save(self, *args, **kwargs):
        if not self.reference:
            self.reference = self.generate_reference()
        super().save(*args, **kwargs)
    
    def generate_reference(self):
        """Generate unique reference number"""
        prefix = 'JE'
        timestamp = timezone.now().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4())[:6].upper()
        return f"{prefix}-{timestamp}-{unique_id}"
    
    def __str__(self):
        return f"{self.reference} - {self.description}"
    
    @property
    def total_debits(self):
        """Calculate total debit amount"""
        return sum(line.debit_amount for line in self.lines.all())
    
    @property
    def total_credits(self):
        """Calculate total credit amount"""
        return sum(line.credit_amount for line in self.lines.all())
    
    @property
    def is_balanced(self):
        """Check if entry is balanced (debits = credits)"""
        return self.total_debits == self.total_credits
    
    def post(self, user):
        """Post the journal entry"""
        if not self.is_balanced:
            raise ValueError("Journal entry is not balanced")
        
        self.is_posted = True
        self.posted_at = timezone.now()
        self.posted_by = user
        self.save()


class JournalEntryLine(models.Model):
    """
    Individual lines in journal entries
    """
    entry = models.ForeignKey(JournalEntry, on_delete=models.CASCADE, related_name='lines')
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='journal_entries')
    
    description = models.CharField(max_length=500, blank=True, null=True)
    debit_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    credit_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, validators=[MinValueValidator(0)])
    
    # Reference information
    reference = models.CharField(max_length=100, blank=True, null=True)
    
    def __str__(self):
        if self.debit_amount > 0:
            return f"{self.account.name} - Debit: ${self.debit_amount}"
        else:
            return f"{self.account.name} - Credit: ${self.credit_amount}"
    
    def clean(self):
        """Validate that either debit or credit is set, but not both"""
        from django.core.exceptions import ValidationError
        
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError("A line cannot have both debit and credit amounts")
        
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError("A line must have either a debit or credit amount")


class Invoice(models.Model):
    """
    Customer invoices
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]
    
    invoice_number = models.CharField(max_length=50, unique=True, editable=False)
    customer = models.ForeignKey('sales.Customer', on_delete=models.CASCADE, related_name='invoices')
    sales_order = models.ForeignKey('sales.SalesOrder', on_delete=models.SET_NULL, blank=True, null=True)
    
    # Dates
    invoice_date = models.DateField(default=timezone.now)
    due_date = models.DateField()
    
    # Amounts
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    paid_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Additional information
    notes = models.TextField(blank=True, null=True)
    terms_and_conditions = models.TextField(blank=True, null=True)
    
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        super().save(*args, **kwargs)
    
    def generate_invoice_number(self):
        """Generate unique invoice number"""
        prefix = 'INV'
        timestamp = timezone.now().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4())[:6].upper()
        return f"{prefix}-{timestamp}-{unique_id}"
    
    def __str__(self):
        return f"{self.invoice_number} - {self.customer.name}"
    
    @property
    def balance_due(self):
        """Calculate remaining balance"""
        return self.total_amount - self.paid_amount
    
    @property
    def is_paid(self):
        """Check if invoice is fully paid"""
        return self.paid_amount >= self.total_amount
    
    @property
    def is_overdue(self):
        """Check if invoice is overdue"""
        return timezone.now().date() > self.due_date and not self.is_paid


class Payment(models.Model):
    """
    Payment records
    """
    PAYMENT_TYPES = [
        ('customer', 'Customer Payment'),
        ('supplier', 'Supplier Payment'),
        ('expense', 'Expense Payment'),
        ('other', 'Other'),
    ]
    
    PAYMENT_METHODS = [
        ('cash', 'Cash'),
        ('check', 'Check'),
        ('credit_card', 'Credit Card'),
        ('bank_transfer', 'Bank Transfer'),
        ('online', 'Online Payment'),
    ]
    
    reference = models.CharField(max_length=50, unique=True, editable=False)
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPES)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS)
    
    # Parties
    customer = models.ForeignKey('sales.Customer', on_delete=models.CASCADE, blank=True, null=True, related_name='payments')
    supplier = models.ForeignKey('sales.Supplier', on_delete=models.CASCADE, blank=True, null=True, related_name='payments')
    
    # Payment details
    amount = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(0.01)])
    payment_date = models.DateField(default=timezone.now)
    
    # Reference information
    check_number = models.CharField(max_length=50, blank=True, null=True)
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    
    notes = models.TextField(blank=True, null=True)
    
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-payment_date']
    
    def save(self, *args, **kwargs):
        if not self.reference:
            self.reference = self.generate_reference()
        super().save(*args, **kwargs)
    
    def generate_reference(self):
        """Generate unique reference number"""
        prefix = 'PAY'
        timestamp = timezone.now().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4())[:6].upper()
        return f"{prefix}-{timestamp}-{unique_id}"
    
    def __str__(self):
        party = self.customer or self.supplier or "Unknown"
        return f"{self.reference} - {party} - ${self.amount}"
