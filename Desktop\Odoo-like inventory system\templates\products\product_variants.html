{% extends 'base.html' %}

{% block title %}{{ product.name }} - Product Variants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-layer-group"></i> Product Variants
        <small class="text-muted">{{ product.name }}</small>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'products:detail' product.pk %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Product
        </a>
        <a href="{% url 'products:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-list"></i> All Products
        </a>
    </div>
</div>

<!-- Product Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded">
                        {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 80px;">
                                <i class="fas fa-image fa-2x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-10">
                        <h5>{{ product.name }}</h5>
                        <p class="text-muted mb-1">SKU: {{ product.sku }}</p>
                        <p class="text-muted mb-1">Category: {{ product.category.name }}</p>
                        <p class="text-muted mb-0">{{ product.description|truncatewords:20 }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add New Variant -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus"></i> Add New Variant
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">Variant Name *</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.sku.id_for_label }}" class="form-label">SKU *</label>
                                {{ form.sku }}
                                {% if form.sku.errors %}
                                    <div class="text-danger small">{{ form.sku.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.barcode.id_for_label }}" class="form-label">Barcode</label>
                                {{ form.barcode }}
                                {% if form.barcode.errors %}
                                    <div class="text-danger small">{{ form.barcode.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.cost_price.id_for_label }}" class="form-label">Cost Price</label>
                                {{ form.cost_price }}
                                {% if form.cost_price.errors %}
                                    <div class="text-danger small">{{ form.cost_price.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.selling_price.id_for_label }}" class="form-label">Selling Price</label>
                                {{ form.selling_price }}
                                {% if form.selling_price.errors %}
                                    <div class="text-danger small">{{ form.selling_price.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.image.id_for_label }}" class="form-label">Variant Image</label>
                                {{ form.image }}
                                {% if form.image.errors %}
                                    <div class="text-danger small">{{ form.image.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Variant Properties -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.color.id_for_label }}" class="form-label">Color</label>
                                {{ form.color }}
                                {% if form.color.errors %}
                                    <div class="text-danger small">{{ form.color.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.size.id_for_label }}" class="form-label">Size</label>
                                {{ form.size }}
                                {% if form.size.errors %}
                                    <div class="text-danger small">{{ form.size.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.material.id_for_label }}" class="form-label">Material</label>
                                {{ form.material }}
                                {% if form.material.errors %}
                                    <div class="text-danger small">{{ form.material.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-check mb-3">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Variant
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Existing Variants -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-layer-group"></i> Existing Variants ({{ variants|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if variants %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Name</th>
                                    <th>SKU</th>
                                    <th>Properties</th>
                                    <th>Cost Price</th>
                                    <th>Selling Price</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for variant in variants %}
                                    <tr>
                                        <td>
                                            {% if variant.image %}
                                                <img src="{{ variant.image.url }}" alt="{{ variant.name }}" class="rounded" width="40" height="40">
                                            {% else %}
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <strong>{{ variant.name }}</strong>
                                            {% if variant.barcode %}
                                                <br><small class="text-muted">{{ variant.barcode }}</small>
                                            {% endif %}
                                        </td>
                                        <td><code>{{ variant.sku }}</code></td>
                                        <td>
                                            {% if variant.color or variant.size or variant.material %}
                                                {% if variant.color %}<span class="badge bg-info me-1">{{ variant.color }}</span>{% endif %}
                                                {% if variant.size %}<span class="badge bg-success me-1">{{ variant.size }}</span>{% endif %}
                                                {% if variant.material %}<span class="badge bg-warning me-1">{{ variant.material }}</span>{% endif %}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if variant.cost_price %}
                                                ${{ variant.cost_price }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if variant.selling_price %}
                                                ${{ variant.selling_price }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if variant.is_active %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Variants Found</h5>
                        <p class="text-muted">This product doesn't have any variants yet. Use the form above to add the first variant.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}
