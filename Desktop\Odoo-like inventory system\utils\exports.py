"""
Export utilities for PDF, Excel, and print functionality
"""
import io
import csv
from datetime import datetime
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.conf import settings
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter


class PDFExporter:
    """PDF export functionality using ReportLab"""
    
    def __init__(self, title="Report", pagesize=A4):
        self.title = title
        self.pagesize = pagesize
        self.styles = getSampleStyleSheet()
        
    def create_pdf_response(self, filename=None):
        """Create HTTP response for PDF download"""
        if not filename:
            filename = f"{self.title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response
    
    def generate_table_pdf(self, data, headers, title=None, filename=None):
        """Generate PDF with table data"""
        response = self.create_pdf_response(filename)
        buffer = io.BytesIO()
        
        doc = SimpleDocTemplate(buffer, pagesize=self.pagesize)
        elements = []
        
        # Title
        if title:
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=self.styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # Center alignment
            )
            elements.append(Paragraph(title, title_style))
        
        # Date
        date_style = ParagraphStyle(
            'DateStyle',
            parent=self.styles['Normal'],
            fontSize=10,
            alignment=2,  # Right alignment
            spaceAfter=20
        )
        elements.append(Paragraph(f"Generated on: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}", date_style))
        
        # Table
        if data:
            table_data = [headers] + data
            table = Table(table_data)
            
            # Table styling
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            elements.append(table)
        else:
            elements.append(Paragraph("No data available", self.styles['Normal']))
        
        doc.build(elements)
        pdf = buffer.getvalue()
        buffer.close()
        response.write(pdf)
        return response


class ExcelExporter:
    """Excel export functionality using openpyxl"""
    
    def __init__(self, title="Report"):
        self.title = title
        
    def create_excel_response(self, filename=None):
        """Create HTTP response for Excel download"""
        if not filename:
            filename = f"{self.title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response
    
    def generate_excel(self, data, headers, title=None, filename=None):
        """Generate Excel file with data"""
        response = self.create_excel_response(filename)
        
        wb = Workbook()
        ws = wb.active
        ws.title = self.title[:31]  # Excel sheet name limit
        
        # Title
        if title:
            ws.merge_cells('A1:' + get_column_letter(len(headers)) + '1')
            ws['A1'] = title
            ws['A1'].font = Font(size=16, bold=True)
            ws['A1'].alignment = Alignment(horizontal='center')
            ws.row_dimensions[1].height = 25
            start_row = 3
        else:
            start_row = 1
        
        # Date
        ws.merge_cells(f'A{start_row}:' + get_column_letter(len(headers)) + str(start_row))
        ws[f'A{start_row}'] = f"Generated on: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}"
        ws[f'A{start_row}'].font = Font(size=10, italic=True)
        ws[f'A{start_row}'].alignment = Alignment(horizontal='right')
        
        # Headers
        header_row = start_row + 2
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=header_row, column=col, value=header)
            cell.font = Font(bold=True, color='FFFFFF')
            cell.fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
            cell.alignment = Alignment(horizontal='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        
        # Data
        for row_idx, row_data in enumerate(data, header_row + 1):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                if row_idx % 2 == 0:
                    cell.fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        wb.save(response)
        return response


class CSVExporter:
    """CSV export functionality"""
    
    def __init__(self, title="Report"):
        self.title = title
        
    def generate_csv(self, data, headers, filename=None):
        """Generate CSV file with data"""
        if not filename:
            filename = f"{self.title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        writer = csv.writer(response)
        
        # Add title and date
        writer.writerow([f"{self.title} - Generated on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}"])
        writer.writerow([])  # Empty row
        
        # Headers
        writer.writerow(headers)
        
        # Data
        for row in data:
            writer.writerow(row)
        
        return response


def export_queryset_to_pdf(queryset, fields, title, filename=None):
    """Export Django queryset to PDF"""
    headers = [field.replace('_', ' ').title() for field in fields]
    data = []
    
    for obj in queryset:
        row = []
        for field in fields:
            value = getattr(obj, field, '')
            if hasattr(value, 'strftime'):  # Date/datetime field
                value = value.strftime('%Y-%m-%d %H:%M')
            elif hasattr(value, '__call__'):  # Method
                value = value()
            row.append(str(value))
        data.append(row)
    
    exporter = PDFExporter(title)
    return exporter.generate_table_pdf(data, headers, title, filename)


def export_queryset_to_excel(queryset, fields, title, filename=None):
    """Export Django queryset to Excel"""
    headers = [field.replace('_', ' ').title() for field in fields]
    data = []
    
    for obj in queryset:
        row = []
        for field in fields:
            value = getattr(obj, field, '')
            if hasattr(value, 'strftime'):  # Date/datetime field
                value = value.strftime('%Y-%m-%d %H:%M')
            elif hasattr(value, '__call__'):  # Method
                value = value()
            row.append(str(value))
        data.append(row)
    
    exporter = ExcelExporter(title)
    return exporter.generate_excel(data, headers, title, filename)


def export_queryset_to_csv(queryset, fields, title, filename=None):
    """Export Django queryset to CSV"""
    headers = [field.replace('_', ' ').title() for field in fields]
    data = []
    
    for obj in queryset:
        row = []
        for field in fields:
            value = getattr(obj, field, '')
            if hasattr(value, 'strftime'):  # Date/datetime field
                value = value.strftime('%Y-%m-%d %H:%M')
            elif hasattr(value, '__call__'):  # Method
                value = value()
            row.append(str(value))
        data.append(row)
    
    exporter = CSVExporter(title)
    return exporter.generate_csv(data, headers, filename)
