from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import api_views

router = DefaultRouter()
router.register(r'warehouses', api_views.WarehouseViewSet)
router.register(r'locations', api_views.LocationViewSet)
router.register(r'stock', api_views.StockViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('stock/low-stock/', api_views.LowStockAPIView.as_view(), name='low_stock'),
    path('stock/by-warehouse/<int:warehouse_id>/', api_views.StockByWarehouseAPIView.as_view(), name='stock_by_warehouse'),
    path('locations/by-warehouse/<int:warehouse_id>/', api_views.LocationsByWarehouseAPIView.as_view(), name='locations_by_warehouse'),
]
