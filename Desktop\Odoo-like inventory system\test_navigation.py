#!/usr/bin/env python
"""
Test the enhanced navigation system
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.test import Client
from users.models import User

def test_navigation_system():
    """Test the enhanced navigation system"""
    print("🧭 Testing Enhanced Navigation System...\n")
    
    try:
        # Create test client
        client = Client()
        
        # Get admin user
        user = User.objects.filter(is_active=True, role='admin').first()
        if not user:
            print("❌ No admin user found for testing")
            return False
        
        # Login the user
        client.force_login(user)
        print(f"✅ Logged in as: {user.username}")
        
        # Test main navigation links
        navigation_links = [
            ('/dashboard/', 'Dashboard'),
            ('/products/', 'Products'),
            ('/warehouses/list/', 'Warehouses'),
            ('/sales/orders/', 'Sales Orders'),
            ('/sales/customers/', 'Customers'),
            ('/dashboard/reports/', 'Reports & Analytics'),
        ]
        
        print("\n🔗 Testing Navigation Links:")
        all_working = True
        
        for url, name in navigation_links:
            try:
                response = client.get(url)
                
                if response.status_code == 200:
                    content = response.content.decode('utf-8')
                    
                    # Check if navigation elements are present
                    nav_checks = [
                        ('Dashboard Link', 'Dashboard' in content),
                        ('Products Link', 'Products' in content),
                        ('Warehouses Link', 'Warehouses' in content),
                        ('Sales Dropdown', 'Sales' in content),
                        ('Reports Link', 'Reports' in content),
                        ('User Menu', user.username in content),
                    ]
                    
                    missing_nav = []
                    for check_name, present in nav_checks:
                        if not present:
                            missing_nav.append(check_name)
                    
                    if missing_nav:
                        print(f"   ⚠️  {name}: Missing navigation elements: {missing_nav}")
                    else:
                        print(f"   ✅ {name}: All navigation elements present")
                        
                elif response.status_code == 302:
                    print(f"   🔄 {name}: Redirected (Status: {response.status_code})")
                else:
                    print(f"   ❌ {name}: Error (Status: {response.status_code})")
                    all_working = False
                    
            except Exception as e:
                print(f"   ❌ {name}: Exception - {str(e)}")
                all_working = False
        
        # Test dropdown functionality by checking HTML structure
        print(f"\n📋 Testing Dropdown Structure:")
        
        response = client.get('/dashboard/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            dropdown_checks = [
                ('Sales Dropdown Toggle', 'dropdown-toggle' in content and 'salesDropdown' in content),
                ('Sales Orders in Dropdown', 'Sales Orders' in content),
                ('Customers in Dropdown', 'Customers' in content),
                ('User Dropdown', 'userDropdown' in content),
                ('Admin Panel Link', 'Admin Panel' in content),
                ('Logout Link', 'Logout' in content),
            ]
            
            for check_name, present in dropdown_checks:
                status = "✅" if present else "❌"
                print(f"   {status} {check_name}: {'Present' if present else 'Missing'}")
                if not present:
                    all_working = False
        
        # Test responsive navigation
        print(f"\n📱 Testing Responsive Elements:")
        
        responsive_checks = [
            ('Navbar Toggle', 'navbar-toggler' in content),
            ('Collapse Navigation', 'navbar-collapse' in content),
            ('Bootstrap Classes', 'navbar-nav' in content),
        ]
        
        for check_name, present in responsive_checks:
            status = "✅" if present else "❌"
            print(f"   {status} {check_name}: {'Present' if present else 'Missing'}")
        
        return all_working
        
    except Exception as e:
        print(f"❌ Error during navigation testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Enhanced Navigation System Testing\n")
    
    success = test_navigation_system()
    
    print(f"\n📋 Final Results:")
    if success:
        print(f"🎉 SUCCESS: Enhanced navigation is working perfectly!")
        print(f"✅ All main navigation links working")
        print(f"✅ Sales dropdown with orders and customers")
        print(f"✅ Reports link easily accessible")
        print(f"✅ User menu with admin and logout")
        print(f"✅ Responsive design elements present")
        print(f"\n🧭 Your enhanced navigation includes:")
        print(f"   📊 Dashboard - Main overview")
        print(f"   📦 Products - Product management")
        print(f"   🏢 Warehouses - Warehouse management")
        print(f"   🛒 Sales ▼ - Dropdown with:")
        print(f"      📋 Sales Orders")
        print(f"      👥 Customers")
        print(f"   📈 Reports - Analytics dashboard")
        print(f"   👤 User Menu ▼ - Dropdown with:")
        print(f"      ⚙️ Admin Panel")
        print(f"      🚪 Logout")
    else:
        print(f"⚠️  Navigation system needs attention")
        print(f"🔧 Review the issues listed above")
