from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Count, Sum
from .models import Warehouse, Location, Stock


@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'code', 'warehouse_type', 'city', 'state', 'manager', 
        'total_products_display', 'is_active', 'is_default', 'created_at'
    )
    list_filter = ('warehouse_type', 'is_active', 'is_default', 'state', 'country', 'created_at')
    search_fields = ('name', 'code', 'city', 'address_line1')
    ordering = ('name',)
    list_editable = ('is_active', 'is_default')
    readonly_fields = ('created_at', 'updated_at', 'total_products_display', 'total_stock_value_display')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'warehouse_type', 'manager')
        }),
        ('Address', {
            'fields': ('address_line1', 'address_line2', 'city', 'state', 'postal_code', 'country')
        }),
        ('Contact Information', {
            'fields': ('phone', 'email')
        }),
        ('Settings', {
            'fields': ('capacity', 'is_active', 'is_default')
        }),
        ('Statistics', {
            'fields': ('total_products_display', 'total_stock_value_display'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('manager').annotate(
            total_products=Count('stock', distinct=True)
        )
    
    def total_products_display(self, obj):
        """Display total number of products in warehouse"""
        return obj.total_products if hasattr(obj, 'total_products') else obj.stock_set.count()
    total_products_display.short_description = 'Total Products'
    total_products_display.admin_order_field = 'total_products'
    
    def total_stock_value_display(self, obj):
        """Display total stock value"""
        return f"${obj.total_stock_value:,.2f}"
    total_stock_value_display.short_description = 'Total Stock Value'
    
    actions = ['activate_warehouses', 'deactivate_warehouses']
    
    def activate_warehouses(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} warehouses were successfully activated.')
    activate_warehouses.short_description = "Activate selected warehouses"
    
    def deactivate_warehouses(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} warehouses were successfully deactivated.')
    deactivate_warehouses.short_description = "Deactivate selected warehouses"


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = (
        'warehouse', 'code', 'name', 'location_type', 'parent', 
        'capacity', 'utilization_display', 'is_active', 'is_pickable', 'is_receivable'
    )
    list_filter = (
        'warehouse', 'location_type', 'is_active', 'is_pickable', 'is_receivable', 'created_at'
    )
    search_fields = ('name', 'code', 'warehouse__name')
    ordering = ('warehouse__name', 'code')
    list_editable = ('is_active', 'is_pickable', 'is_receivable')
    readonly_fields = ('created_at', 'updated_at', 'utilization_display', 'stock_count_display')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('warehouse', 'name', 'code', 'location_type', 'parent')
        }),
        ('Physical Properties', {
            'fields': ('capacity', 'length', 'width', 'height'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('is_active', 'is_pickable', 'is_receivable')
        }),
        ('Statistics', {
            'fields': ('utilization_display', 'stock_count_display'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('warehouse', 'parent').annotate(
            stock_count=Count('stock')
        )
    
    def utilization_display(self, obj):
        """Display current utilization percentage"""
        utilization = obj.current_utilization
        if utilization > 90:
            color = 'red'
        elif utilization > 70:
            color = 'orange'
        else:
            color = 'green'
        return format_html('<span style="color: {};">{:.1f}%</span>', color, utilization)
    utilization_display.short_description = 'Utilization'
    
    def stock_count_display(self, obj):
        """Display number of stock records in location"""
        return obj.stock_count if hasattr(obj, 'stock_count') else obj.stock_set.count()
    stock_count_display.short_description = 'Stock Records'
    stock_count_display.admin_order_field = 'stock_count'


@admin.register(Stock)
class StockAdmin(admin.ModelAdmin):
    list_display = (
        'product', 'product_variant', 'warehouse', 'location', 
        'quantity', 'reserved_quantity', 'available_quantity_display', 
        'stock_value_display', 'last_movement'
    )
    list_filter = (
        'warehouse', 'location', 'product__category', 'product__brand', 
        'last_movement', 'created_at'
    )
    search_fields = (
        'product__name', 'product__sku', 'product__barcode', 
        'product_variant__name', 'product_variant__sku'
    )
    ordering = ('-last_movement',)
    readonly_fields = (
        'created_at', 'updated_at', 'last_movement', 'available_quantity_display', 
        'stock_value_display', 'is_low_stock_display'
    )
    
    fieldsets = (
        ('Product Information', {
            'fields': ('product', 'product_variant')
        }),
        ('Location', {
            'fields': ('warehouse', 'location')
        }),
        ('Stock Levels', {
            'fields': ('quantity', 'reserved_quantity', 'available_quantity_display')
        }),
        ('Stock Analysis', {
            'fields': ('stock_value_display', 'is_low_stock_display'),
            'classes': ('collapse',)
        }),
        ('Tracking', {
            'fields': ('last_counted', 'last_movement'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'product', 'product_variant', 'warehouse', 'location'
        )
    
    def available_quantity_display(self, obj):
        """Display available quantity with color coding"""
        available = obj.available_quantity
        if available <= 0:
            return format_html('<span style="color: red;">{}</span>', available)
        elif obj.is_low_stock:
            return format_html('<span style="color: orange;">{}</span>', available)
        else:
            return format_html('<span style="color: green;">{}</span>', available)
    available_quantity_display.short_description = 'Available'
    
    def stock_value_display(self, obj):
        """Display stock value"""
        return f"${obj.stock_value:,.2f}"
    stock_value_display.short_description = 'Stock Value'
    
    def is_low_stock_display(self, obj):
        """Display low stock status"""
        if obj.is_low_stock:
            return format_html('<span style="color: red;">⚠ Low Stock</span>')
        return format_html('<span style="color: green;">✓ Normal</span>')
    is_low_stock_display.short_description = 'Stock Status'
    
    actions = ['mark_for_recount', 'reset_reserved_quantity']
    
    def mark_for_recount(self, request, queryset):
        """Mark selected stock records for physical recount"""
        from django.utils import timezone
        updated = queryset.update(last_counted=None)
        self.message_user(request, f'{updated} stock records marked for recount.')
    mark_for_recount.short_description = "Mark for physical recount"
    
    def reset_reserved_quantity(self, request, queryset):
        """Reset reserved quantity to zero"""
        updated = queryset.update(reserved_quantity=0)
        self.message_user(request, f'Reserved quantity reset for {updated} stock records.')
    reset_reserved_quantity.short_description = "Reset reserved quantity"
