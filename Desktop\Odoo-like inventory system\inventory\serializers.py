from rest_framework import serializers
from .models import StockMovement, StockAdjustment, StockAdjustmentLine


class StockMovementSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    from_warehouse_name = serializers.CharField(source='from_warehouse.name', read_only=True)
    to_warehouse_name = serializers.CharField(source='to_warehouse.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = StockMovement
        fields = [
            'id', 'reference', 'product', 'product_name', 'product_sku',
            'product_variant', 'movement_type', 'reason', 'quantity',
            'from_warehouse', 'from_warehouse_name', 'from_location',
            'to_warehouse', 'to_warehouse_name', 'to_location',
            'unit_cost', 'total_cost', 'is_processed', 'processed_at',
            'notes', 'created_by', 'created_by_name', 'created_at'
        ]


class StockAdjustmentLineSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    
    class Meta:
        model = StockAdjustmentLine
        fields = [
            'id', 'product', 'product_name', 'product_sku', 'product_variant',
            'location', 'location_name', 'expected_quantity', 'actual_quantity',
            'difference', 'unit_cost', 'total_cost_impact', 'notes'
        ]


class StockAdjustmentSerializer(serializers.ModelSerializer):
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    lines = StockAdjustmentLineSerializer(many=True, read_only=True)
    
    class Meta:
        model = StockAdjustment
        fields = [
            'id', 'reference', 'adjustment_type', 'warehouse', 'warehouse_name',
            'reason', 'notes', 'is_approved', 'approved_by', 'approved_by_name',
            'approved_at', 'created_by', 'created_by_name', 'created_at',
            'lines'
        ]
