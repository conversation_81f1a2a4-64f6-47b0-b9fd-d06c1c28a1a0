{% extends 'base.html' %}

{% block title %}Stock Movements - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Stock Movements</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if user.can_manage_inventory %}
        <div class="btn-group me-2">
            <a href="{% url 'inventory:quick_stock_in' %}" class="btn btn-success">
                <i class="fas fa-plus-circle"></i> Stock In
            </a>
            <a href="{% url 'inventory:quick_stock_out' %}" class="btn btn-danger">
                <i class="fas fa-minus-circle"></i> Stock Out
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-md-12">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <input type="text" name="search" class="form-control" placeholder="Search movements..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-2">
                <select name="movement_type" class="form-control">
                    <option value="">All Types</option>
                    <option value="in" {% if request.GET.movement_type == 'in' %}selected{% endif %}>Stock In</option>
                    <option value="out" {% if request.GET.movement_type == 'out' %}selected{% endif %}>Stock Out</option>
                    <option value="transfer" {% if request.GET.movement_type == 'transfer' %}selected{% endif %}>Transfer</option>
                    <option value="adjustment" {% if request.GET.movement_type == 'adjustment' %}selected{% endif %}>Adjustment</option>
                </select>
            </div>
            <div class="col-md-2">
                <select name="warehouse" class="form-control">
                    <option value="">All Warehouses</option>
                    {% for warehouse in warehouses %}
                        <option value="{{ warehouse.id }}" {% if request.GET.warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>
                            {{ warehouse.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}">
            </div>
            <div class="col-md-2">
                <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}">
            </div>
            <div class="col-md-1">
                <button type="submit" class="btn btn-outline-secondary">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Movements Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Stock Movements ({{ movements|length }})</h5>
    </div>
    <div class="card-body">
        {% if movements %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Reference</th>
                            <th>Product</th>
                            <th>Type</th>
                            <th>Quantity</th>
                            <th>From</th>
                            <th>To</th>
                            <th>User</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in movements %}
                            <tr>
                                <td>{{ movement.created_at|date:"M d, Y H:i" }}</td>
                                <td><code>{{ movement.reference }}</code></td>
                                <td>
                                    <strong>{{ movement.product.name|truncatechars:30 }}</strong>
                                    <br><small class="text-muted">{{ movement.product.sku }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-{% if movement.movement_type == 'in' %}success{% elif movement.movement_type == 'out' %}danger{% elif movement.movement_type == 'transfer' %}info{% else %}warning{% endif %}">
                                        {{ movement.get_movement_type_display }}
                                    </span>
                                </td>
                                <td>
                                    {% if movement.movement_type == 'out' %}-{% endif %}{{ movement.quantity }}
                                </td>
                                <td>
                                    {% if movement.from_warehouse %}
                                        {{ movement.from_warehouse.name }}
                                        {% if movement.from_location %}
                                            <br><small class="text-muted">{{ movement.from_location.name }}</small>
                                        {% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if movement.to_warehouse %}
                                        {{ movement.to_warehouse.name }}
                                        {% if movement.to_location %}
                                            <br><small class="text-muted">{{ movement.to_location.name }}</small>
                                        {% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ movement.created_by.get_full_name|default:movement.created_by.username }}</td>
                                <td>{{ movement.notes|truncatechars:30|default:"-" }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="Movements pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No stock movements found</h5>
                <p class="text-muted">No movements match your search criteria.</p>
                {% if user.can_manage_inventory %}
                    <div class="btn-group">
                        <a href="{% url 'inventory:quick_stock_in' %}" class="btn btn-success">
                            <i class="fas fa-plus-circle"></i> Add Stock
                        </a>
                        <a href="{% url 'inventory:quick_stock_out' %}" class="btn btn-danger">
                            <i class="fas fa-minus-circle"></i> Remove Stock
                        </a>
                    </div>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Movement Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ total_in }}</h5>
                <p class="card-text text-muted">Stock In</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">{{ total_out }}</h5>
                <p class="card-text text-muted">Stock Out</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ total_transfers }}</h5>
                <p class="card-text text-muted">Transfers</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ total_adjustments }}</h5>
                <p class="card-text text-muted">Adjustments</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}
