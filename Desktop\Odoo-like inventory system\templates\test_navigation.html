{% extends 'base.html' %}

{% block title %}Navigation Test - Inventory Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1>Navigation Test Page</h1>
    <p>Click on each link below to test if all navigation is working correctly:</p>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Main Navigation</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <a href="{% url 'inventory:dashboard' %}" class="btn btn-outline-primary btn-sm">Dashboard</a>
                            <span class="text-muted">- Main dashboard</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'products:list' %}" class="btn btn-outline-primary btn-sm">Products</a>
                            <span class="text-muted">- Product catalog</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'warehouses:list' %}" class="btn btn-outline-primary btn-sm">Warehouses</a>
                            <span class="text-muted">- Warehouse management</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'inventory:movement_list' %}" class="btn btn-outline-primary btn-sm">Stock Movements</a>
                            <span class="text-muted">- Inventory movements</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'sales:order_list' %}" class="btn btn-outline-success btn-sm">Sales Orders</a>
                            <span class="text-muted">- Sales management</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-success btn-sm">Customers</a>
                            <span class="text-muted">- Customer management</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'inventory:reports' %}" class="btn btn-outline-info btn-sm">Reports</a>
                            <span class="text-muted">- Analytics dashboard</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'users:list' %}" class="btn btn-outline-warning btn-sm">Users</a>
                            <span class="text-muted">- User management</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <a href="{% url 'inventory:quick_stock_in' %}" class="btn btn-outline-success btn-sm">Quick Stock In</a>
                            <span class="text-muted">- Add inventory</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'inventory:quick_stock_out' %}" class="btn btn-outline-danger btn-sm">Quick Stock Out</a>
                            <span class="text-muted">- Remove inventory</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'inventory:stock_transfer' %}" class="btn btn-outline-info btn-sm">Stock Transfer</a>
                            <span class="text-muted">- Transfer between warehouses</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'products:create' %}" class="btn btn-outline-primary btn-sm">Add Product</a>
                            <span class="text-muted">- Create new product</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'warehouses:create' %}" class="btn btn-outline-primary btn-sm">Add Warehouse</a>
                            <span class="text-muted">- Create new warehouse</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'sales:order_create' %}" class="btn btn-outline-success btn-sm">New Sales Order</a>
                            <span class="text-muted">- Create sales order</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'sales:customer_create' %}" class="btn btn-outline-success btn-sm">Add Customer</a>
                            <span class="text-muted">- Create new customer</span>
                        </li>
                        <li class="list-group-item">
                            <a href="{% url 'users:create' %}" class="btn btn-outline-warning btn-sm">Add User</a>
                            <span class="text-muted">- Create new user</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Additional Pages</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>Product Management</h6>
                            <ul class="list-unstyled">
                                <li><a href="{% url 'products:category_list' %}" class="btn btn-outline-secondary btn-sm mb-1">Categories</a></li>
                                <li><a href="{% url 'products:brand_list' %}" class="btn btn-outline-secondary btn-sm mb-1">Brands</a></li>
                                <li><a href="{% url 'products:category_create' %}" class="btn btn-outline-secondary btn-sm mb-1">Add Category</a></li>
                                <li><a href="{% url 'products:brand_create' %}" class="btn btn-outline-secondary btn-sm mb-1">Add Brand</a></li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>Inventory Operations</h6>
                            <ul class="list-unstyled">
                                <li><a href="{% url 'inventory:adjustment_list' %}" class="btn btn-outline-secondary btn-sm mb-1">Stock Adjustments</a></li>
                                <li><a href="{% url 'inventory:adjustment_create' %}" class="btn btn-outline-secondary btn-sm mb-1">New Adjustment</a></li>
                                <li><a href="{% url 'inventory:stock_report' %}" class="btn btn-outline-secondary btn-sm mb-1">Stock Report</a></li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>User Management</h6>
                            <ul class="list-unstyled">
                                <li><a href="{% url 'users:profile' %}" class="btn btn-outline-secondary btn-sm mb-1">My Profile</a></li>
                                <li><a href="{% url 'users:logout' %}" class="btn btn-outline-danger btn-sm mb-1">Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="alert alert-info mt-4">
        <h6>Testing Instructions:</h6>
        <ol>
            <li>Click on each link above</li>
            <li>Verify that the page loads correctly (no 404 errors)</li>
            <li>Check that the navigation menu is working</li>
            <li>Test responsive design by resizing the browser window</li>
        </ol>
        <p><strong>Expected Result:</strong> All links should work and load their respective pages without errors.</p>
    </div>
</div>
{% endblock %}
