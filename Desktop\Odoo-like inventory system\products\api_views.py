from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q
from .models import Product, Category, Brand, ProductVariant
from .serializers import ProductSerializer, CategorySerializer, BrandSerializer, ProductVariantSerializer


class ProductViewSet(viewsets.ModelViewSet):
    """API ViewSet for products"""
    queryset = Product.objects.filter(is_active=True)
    serializer_class = ProductSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(sku__icontains=search) |
                Q(barcode__icontains=search)
            )
        return queryset
    
    @action(detail=True, methods=['post'])
    def generate_barcode(self, request, pk=None):
        """Generate barcode for product"""
        product = self.get_object()
        # Implementation for barcode generation
        return Response({'message': 'Barcode generated successfully'})


class CategoryViewSet(viewsets.ModelViewSet):
    """API ViewSet for categories"""
    queryset = Category.objects.filter(is_active=True)
    serializer_class = CategorySerializer


class BrandViewSet(viewsets.ModelViewSet):
    """API ViewSet for brands"""
    queryset = Brand.objects.filter(is_active=True)
    serializer_class = BrandSerializer


class ProductVariantViewSet(viewsets.ModelViewSet):
    """API ViewSet for product variants"""
    queryset = ProductVariant.objects.filter(is_active=True)
    serializer_class = ProductVariantSerializer


class ProductSearchAPIView(APIView):
    """API for product search"""
    
    def get(self, request):
        query = request.query_params.get('q', '')
        products = Product.objects.filter(
            Q(name__icontains=query) | Q(sku__icontains=query),
            is_active=True
        )[:10]
        
        results = []
        for product in products:
            results.append({
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'price': str(product.selling_price),
            })
        
        return Response({'results': results})


class ProductByBarcodeAPIView(APIView):
    """API to get product by barcode"""
    
    def get(self, request, barcode):
        try:
            product = Product.objects.get(barcode=barcode, is_active=True)
            serializer = ProductSerializer(product)
            return Response(serializer.data)
        except Product.DoesNotExist:
            return Response({'error': 'Product not found'}, status=status.HTTP_404_NOT_FOUND)


class LowStockProductsAPIView(APIView):
    """API for low stock products"""
    
    def get(self, request):
        from django.db.models import F
        low_stock_products = Product.objects.filter(
            stock__quantity__lte=F('min_stock_level'),
            is_active=True
        ).distinct()
        
        serializer = ProductSerializer(low_stock_products, many=True)
        return Response(serializer.data)
