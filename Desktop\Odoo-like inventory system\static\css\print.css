/* Print-friendly styles for all pages */

@media print {
    /* Hide non-essential elements */
    .navbar,
    .sidebar,
    .btn-toolbar,
    .pagination,
    .no-print,
    .export-buttons,
    .dropdown,
    .modal,
    .alert-dismissible .btn-close,
    .form-actions,
    .breadcrumb,
    .footer {
        display: none !important;
    }
    
    /* Page setup */
    @page {
        size: A4;
        margin: 0.5in;
    }
    
    body {
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: white;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    /* Main content adjustments */
    .container-fluid,
    .container {
        width: 100% !important;
        max-width: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    /* Headers and titles */
    h1, h2, h3, h4, h5, h6 {
        color: #000;
        page-break-after: avoid;
        margin-top: 0;
    }
    
    .page-title {
        font-size: 18pt;
        font-weight: bold;
        text-align: center;
        margin-bottom: 20pt;
        border-bottom: 2pt solid #000;
        padding-bottom: 10pt;
    }
    
    /* Tables */
    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20pt;
        page-break-inside: avoid;
    }
    
    table th,
    table td {
        border: 1pt solid #000;
        padding: 8pt;
        text-align: left;
        vertical-align: top;
    }
    
    table th {
        background-color: #f0f0f0 !important;
        font-weight: bold;
        text-align: center;
    }
    
    table tr:nth-child(even) {
        background-color: #f9f9f9 !important;
    }
    
    /* Cards */
    .card {
        border: 1pt solid #000;
        margin-bottom: 15pt;
        page-break-inside: avoid;
    }
    
    .card-header {
        background-color: #e9ecef !important;
        border-bottom: 1pt solid #000;
        padding: 10pt;
        font-weight: bold;
    }
    
    .card-body {
        padding: 10pt;
    }
    
    /* Statistics cards */
    .stats-card {
        display: inline-block;
        width: 23%;
        margin-right: 2%;
        border: 1pt solid #000;
        padding: 10pt;
        text-align: center;
        vertical-align: top;
    }
    
    .stats-card h5 {
        font-size: 14pt;
        margin-bottom: 5pt;
    }
    
    .stats-card .card-text {
        font-size: 10pt;
        color: #666;
    }
    
    /* Forms */
    .form-control,
    .form-select {
        border: 1pt solid #000;
        padding: 5pt;
        background: white;
    }
    
    .form-label {
        font-weight: bold;
        margin-bottom: 3pt;
    }
    
    /* Lists */
    .list-group-item {
        border: 1pt solid #000;
        padding: 8pt;
        margin-bottom: 2pt;
    }
    
    /* Badges and status indicators */
    .badge {
        border: 1pt solid #000;
        padding: 2pt 6pt;
        font-weight: bold;
    }
    
    .badge.bg-success { background-color: #d4edda !important; color: #155724 !important; }
    .badge.bg-warning { background-color: #fff3cd !important; color: #856404 !important; }
    .badge.bg-danger { background-color: #f8d7da !important; color: #721c24 !important; }
    .badge.bg-info { background-color: #d1ecf1 !important; color: #0c5460 !important; }
    .badge.bg-primary { background-color: #d1ecf1 !important; color: #084298 !important; }
    
    /* Page breaks */
    .page-break {
        page-break-before: always;
    }
    
    .page-break-after {
        page-break-after: always;
    }
    
    .no-page-break {
        page-break-inside: avoid;
    }
    
    /* Print header */
    .print-header {
        display: block !important;
        text-align: center;
        margin-bottom: 20pt;
        border-bottom: 2pt solid #000;
        padding-bottom: 10pt;
    }
    
    .print-header h1 {
        font-size: 20pt;
        margin-bottom: 5pt;
    }
    
    .print-header .company-info {
        font-size: 10pt;
        color: #666;
    }
    
    /* Print footer */
    .print-footer {
        display: block !important;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 10pt;
        border-top: 1pt solid #000;
        padding-top: 5pt;
    }
    
    /* Specific page adjustments */
    .dashboard-stats {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    
    .dashboard-stats .card {
        width: 48%;
        margin-bottom: 10pt;
    }
    
    /* Charts - hide or replace with text */
    canvas,
    .chart-container {
        display: none !important;
    }
    
    .chart-placeholder {
        display: block !important;
        text-align: center;
        padding: 20pt;
        border: 1pt dashed #000;
        font-style: italic;
    }
    
    /* Images */
    img {
        max-width: 100%;
        height: auto;
    }
    
    /* Links */
    a {
        color: #000;
        text-decoration: none;
    }
    
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 10pt;
        color: #666;
    }
    
    /* Utility classes */
    .print-only {
        display: block !important;
    }
    
    .screen-only {
        display: none !important;
    }
    
    /* Responsive adjustments for print */
    .row {
        display: block !important;
    }
    
    .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
    .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
    .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
    .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
    .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
    .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
    .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
    .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
        width: 100% !important;
        float: none !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
    
    /* Two-column layout for forms */
    .print-two-column .col-md-6 {
        width: 48% !important;
        float: left !important;
        margin-right: 2% !important;
    }
    
    .print-two-column .col-md-6:nth-child(even) {
        margin-right: 0 !important;
    }
    
    /* Clear floats */
    .clearfix:after {
        content: "";
        display: table;
        clear: both;
    }
}
