{% extends 'base.html' %}

{% block title %}Sales Orders - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Sales Orders</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% include 'components/export_buttons.html' with export_url=request.get_full_path page_title='Sales Orders Report' %}
        {% if user.can_manage_sales %}
        <a href="{% url 'sales:order_create' %}" class="btn btn-primary ms-2">
            <i class="fas fa-plus"></i> New Order
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-12">
        <form method="get" class="row g-3">
            <div class="col-md-3 col-sm-6">
                <input type="text" name="search" class="form-control" placeholder="Search orders..." value="{{ request.GET.search }}">
            </div>
            <div class="col-md-2 col-sm-6">
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    {% for value, label in order_statuses %}
                        <option value="{{ value }}" {% if request.GET.status == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 col-sm-6">
                <input type="date" name="date_from" class="form-control" value="{{ request.GET.date_from }}" placeholder="From Date">
            </div>
            <div class="col-md-2 col-sm-6">
                <input type="date" name="date_to" class="form-control" value="{{ request.GET.date_to }}" placeholder="To Date">
            </div>
            <div class="col-md-2 col-sm-6">
                <select name="customer" class="form-control">
                    <option value="">All Customers</option>
                    {% for customer in customers %}
                        <option value="{{ customer.id }}" {% if request.GET.customer == customer.id|stringformat:"s" %}selected{% endif %}>
                            {{ customer.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-1 col-sm-6">
                <button type="submit" class="btn btn-outline-secondary w-100">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Orders Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Sales Orders ({{ orders|length }})</h5>
    </div>
    <div class="card-body">
        {% if orders %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th class="d-none d-md-table-cell">Date</th>
                            <th class="d-none d-lg-table-cell">Items</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                            <tr>
                                <td>
                                    <strong>{{ order.order_number }}</strong>
                                    {% if order.priority == 'high' %}
                                        <span class="badge bg-danger ms-1">High Priority</span>
                                    {% elif order.priority == 'medium' %}
                                        <span class="badge bg-warning ms-1">Medium Priority</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ order.customer.name }}</strong>
                                        <div class="d-md-none">
                                            <small class="text-muted">{{ order.order_date|date:"M d, Y" }}</small>
                                        </div>
                                        <div class="d-none d-md-block">
                                            <small class="text-muted">{{ order.customer.email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">
                                    {{ order.order_date|date:"M d, Y" }}
                                    <br><small class="text-muted">{{ order.created_at|date:"H:i" }}</small>
                                </td>
                                <td class="d-none d-lg-table-cell">{{ order.items.count }} items</td>
                                <td>${{ order.total_amount|floatformat:2 }}</td>
                                <td>
                                    <span class="badge bg-{% if order.status == 'pending' %}warning{% elif order.status == 'confirmed' %}info{% elif order.status == 'shipped' %}primary{% elif order.status == 'delivered' %}success{% elif order.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                        {{ order.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'sales:order_detail' order.pk %}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if user.can_manage_sales and order.status != 'cancelled' %}
                                            <a href="{% url 'sales:order_edit' order.pk %}" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No sales orders found</h5>
                <p class="text-muted">No orders match your search criteria.</p>
                {% if user.can_manage_sales %}
                    <a href="{% url 'sales:order_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create First Order
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Order Statistics -->
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <h5 class="card-title">{{ total_orders }}</h5>
                <p class="card-text text-muted">Total Orders</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <h5 class="card-title">${{ total_revenue|floatformat:2 }}</h5>
                <p class="card-text text-muted">Total Revenue</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <h5 class="card-title">{{ pending_orders }}</h5>
                <p class="card-text text-muted">Pending Orders</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <h5 class="card-title">${{ avg_order_value|floatformat:2 }}</h5>
                <p class="card-text text-muted">Avg Order Value</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}
