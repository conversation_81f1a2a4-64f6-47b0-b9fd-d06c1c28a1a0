# Generated by Django 5.2.4 on 2025-07-17 08:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0002_initial'),
        ('sales', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseorder',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='purchaseorderline',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='sales.purchaseorder'),
        ),
        migrations.AddField(
            model_name='purchaseorderline',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product'),
        ),
        migrations.AddField(
            model_name='purchaseorderline',
            name='product_variant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productvariant'),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_orders', to='sales.customer'),
        ),
        migrations.AddField(
            model_name='salesorderline',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='sales.salesorder'),
        ),
        migrations.AddField(
            model_name='salesorderline',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product'),
        ),
        migrations.AddField(
            model_name='salesorderline',
            name='product_variant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productvariant'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='sales.supplier'),
        ),
        migrations.AddIndex(
            model_name='salesorder',
            index=models.Index(fields=['order_number'], name='sales_sales_order_n_78c57a_idx'),
        ),
        migrations.AddIndex(
            model_name='salesorder',
            index=models.Index(fields=['customer', 'status'], name='sales_sales_custome_f3a5e5_idx'),
        ),
        migrations.AddIndex(
            model_name='salesorder',
            index=models.Index(fields=['order_date'], name='sales_sales_order_d_55e46c_idx'),
        ),
    ]
