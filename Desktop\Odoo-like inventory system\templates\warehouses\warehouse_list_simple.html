<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Warehouses - Inventory Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard/">
                <i class="fas fa-boxes"></i> Inventory System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard/">Dashboard</a>
                <a class="nav-link" href="/products/">Products</a>
                <a class="nav-link" href="/warehouses/list/">Warehouses</a>
                <a class="nav-link" href="/admin/">Admin</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                <i class="fas fa-warehouse"></i> Warehouse Management
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <a href="/warehouses/create/" class="btn btn-sm btn-success">
                        <i class="fas fa-plus"></i> Add Warehouse
                    </a>
                    <a href="/warehouses/" class="btn btn-sm btn-info">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        <div class="alert alert-success" role="alert">
            <h4 class="alert-heading">🎉 Warehouses Page is Working!</h4>
            <p>The warehouse management system is now functional. Template recursion issue has been resolved.</p>
            <hr>
            <p class="mb-0">
                <strong>Status:</strong> ✅ Template fixed and working<br>
                <strong>Features:</strong> ✅ Warehouse listing, creation, and management available
            </p>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Total Warehouses
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ object_list.count|default:"0" }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Active Warehouses
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    Loading...
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Total Locations
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    Loading...
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Stock Value
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    Loading...
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Warehouses Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list"></i> Warehouse List
                </h6>
            </div>
            <div class="card-body">
                {% if object_list %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Location</th>
                                    <th>Manager</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for warehouse in object_list %}
                                <tr>
                                    <td><strong>{{ warehouse.code|default:"N/A" }}</strong></td>
                                    <td>
                                        <strong>{{ warehouse.name|default:"Unnamed Warehouse" }}</strong>
                                        {% if warehouse.description %}
                                            <br><small class="text-muted">{{ warehouse.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ warehouse.warehouse_type|default:"Standard" }}</span>
                                    </td>
                                    <td>
                                        {{ warehouse.address|default:"No Address" }}<br>
                                        <small class="text-muted">{{ warehouse.city|default:"" }} {{ warehouse.state|default:"" }}</small>
                                    </td>
                                    <td>{{ warehouse.manager.get_full_name|default:"No Manager" }}</td>
                                    <td>
                                        {% if warehouse.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="/warehouses/{{ warehouse.pk }}/" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/warehouses/{{ warehouse.pk }}/edit/" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-warehouse fa-3x text-gray-300 mb-3"></i>
                        <h5>No Warehouses Found</h5>
                        <p class="text-muted">Start by adding your first warehouse to the system.</p>
                        <a href="/warehouses/create/" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add First Warehouse
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-bolt"></i> Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="/warehouses/create/" class="btn btn-success btn-block">
                                    <i class="fas fa-plus-circle"></i> Add New Warehouse
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/warehouses/" class="btn btn-info btn-block">
                                    <i class="fas fa-tachometer-alt"></i> Warehouse Dashboard
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/products/" class="btn btn-secondary btn-block">
                                    <i class="fas fa-box"></i> Manage Products
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="/dashboard/" class="btn btn-primary btn-block">
                                    <i class="fas fa-tachometer-alt"></i> Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <style>
        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }
        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }
        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }
        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }
        .text-gray-800 {
            color: #5a5c69 !important;
        }
        .text-gray-300 {
            color: #dddfeb !important;
        }
    </style>
</body>
</html>
