#!/usr/bin/env python
"""
Test script to verify warehouse creation and auto-code generation
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from warehouses.models import Warehouse
from users.models import User

def test_warehouse_creation():
    """Test warehouse creation with auto-generated codes"""
    print("🧪 Testing Warehouse Creation with Auto-Generated Codes")
    print("=" * 60)
    
    # Get a user for the manager field
    try:
        manager = User.objects.first()
        if not manager:
            print("❌ No users found. Please create a user first.")
            return
    except Exception as e:
        print(f"❌ Error getting user: {e}")
        return
    
    # Test data
    test_warehouses = [
        {
            'name': 'Test Warehouse 1',
            'warehouse_type': 'main',
            'address_line1': '123 Test Street',
            'city': 'Test City',
            'state': 'Test State',
            'postal_code': '12345',
            'country': 'US',
            'phone': '******-0123',
            'email': '<EMAIL>',
            'manager': manager,
            'capacity': 1000,
        },
        {
            'name': 'Test Warehouse 2',
            'warehouse_type': 'distribution',
            'address_line1': '456 Demo Avenue',
            'city': 'Demo City',
            'state': 'Demo State',
            'postal_code': '67890',
            'country': 'CA',
            'phone': '******-0456',
            'email': '<EMAIL>',
            'manager': manager,
            'capacity': 2000,
        }
    ]
    
    created_warehouses = []
    
    for i, warehouse_data in enumerate(test_warehouses, 1):
        try:
            print(f"\n📦 Creating Test Warehouse {i}...")
            print(f"   Name: {warehouse_data['name']}")
            print(f"   Type: {warehouse_data['warehouse_type']}")
            print(f"   Location: {warehouse_data['city']}, {warehouse_data['state']}")
            
            # Create warehouse (code should be auto-generated)
            warehouse = Warehouse.objects.create(**warehouse_data)
            created_warehouses.append(warehouse)
            
            print(f"   ✅ Created successfully!")
            print(f"   🏷️  Auto-generated code: {warehouse.code}")
            print(f"   📍 Full address: {warehouse.full_address}")
            
        except Exception as e:
            print(f"   ❌ Error creating warehouse: {e}")
    
    # Summary
    print(f"\n📊 Summary:")
    print(f"   Total warehouses created: {len(created_warehouses)}")
    
    if created_warehouses:
        print(f"\n🏷️  Generated Codes:")
        for warehouse in created_warehouses:
            print(f"   • {warehouse.name}: {warehouse.code}")
        
        # Test uniqueness
        codes = [w.code for w in created_warehouses]
        if len(codes) == len(set(codes)):
            print(f"   ✅ All codes are unique!")
        else:
            print(f"   ❌ Duplicate codes found!")
        
        # Test format
        print(f"\n🔍 Code Format Validation:")
        for warehouse in created_warehouses:
            code = warehouse.code
            if code.startswith('WH') and len(code) == 10:
                print(f"   ✅ {warehouse.name}: {code} (Valid format)")
            else:
                print(f"   ❌ {warehouse.name}: {code} (Invalid format)")
    
    # Cleanup (optional - comment out if you want to keep test data)
    print(f"\n🧹 Cleaning up test data...")
    for warehouse in created_warehouses:
        warehouse.delete()
        print(f"   🗑️  Deleted: {warehouse.name}")
    
    print(f"\n🎉 Test completed successfully!")

if __name__ == '__main__':
    test_warehouse_creation()
