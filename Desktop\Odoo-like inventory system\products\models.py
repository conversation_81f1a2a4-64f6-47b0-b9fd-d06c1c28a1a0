from django.db import models
from django.core.validators import MinValueValidator
from django.urls import reverse
import uuid
import os


def product_image_path(instance, filename):
    """Generate file path for product images"""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4()}.{ext}"
    return os.path.join('products', 'images', filename)


def barcode_image_path(instance, filename):
    """Generate file path for barcode images"""
    ext = filename.split('.')[-1]
    filename = f"barcode_{instance.sku}.{ext}"
    return os.path.join('products', 'barcodes', filename)


class Category(models.Model):
    """
    Product categories for organization
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='children')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def get_full_path(self):
        """Get the full category path (e.g., 'Electronics > Computers > Laptops')"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name


class Brand(models.Model):
    """
    Product brands/manufacturers
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    logo = models.ImageField(upload_to='brands/', blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Product(models.Model):
    """
    Main Product model
    """
    PRODUCT_TYPES = [
        ('simple', 'Simple Product'),
        ('variant', 'Product with Variants'),
        ('service', 'Service'),
        ('digital', 'Digital Product'),
    ]
    
    UNITS = [
        ('pcs', 'Pieces'),
        ('kg', 'Kilograms'),
        ('g', 'Grams'),
        ('l', 'Liters'),
        ('ml', 'Milliliters'),
        ('m', 'Meters'),
        ('cm', 'Centimeters'),
        ('box', 'Box'),
        ('pack', 'Pack'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=200)
    sku = models.CharField(max_length=50, unique=True, help_text="Stock Keeping Unit")
    barcode = models.CharField(max_length=50, blank=True, null=True, unique=True)
    description = models.TextField(blank=True, null=True)
    short_description = models.CharField(max_length=500, blank=True, null=True)
    
    # Classification
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)
    brand = models.ForeignKey(Brand, on_delete=models.SET_NULL, null=True, blank=True)
    product_type = models.CharField(max_length=20, choices=PRODUCT_TYPES, default='simple')
    
    # Pricing
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    selling_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    
    # Physical Properties
    weight = models.DecimalField(max_digits=8, decimal_places=3, blank=True, null=True, help_text="Weight in kg")
    dimensions_length = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, help_text="Length in cm")
    dimensions_width = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, help_text="Width in cm")
    dimensions_height = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True, help_text="Height in cm")
    unit = models.CharField(max_length=10, choices=UNITS, default='pcs')
    
    # Inventory Settings
    track_inventory = models.BooleanField(default=True)
    min_stock_level = models.IntegerField(default=0, help_text="Minimum stock level for alerts")
    max_stock_level = models.IntegerField(blank=True, null=True, help_text="Maximum stock level")
    
    # Images and Media
    image = models.ImageField(upload_to=product_image_path, blank=True, null=True)
    barcode_image = models.ImageField(upload_to=barcode_image_path, blank=True, null=True)
    
    # Status and Metadata
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, related_name='created_products')
    
    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['sku']),
            models.Index(fields=['barcode']),
            models.Index(fields=['category']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.sku})"
    
    def get_absolute_url(self):
        return reverse('products:detail', kwargs={'pk': self.pk})
    
    @property
    def profit_margin(self):
        """Calculate profit margin percentage"""
        if self.cost_price and self.selling_price:
            return ((self.selling_price - self.cost_price) / self.selling_price) * 100
        return 0
    
    @property
    def total_stock(self):
        """Get total stock across all warehouses"""
        return sum(stock.quantity for stock in self.stock_set.all())
    
    @property
    def is_low_stock(self):
        """Check if product is below minimum stock level"""
        return self.total_stock <= self.min_stock_level


class ProductVariant(models.Model):
    """
    Product variants (e.g., different sizes, colors)
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='variants')
    name = models.CharField(max_length=100)
    sku = models.CharField(max_length=50, unique=True)
    barcode = models.CharField(max_length=50, blank=True, null=True, unique=True)
    
    # Variant-specific pricing (optional override)
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    selling_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    
    # Variant-specific properties
    color = models.CharField(max_length=50, blank=True, null=True)
    size = models.CharField(max_length=50, blank=True, null=True)
    material = models.CharField(max_length=100, blank=True, null=True)
    
    # Images
    image = models.ImageField(upload_to=product_image_path, blank=True, null=True)
    barcode_image = models.ImageField(upload_to=barcode_image_path, blank=True, null=True)
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['name']
        unique_together = ['product', 'name']
    
    def __str__(self):
        return f"{self.product.name} - {self.name}"
    
    @property
    def effective_cost_price(self):
        """Get cost price (variant-specific or parent product)"""
        return self.cost_price or self.product.cost_price
    
    @property
    def effective_selling_price(self):
        """Get selling price (variant-specific or parent product)"""
        return self.selling_price or self.product.selling_price


class ProductImage(models.Model):
    """
    Additional product images
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to=product_image_path)
    alt_text = models.CharField(max_length=200, blank=True, null=True)
    is_primary = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['order', 'created_at']
    
    def __str__(self):
        return f"Image for {self.product.name}"
