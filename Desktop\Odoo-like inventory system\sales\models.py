from django.db import models
from django.core.validators import MinValueValidator, EmailValidator
from django.utils import timezone
import uuid


class Customer(models.Model):
    """
    Customer information for sales
    """
    CUSTOMER_TYPES = [
        ('individual', 'Individual'),
        ('business', 'Business'),
        ('government', 'Government'),
        ('nonprofit', 'Non-Profit'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=200)
    customer_code = models.CharField(max_length=20, unique=True, editable=False)
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPES, default='individual')
    
    # Contact Information
    email = models.EmailField(validators=[EmailValidator()])
    phone = models.CharField(max_length=20, blank=True, null=True, help_text='Phone number with country code')
    mobile = models.CharField(max_length=20, blank=True, null=True, help_text='Mobile number with country code')
    website = models.URLField(blank=True, null=True)
    
    # Address Information
    billing_address_line1 = models.Char<PERSON>ield(max_length=200)
    billing_address_line2 = models.CharField(max_length=200, blank=True, null=True)
    billing_city = models.CharField(max_length=100)
    billing_state = models.CharField(max_length=100)
    billing_postal_code = models.CharField(max_length=20)
    billing_country = models.CharField(max_length=2, default='US', help_text='ISO 3166-1 alpha-2 country code')
    
    shipping_address_line1 = models.CharField(max_length=200, blank=True, null=True)
    shipping_address_line2 = models.CharField(max_length=200, blank=True, null=True)
    shipping_city = models.CharField(max_length=100, blank=True, null=True)
    shipping_state = models.CharField(max_length=100, blank=True, null=True)
    shipping_postal_code = models.CharField(max_length=20, blank=True, null=True)
    shipping_country = models.CharField(max_length=2, blank=True, null=True, help_text='ISO 3166-1 alpha-2 country code')
    
    # Business Information
    tax_id = models.CharField(max_length=50, blank=True, null=True)
    credit_limit = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    payment_terms = models.CharField(max_length=100, default='Net 30')
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['customer_code']),
            models.Index(fields=['email']),
            models.Index(fields=['is_active']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.customer_code:
            self.customer_code = self.generate_customer_code()
        super().save(*args, **kwargs)
    
    def generate_customer_code(self):
        """Generate unique customer code"""
        prefix = 'CUST'
        timestamp = timezone.now().strftime('%Y%m')
        unique_id = str(uuid.uuid4())[:6].upper()
        return f"{prefix}-{timestamp}-{unique_id}"
    
    def __str__(self):
        return f"{self.name} ({self.customer_code})"
    
    def get_shipping_address(self):
        """Get shipping address or fallback to billing address"""
        if self.shipping_address_line1:
            address_parts = [self.shipping_address_line1]
            if self.shipping_address_line2:
                address_parts.append(self.shipping_address_line2)
            address_parts.extend([
                self.shipping_city, 
                self.shipping_state, 
                self.shipping_postal_code, 
                self.shipping_country or 'USA'
            ])
            return ', '.join(address_parts)
        else:
            return self.get_billing_address()
    
    def get_billing_address(self):
        """Get formatted billing address"""
        address_parts = [self.billing_address_line1]
        if self.billing_address_line2:
            address_parts.append(self.billing_address_line2)
        address_parts.extend([self.billing_city, self.billing_state, self.billing_postal_code, self.billing_country])
        return ', '.join(address_parts)


class Supplier(models.Model):
    """
    Supplier information for purchases
    """
    # Basic Information
    name = models.CharField(max_length=200)
    supplier_code = models.CharField(max_length=20, unique=True, editable=False)
    
    # Contact Information
    email = models.EmailField(validators=[EmailValidator()])
    phone = models.CharField(max_length=20, blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    contact_person = models.CharField(max_length=100, blank=True, null=True)
    
    # Address Information
    address_line1 = models.CharField(max_length=200)
    address_line2 = models.CharField(max_length=200, blank=True, null=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20)
    country = models.CharField(max_length=100, default='USA')
    
    # Business Information
    tax_id = models.CharField(max_length=50, blank=True, null=True)
    payment_terms = models.CharField(max_length=100, default='Net 30')
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
    
    def save(self, *args, **kwargs):
        if not self.supplier_code:
            self.supplier_code = self.generate_supplier_code()
        super().save(*args, **kwargs)
    
    def generate_supplier_code(self):
        """Generate unique supplier code"""
        prefix = 'SUPP'
        timestamp = timezone.now().strftime('%Y%m')
        unique_id = str(uuid.uuid4())[:6].upper()
        return f"{prefix}-{timestamp}-{unique_id}"
    
    def __str__(self):
        return f"{self.name} ({self.supplier_code})"


class SalesOrder(models.Model):
    """
    Sales orders from customers
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
        ('returned', 'Returned'),
    ]
    
    # Order Information
    order_number = models.CharField(max_length=50, unique=True, editable=False)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='sales_orders')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Dates
    order_date = models.DateTimeField(default=timezone.now)
    expected_delivery_date = models.DateField(blank=True, null=True)
    actual_delivery_date = models.DateField(blank=True, null=True)
    
    # Financial Information
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    shipping_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Shipping Information
    shipping_method = models.CharField(max_length=100, blank=True, null=True)
    tracking_number = models.CharField(max_length=100, blank=True, null=True)
    
    # Additional Information
    notes = models.TextField(blank=True, null=True)
    internal_notes = models.TextField(blank=True, null=True)
    
    # Metadata
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order_number']),
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['order_date']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)
    
    def generate_order_number(self):
        """Generate unique order number"""
        prefix = 'SO'
        timestamp = timezone.now().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4())[:6].upper()
        return f"{prefix}-{timestamp}-{unique_id}"
    
    def __str__(self):
        return f"{self.order_number} - {self.customer.name}"
    
    def calculate_totals(self):
        """Calculate order totals from line items"""
        self.subtotal = sum(line.line_total for line in self.lines.all())
        self.total_amount = self.subtotal + self.tax_amount + self.shipping_cost - self.discount_amount
        self.save()


class SalesOrderLine(models.Model):
    """
    Line items for sales orders
    """
    order = models.ForeignKey(SalesOrder, on_delete=models.CASCADE, related_name='lines')
    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    product_variant = models.ForeignKey('products.ProductVariant', on_delete=models.CASCADE, blank=True, null=True)
    
    quantity = models.IntegerField(validators=[MinValueValidator(1)])
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    line_total = models.DecimalField(max_digits=12, decimal_places=2, editable=False)
    
    # Fulfillment
    quantity_shipped = models.IntegerField(default=0)
    quantity_delivered = models.IntegerField(default=0)
    
    notes = models.TextField(blank=True, null=True)
    
    def save(self, *args, **kwargs):
        discount_amount = (self.unit_price * self.quantity) * (self.discount_percent / 100)
        self.line_total = (self.unit_price * self.quantity) - discount_amount
        super().save(*args, **kwargs)
    
    def __str__(self):
        variant_info = f" - {self.product_variant.name}" if self.product_variant else ""
        return f"{self.product.name}{variant_info} x {self.quantity}"
    
    @property
    def remaining_quantity(self):
        """Get quantity remaining to ship"""
        return self.quantity - self.quantity_shipped


class PurchaseOrder(models.Model):
    """
    Purchase orders to suppliers
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('confirmed', 'Confirmed'),
        ('received', 'Received'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Order Information
    order_number = models.CharField(max_length=50, unique=True, editable=False)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='purchase_orders')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Dates
    order_date = models.DateTimeField(default=timezone.now)
    expected_delivery_date = models.DateField(blank=True, null=True)
    actual_delivery_date = models.DateField(blank=True, null=True)
    
    # Financial Information
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    shipping_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Additional Information
    notes = models.TextField(blank=True, null=True)
    
    # Metadata
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)
    
    def generate_order_number(self):
        """Generate unique order number"""
        prefix = 'PO'
        timestamp = timezone.now().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4())[:6].upper()
        return f"{prefix}-{timestamp}-{unique_id}"
    
    def __str__(self):
        return f"{self.order_number} - {self.supplier.name}"


class PurchaseOrderLine(models.Model):
    """
    Line items for purchase orders
    """
    order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='lines')
    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    product_variant = models.ForeignKey('products.ProductVariant', on_delete=models.CASCADE, blank=True, null=True)
    
    quantity = models.IntegerField(validators=[MinValueValidator(1)])
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2)
    line_total = models.DecimalField(max_digits=12, decimal_places=2, editable=False)
    
    # Receiving
    quantity_received = models.IntegerField(default=0)
    
    notes = models.TextField(blank=True, null=True)
    
    def save(self, *args, **kwargs):
        self.line_total = self.unit_cost * self.quantity
        super().save(*args, **kwargs)
    
    def __str__(self):
        variant_info = f" - {self.product_variant.name}" if self.product_variant else ""
        return f"{self.product.name}{variant_info} x {self.quantity}"
    
    @property
    def remaining_quantity(self):
        """Get quantity remaining to receive"""
        return self.quantity - self.quantity_received
