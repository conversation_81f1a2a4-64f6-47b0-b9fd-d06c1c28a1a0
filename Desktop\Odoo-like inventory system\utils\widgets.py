from django import forms
from django.forms.widgets import MultiWidget, Select, TextInput
from .countries import get_phone_code_choices, get_country_choices


class PhoneNumberWidget(MultiWidget):
    """
    A widget that splits phone number input into country code and number
    """
    def __init__(self, attrs=None):
        widgets = [
            Select(choices=get_phone_code_choices(), attrs={'class': 'form-control', 'style': 'width: 120px;'}),
            TextInput(attrs={'class': 'form-control', 'placeholder': 'Phone number'})
        ]
        super().__init__(widgets, attrs)

    def decompress(self, value):
        if value:
            # Try to split the phone number
            if value.startswith('+'):
                # Find where the country code ends
                for i, char in enumerate(value[1:], 1):
                    if not char.isdigit():
                        country_code = value[:i]
                        number = value[i:].strip()
                        return [country_code, number]
                # If all digits after +, assume first 1-4 digits are country code
                if len(value) > 4:
                    country_code = value[:4] if value[1:4].isdigit() else value[:3] if value[1:3].isdigit() else value[:2]
                    number = value[len(country_code):].strip()
                    return [country_code, number]
            return ['', value]
        return ['', '']

    def value_from_datadict(self, data, files, name):
        country_code = data.get(f'{name}_0', '')
        number = data.get(f'{name}_1', '')
        if country_code and number:
            return f"{country_code} {number}".strip()
        return ''

    def format_output(self, rendered_widgets):
        return f'<div class="row"><div class="col-4">{rendered_widgets[0]}</div><div class="col-8">{rendered_widgets[1]}</div></div>'


class CountrySelectWidget(Select):
    """
    A select widget for countries with search functionality
    """
    def __init__(self, attrs=None):
        if attrs is None:
            attrs = {}
        attrs.update({
            'class': 'form-control country-select',
            'data-live-search': 'true',
            'data-size': '10'
        })
        super().__init__(attrs=attrs, choices=get_country_choices())

    class Media:
        css = {
            'all': ('https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css',)
        }
        js = ('https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js',)


class PhoneNumberField(forms.CharField):
    """
    A form field for phone numbers with country code selection
    """
    widget = PhoneNumberWidget

    def __init__(self, *args, **kwargs):
        kwargs.setdefault('max_length', 20)
        super().__init__(*args, **kwargs)

    def clean(self, value):
        value = super().clean(value)
        if value:
            # Basic validation - ensure it contains only digits, spaces, +, -, (, )
            import re
            if not re.match(r'^[\+\d\s\-\(\)]+$', value):
                raise forms.ValidationError('Please enter a valid phone number.')
        return value


class CountryField(forms.ChoiceField):
    """
    A form field for country selection
    """
    widget = CountrySelectWidget

    def __init__(self, *args, **kwargs):
        kwargs['choices'] = get_country_choices()
        super().__init__(*args, **kwargs)
