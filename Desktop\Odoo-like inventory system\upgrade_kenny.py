#!/usr/bin/env python
"""
Upgrade kenny user to admin role
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

try:
    from users.models import User
    
    # Find kenny user
    kenny = User.objects.get(username='kenny')
    print(f"📋 Current kenny status:")
    print(f"   Username: {kenny.username}")
    print(f"   Role: {kenny.get_role_display()}")
    print(f"   Can manage inventory: {kenny.can_manage_inventory}")
    print(f"   Can manage sales: {kenny.can_manage_sales}")
    print(f"   Is superuser: {kenny.is_superuser}")
    
    # Upgrade kenny to admin
    kenny.role = 'admin'
    kenny.save()
    
    print(f"\n🚀 UPGRADED kenny to admin!")
    print(f"   New role: {kenny.get_role_display()}")
    print(f"   Can manage inventory: {kenny.can_manage_inventory}")
    print(f"   Can manage sales: {kenny.can_manage_sales}")
    
    print(f"\n✅ <PERSON> can now access all system features!")
    print(f"\n🔑 LOGIN CREDENTIALS:")
    print(f"   Username: kenny")
    print(f"   Password: InventoryAdmin2025!")
    print(f"   URL: http://127.0.0.1:8000/login/")
    
except User.DoesNotExist:
    print("❌ Kenny user not found")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
