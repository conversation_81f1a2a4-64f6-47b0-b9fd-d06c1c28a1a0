from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import api_views

router = DefaultRouter()
router.register(r'products', api_views.ProductViewSet)
router.register(r'categories', api_views.CategoryViewSet)
router.register(r'brands', api_views.BrandViewSet)
router.register(r'variants', api_views.ProductVariantViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('search/', api_views.ProductSearchAPIView.as_view(), name='product_search'),
    path('barcode/<str:barcode>/', api_views.ProductByBarcodeAPIView.as_view(), name='product_by_barcode'),
    path('low-stock/', api_views.LowStockProductsAPIView.as_view(), name='low_stock_products'),
]
