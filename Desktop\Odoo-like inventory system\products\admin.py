from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import Category, Brand, Product, ProductVariant, ProductImage


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'parent', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at', 'parent')
    search_fields = ('name', 'description')
    ordering = ('name',)
    list_editable = ('is_active',)
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'parent', 'is_active')
        }),
    )


@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ('name', 'website', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('name',)
    list_editable = ('is_active',)
    
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'logo', 'website', 'is_active')
        }),
    )


class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1
    fields = ('image', 'alt_text', 'is_primary', 'order')


class ProductVariantInline(admin.TabularInline):
    model = ProductVariant
    extra = 0
    fields = ('name', 'sku', 'color', 'size', 'cost_price', 'selling_price', 'is_active')
    readonly_fields = ('sku',)


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'sku', 'category', 'brand', 'cost_price', 'selling_price', 
        'stock_status', 'is_active', 'created_at'
    )
    list_filter = (
        'is_active', 'is_featured', 'category', 'brand', 'product_type', 
        'track_inventory', 'created_at'
    )
    search_fields = ('name', 'sku', 'barcode', 'description')
    ordering = ('-created_at',)
    list_editable = ('is_active',)
    readonly_fields = ('created_at', 'updated_at', 'profit_margin_display', 'total_stock_display')
    
    inlines = [ProductVariantInline, ProductImageInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'sku', 'barcode', 'description', 'short_description')
        }),
        ('Classification', {
            'fields': ('category', 'brand', 'product_type')
        }),
        ('Pricing', {
            'fields': ('cost_price', 'selling_price', 'profit_margin_display')
        }),
        ('Physical Properties', {
            'fields': ('weight', 'dimensions_length', 'dimensions_width', 'dimensions_height', 'unit'),
            'classes': ('collapse',)
        }),
        ('Inventory Settings', {
            'fields': ('track_inventory', 'min_stock_level', 'max_stock_level', 'total_stock_display')
        }),
        ('Media', {
            'fields': ('image', 'barcode_image')
        }),
        ('Status', {
            'fields': ('is_active', 'is_featured', 'created_by')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def profit_margin_display(self, obj):
        """Display profit margin as percentage"""
        return f"{obj.profit_margin:.2f}%"
    profit_margin_display.short_description = 'Profit Margin'
    
    def total_stock_display(self, obj):
        """Display total stock across all warehouses"""
        total = obj.total_stock
        if total <= obj.min_stock_level:
            return format_html('<span style="color: red;">{}</span>', total)
        return total
    total_stock_display.short_description = 'Total Stock'
    
    def stock_status(self, obj):
        """Display stock status with color coding"""
        total = obj.total_stock
        if total <= 0:
            return format_html('<span style="color: red;">Out of Stock</span>')
        elif total <= obj.min_stock_level:
            return format_html('<span style="color: orange;">Low Stock ({})</span>', total)
        else:
            return format_html('<span style="color: green;">In Stock ({})</span>', total)
    stock_status.short_description = 'Stock Status'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('category', 'brand', 'created_by')
    
    actions = ['activate_products', 'deactivate_products', 'mark_as_featured', 'unmark_as_featured']
    
    def activate_products(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} products were successfully activated.')
    activate_products.short_description = "Activate selected products"
    
    def deactivate_products(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} products were successfully deactivated.')
    deactivate_products.short_description = "Deactivate selected products"
    
    def mark_as_featured(self, request, queryset):
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} products were marked as featured.')
    mark_as_featured.short_description = "Mark as featured"
    
    def unmark_as_featured(self, request, queryset):
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} products were unmarked as featured.')
    unmark_as_featured.short_description = "Unmark as featured"


@admin.register(ProductVariant)
class ProductVariantAdmin(admin.ModelAdmin):
    list_display = ('name', 'product', 'sku', 'color', 'size', 'effective_cost_price', 'effective_selling_price', 'is_active')
    list_filter = ('is_active', 'color', 'size', 'product__category', 'created_at')
    search_fields = ('name', 'sku', 'barcode', 'product__name')
    ordering = ('product__name', 'name')
    list_editable = ('is_active',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('product', 'name', 'sku', 'barcode')
        }),
        ('Pricing', {
            'fields': ('cost_price', 'selling_price')
        }),
        ('Variant Properties', {
            'fields': ('color', 'size', 'material')
        }),
        ('Media', {
            'fields': ('image', 'barcode_image')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product')


@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    list_display = ('product', 'alt_text', 'is_primary', 'order', 'created_at')
    list_filter = ('is_primary', 'created_at')
    search_fields = ('product__name', 'alt_text')
    ordering = ('product__name', 'order')
    list_editable = ('is_primary', 'order')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product')
