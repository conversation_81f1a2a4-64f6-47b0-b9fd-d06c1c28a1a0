#!/usr/bin/env python
"""
Check user consistency across different views and sessions
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.test import Client
from users.models import User
from django.contrib.sessions.models import Session
from django.contrib.auth import get_user_model

def check_user_consistency():
    """Check user consistency across the system"""
    print("👤 Checking User Consistency Across System...\n")
    
    try:
        # Check all users in the system
        print("📊 Current Users in Database:")
        users = User.objects.all()
        for user in users:
            print(f"   - Username: {user.username}")
            print(f"     Email: {user.email}")
            print(f"     Role: {user.get_role_display()}")
            print(f"     Active: {user.is_active}")
            print(f"     Staff: {user.is_staff}")
            print(f"     Superuser: {user.is_superuser}")
            print(f"     Last Login: {user.last_login}")
            print()
        
        # Check active sessions
        print("🔐 Active Sessions:")
        sessions = Session.objects.all()
        print(f"   Total active sessions: {sessions.count()}")
        
        for session in sessions:
            session_data = session.get_decoded()
            user_id = session_data.get('_auth_user_id')
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                    print(f"   - Session for: {user.username} (ID: {user_id})")
                    print(f"     Expires: {session.expire_date}")
                except User.DoesNotExist:
                    print(f"   - Session for unknown user ID: {user_id}")
        
        # Test login with kenny specifically
        print("\n🧪 Testing Kenny Login:")
        
        try:
            kenny = User.objects.get(username='kenny')
            print(f"✅ Kenny found in database:")
            print(f"   Username: {kenny.username}")
            print(f"   Email: {kenny.email}")
            print(f"   Role: {kenny.get_role_display()}")
            print(f"   Can manage inventory: {kenny.can_manage_inventory}")
            print(f"   Can manage sales: {kenny.can_manage_sales}")
            print(f"   Active: {kenny.is_active}")
            print(f"   Staff: {kenny.is_staff}")
            print(f"   Superuser: {kenny.is_superuser}")
            
            # Test client login
            client = Client()
            client.force_login(kenny)
            
            # Test different pages to see user context
            test_pages = [
                ('/dashboard/', 'Dashboard'),
                ('/products/', 'Products'),
                ('/sales/orders/', 'Sales Orders'),
                ('/admin/', 'Admin Panel'),
            ]
            
            print(f"\n🔍 Testing User Context on Different Pages:")
            
            for url, page_name in test_pages:
                try:
                    response = client.get(url)
                    if response.status_code == 200:
                        content = response.content.decode('utf-8')
                        
                        # Look for user references in the content
                        user_references = []
                        if 'kenny' in content.lower():
                            user_references.append('kenny')
                        if 'admin' in content.lower() and 'admin panel' not in content.lower():
                            user_references.append('admin (not panel)')
                        
                        print(f"   {page_name}: Status {response.status_code}")
                        if user_references:
                            print(f"      User references found: {user_references}")
                        else:
                            print(f"      No specific user references found")
                            
                    elif response.status_code == 302:
                        print(f"   {page_name}: Redirected (Status {response.status_code})")
                    else:
                        print(f"   {page_name}: Error (Status {response.status_code})")
                        
                except Exception as e:
                    print(f"   {page_name}: Exception - {str(e)}")
            
        except User.DoesNotExist:
            print("❌ Kenny user not found in database!")
            
        # Check if there are multiple users with similar names
        print(f"\n🔍 Checking for Similar Usernames:")
        similar_users = User.objects.filter(username__icontains='ken')
        for user in similar_users:
            print(f"   - {user.username} (ID: {user.id}, Active: {user.is_active})")
            
        admin_users = User.objects.filter(username__icontains='admin')
        for user in admin_users:
            print(f"   - {user.username} (ID: {user.id}, Active: {user.is_active})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during user consistency check: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_user_consistency():
    """Fix any user consistency issues"""
    print(f"\n🔧 Checking for User Consistency Issues to Fix:")
    
    try:
        # Check if kenny exists and has correct settings
        kenny = User.objects.get(username='kenny')
        
        issues_fixed = []
        
        # Ensure kenny is active
        if not kenny.is_active:
            kenny.is_active = True
            issues_fixed.append("Activated kenny account")
        
        # Ensure kenny has staff status for admin access
        if not kenny.is_staff:
            kenny.is_staff = True
            issues_fixed.append("Granted staff status to kenny")
        
        # Ensure kenny has admin role
        if kenny.role != 'admin':
            kenny.role = 'admin'
            issues_fixed.append("Set kenny role to admin")
        
        if issues_fixed:
            kenny.save()
            print(f"✅ Fixed issues for kenny:")
            for issue in issues_fixed:
                print(f"   - {issue}")
        else:
            print(f"✅ Kenny account is properly configured")
            
        return True
        
    except User.DoesNotExist:
        print(f"❌ Kenny user not found - cannot fix")
        return False
    except Exception as e:
        print(f"❌ Error fixing user consistency: {e}")
        return False

if __name__ == "__main__":
    print("🚀 User Consistency Check\n")
    
    check_success = check_user_consistency()
    fix_success = fix_user_consistency()
    
    print(f"\n📋 Final Results:")
    print(f"   User Check: {'✅ COMPLETED' if check_success else '❌ FAILED'}")
    print(f"   User Fix: {'✅ COMPLETED' if fix_success else '❌ FAILED'}")
    
    if check_success and fix_success:
        print(f"\n🎉 User consistency verified and fixed!")
        print(f"✅ Kenny should now appear consistently across all pages")
        print(f"🔑 Login credentials: kenny / InventoryAdmin2025!")
    else:
        print(f"\n⚠️  User consistency issues detected")
