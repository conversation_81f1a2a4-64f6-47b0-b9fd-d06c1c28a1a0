from django.urls import path
from . import views

app_name = 'products'

urlpatterns = [
    # Product URLs
    path('', views.ProductListView.as_view(), name='list'),
    path('create/', views.ProductCreateView.as_view(), name='create'),
    path('<int:pk>/', views.ProductDetailView.as_view(), name='detail'),
    path('<int:pk>/edit/', views.ProductUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.product_delete, name='delete'),
    
    # Product Variants
    path('<int:product_pk>/variants/', views.product_variants, name='variants'),
    
    # Barcode and QR Code
    path('<int:pk>/generate-barcode/', views.generate_product_barcode, name='generate_barcode'),
    path('<int:pk>/generate-qr/', views.generate_product_qr, name='generate_qr'),
    
    # Category URLs
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('categories/create/', views.CategoryCreateView.as_view(), name='category_create'),
    
    # Brand URLs
    path('brands/', views.BrandListView.as_view(), name='brand_list'),
    path('brands/create/', views.BrandCreateView.as_view(), name='brand_create'),
    
    # AJAX URLs
    path('ajax/search/', views.product_search_ajax, name='search_ajax'),
    path('ajax/check-sku/', views.check_sku_availability, name='check_sku'),
    
    # Bulk Operations
    path('bulk-update/', views.bulk_update_products, name='bulk_update'),
    
    # Export
    path('export/csv/', views.export_products_csv, name='export_csv'),
]
