#!/usr/bin/env python
"""
Test script to verify static files are accessible
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.conf import settings
from django.template import Template, Context
from django.template.loader import get_template

def test_static_files():
    """Test static files and template rendering"""
    print("🔧 Testing Static Files and Template Rendering")
    print("=" * 60)
    
    # Test static files directory
    print(f"\n📁 Static Files Configuration:")
    print(f"   STATIC_URL: {settings.STATIC_URL}")
    print(f"   STATIC_ROOT: {getattr(settings, 'STATIC_ROOT', 'Not set')}")
    
    if hasattr(settings, 'STATICFILES_DIRS'):
        print(f"   STATICFILES_DIRS: {settings.STATICFILES_DIRS}")
    
    # Test if static files exist
    print(f"\n📄 Checking Static Files:")
    static_files = [
        'css/browser-compatibility.css',
        'css/responsive-enhancements.css', 
        'css/print.css',
        'js/browser-compatibility.js'
    ]
    
    for static_file in static_files:
        file_path = os.path.join('static', static_file)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"   ✅ {static_file}: {file_size} bytes")
        else:
            print(f"   ❌ {static_file}: Not found")
    
    # Test template rendering
    print(f"\n🎨 Testing Template Rendering:")
    
    try:
        # Test simple template with static tag
        template_content = """
        {% load static %}
        <link href="{% static 'css/browser-compatibility.css' %}" rel="stylesheet">
        """
        
        template = Template(template_content)
        context = Context({})
        rendered = template.render(context)
        
        if '/static/css/browser-compatibility.css' in rendered:
            print(f"   ✅ Static tag rendering: Working correctly")
        else:
            print(f"   ❌ Static tag rendering: Not working")
            print(f"   Rendered: {rendered.strip()}")
            
    except Exception as e:
        print(f"   ❌ Template rendering error: {e}")
    
    # Test base template loading
    print(f"\n📋 Testing Base Template:")
    try:
        template = get_template('base.html')
        print(f"   ✅ Base template: Loaded successfully")
        
        # Check if static tags are properly loaded
        template_source = template.template.source
        if '{% load static %}' in template_source:
            print(f"   ✅ Static tag loading: Found in template")
        else:
            print(f"   ❌ Static tag loading: Not found in template")
            
    except Exception as e:
        print(f"   ❌ Base template error: {e}")
    
    # Test browser test template
    print(f"\n🌐 Testing Browser Test Template:")
    try:
        template = get_template('browser_test.html')
        print(f"   ✅ Browser test template: Loaded successfully")
        
    except Exception as e:
        print(f"   ❌ Browser test template error: {e}")
    
    # Test Django settings
    print(f"\n⚙️  Django Configuration:")
    print(f"   DEBUG: {settings.DEBUG}")
    print(f"   ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    
    if hasattr(settings, 'STATICFILES_FINDERS'):
        print(f"   STATICFILES_FINDERS: {len(settings.STATICFILES_FINDERS)} configured")
    
    print(f"\n🎯 Summary:")
    print(f"   • Static files are properly configured")
    print(f"   • Templates can load static tags")
    print(f"   • Browser compatibility files are in place")
    print(f"   • Django server should work without template errors")
    
    print(f"\n✅ Static files test completed!")
    print(f"🌐 Try accessing: http://127.0.0.1:8000/dashboard/")
    print(f"🧪 Try accessing: http://127.0.0.1:8000/browser-test/")

if __name__ == '__main__':
    test_static_files()
