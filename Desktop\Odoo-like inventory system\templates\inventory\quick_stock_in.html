{% extends 'base.html' %}

{% block title %}Quick Stock In - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Quick Stock In</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'inventory:movement_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Movements
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Add Stock to Inventory</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.product.id_for_label }}" class="form-label">Product *</label>
                                {{ form.product }}
                                {% if form.product.errors %}
                                    <div class="text-danger small">{{ form.product.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.quantity.id_for_label }}" class="form-label">Quantity *</label>
                                {{ form.quantity }}
                                {% if form.quantity.errors %}
                                    <div class="text-danger small">{{ form.quantity.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.warehouse.id_for_label }}" class="form-label">Warehouse *</label>
                                {{ form.warehouse }}
                                {% if form.warehouse.errors %}
                                    <div class="text-danger small">{{ form.warehouse.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.location.id_for_label }}" class="form-label">Location</label>
                                {{ form.location }}
                                {% if form.location.errors %}
                                    <div class="text-danger small">{{ form.location.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.reference.id_for_label }}" class="form-label">Reference</label>
                        {{ form.reference }}
                        {% if form.reference.errors %}
                            <div class="text-danger small">{{ form.reference.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">Purchase order, receipt number, etc.</small>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'inventory:movement_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus-circle"></i> Add Stock
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'inventory:quick_stock_out' %}" class="btn btn-outline-danger">
                        <i class="fas fa-minus-circle"></i> Quick Stock Out
                    </a>
                    <a href="{% url 'inventory:movement_list' %}" class="btn btn-outline-info">
                        <i class="fas fa-list"></i> View All Movements
                    </a>
                    <a href="{% url 'products:create' %}" class="btn btn-outline-primary">
                        <i class="fas fa-plus"></i> Add New Product
                    </a>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>Stock In Guidelines:</h6>
                    <ul class="mb-0">
                        <li>Select the correct product and warehouse</li>
                        <li>Enter the exact quantity received</li>
                        <li>Include reference numbers for tracking</li>
                        <li>Add notes for additional context</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
