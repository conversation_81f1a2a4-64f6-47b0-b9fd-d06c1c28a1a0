{% extends 'base.html' %}

{% block title %}{% if object %}Edit Brand{% else %}Add Brand{% endif %} - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{% if object %}Edit Brand{% else %}Add Brand{% endif %}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'products:brand_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Brands
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Brand Information</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Brand Name *</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger small">{{ form.name.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small">{{ form.description.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.website.id_for_label }}" class="form-label">Website</label>
                        {{ form.website }}
                        {% if form.website.errors %}
                            <div class="text-danger small">{{ form.website.errors.0 }}</div>
                        {% endif %}
                        <small class="text-muted">Include http:// or https://</small>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.logo.id_for_label }}" class="form-label">Brand Logo</label>
                        {{ form.logo }}
                        {% if form.logo.errors %}
                            <div class="text-danger small">{{ form.logo.errors.0 }}</div>
                        {% endif %}
                        {% if object and object.logo %}
                            <div class="mt-2">
                                <img src="{{ object.logo.url }}" alt="Current logo" class="img-thumbnail" style="max-width: 100px;">
                                <small class="text-muted d-block">Current logo</small>
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3 form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            Active
                        </label>
                        {% if form.is_active.errors %}
                            <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'products:brand_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {% if object %}Update Brand{% else %}Create Brand{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>Brand Guidelines:</h6>
                    <ul class="mb-0">
                        <li>Use official brand names</li>
                        <li>Upload high-quality logos</li>
                        <li>Include official website links</li>
                        <li>Keep brand information up to date</li>
                    </ul>
                </div>
            </div>
        </div>

        {% if object %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Brand Statistics</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td>Products:</td>
                        <td>{{ object.products.count }}</td>
                    </tr>
                    <tr>
                        <td>Created:</td>
                        <td>{{ object.created_at|date:"M d, Y" }}</td>
                    </tr>
                    <tr>
                        <td>Status:</td>
                        <td>
                            {% if object.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}
