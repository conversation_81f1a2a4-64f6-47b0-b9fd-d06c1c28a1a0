from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
import uuid


class StockMovement(models.Model):
    """
    Track all stock movements (in, out, transfers, adjustments)
    """
    MOVEMENT_TYPES = [
        ('in', 'Stock In'),
        ('out', 'Stock Out'),
        ('transfer', 'Transfer'),
        ('adjustment', 'Adjustment'),
        ('return', 'Return'),
        ('damaged', 'Damaged'),
        ('expired', 'Expired'),
    ]
    
    MOVEMENT_REASONS = [
        ('purchase', 'Purchase'),
        ('sale', 'Sale'),
        ('transfer', 'Warehouse Transfer'),
        ('adjustment', 'Stock Adjustment'),
        ('return_customer', 'Customer Return'),
        ('return_supplier', 'Return to Supplier'),
        ('damaged', 'Damaged Goods'),
        ('expired', 'Expired Goods'),
        ('theft', 'Theft/Loss'),
        ('production', 'Production'),
        ('sample', 'Sample'),
        ('promotion', 'Promotional'),
    ]
    
    # Reference
    reference = models.CharField(max_length=50, unique=True, editable=False)
    
    # Product Information
    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    product_variant = models.ForeignKey('products.ProductVariant', on_delete=models.CASCADE, blank=True, null=True)
    
    # Movement Details
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES)
    reason = models.CharField(max_length=20, choices=MOVEMENT_REASONS)
    quantity = models.IntegerField(validators=[MinValueValidator(1)])
    
    # Location Information
    from_warehouse = models.ForeignKey('warehouses.Warehouse', on_delete=models.CASCADE, related_name='outgoing_movements', blank=True, null=True)
    from_location = models.ForeignKey('warehouses.Location', on_delete=models.CASCADE, related_name='outgoing_movements', blank=True, null=True)
    to_warehouse = models.ForeignKey('warehouses.Warehouse', on_delete=models.CASCADE, related_name='incoming_movements', blank=True, null=True)
    to_location = models.ForeignKey('warehouses.Location', on_delete=models.CASCADE, related_name='incoming_movements', blank=True, null=True)
    
    # Financial Information
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    total_cost = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    
    # Related Documents
    purchase_order = models.ForeignKey('sales.PurchaseOrder', on_delete=models.SET_NULL, blank=True, null=True)
    sales_order = models.ForeignKey('sales.SalesOrder', on_delete=models.SET_NULL, blank=True, null=True)
    
    # Status and Metadata
    is_processed = models.BooleanField(default=False)
    processed_at = models.DateTimeField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['product', 'created_at']),
            models.Index(fields=['movement_type']),
            models.Index(fields=['is_processed']),
            models.Index(fields=['reference']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.reference:
            self.reference = self.generate_reference()
        if not self.total_cost and self.unit_cost:
            self.total_cost = self.unit_cost * self.quantity
        super().save(*args, **kwargs)
    
    def generate_reference(self):
        """Generate unique reference number"""
        prefix = {
            'in': 'IN',
            'out': 'OUT',
            'transfer': 'TRF',
            'adjustment': 'ADJ',
            'return': 'RET',
            'damaged': 'DMG',
            'expired': 'EXP',
        }.get(self.movement_type, 'MOV')
        
        timestamp = timezone.now().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4())[:8].upper()
        return f"{prefix}-{timestamp}-{unique_id}"
    
    def __str__(self):
        return f"{self.reference} - {self.get_movement_type_display()}"
    
    def process_movement(self):
        """Process the stock movement and update stock levels"""
        if self.is_processed:
            return False
        
        # Update stock levels based on movement type
        if self.movement_type == 'in':
            self._process_stock_in()
        elif self.movement_type == 'out':
            self._process_stock_out()
        elif self.movement_type == 'transfer':
            self._process_transfer()
        elif self.movement_type == 'adjustment':
            self._process_adjustment()
        
        self.is_processed = True
        self.processed_at = timezone.now()
        self.save()
        return True
    
    def _process_stock_in(self):
        """Process stock in movement"""
        from warehouses.models import Stock
        
        stock, created = Stock.objects.get_or_create(
            product=self.product,
            product_variant=self.product_variant,
            warehouse=self.to_warehouse,
            location=self.to_location,
            defaults={'quantity': 0}
        )
        stock.quantity += self.quantity
        stock.save()
    
    def _process_stock_out(self):
        """Process stock out movement"""
        from warehouses.models import Stock
        
        try:
            stock = Stock.objects.get(
                product=self.product,
                product_variant=self.product_variant,
                warehouse=self.from_warehouse,
                location=self.from_location
            )
            if stock.quantity >= self.quantity:
                stock.quantity -= self.quantity
                stock.save()
            else:
                raise ValueError(f"Insufficient stock. Available: {stock.quantity}, Required: {self.quantity}")
        except Stock.DoesNotExist:
            raise ValueError("Stock record not found")
    
    def _process_transfer(self):
        """Process transfer movement"""
        self._process_stock_out()
        self._process_stock_in()
    
    def _process_adjustment(self):
        """Process stock adjustment"""
        from warehouses.models import Stock
        
        stock, created = Stock.objects.get_or_create(
            product=self.product,
            product_variant=self.product_variant,
            warehouse=self.to_warehouse or self.from_warehouse,
            location=self.to_location or self.from_location,
            defaults={'quantity': 0}
        )
        
        # For adjustments, quantity can be positive (increase) or negative (decrease)
        if self.movement_type == 'adjustment':
            stock.quantity = self.quantity  # Set absolute quantity
        else:
            stock.quantity += self.quantity  # Relative adjustment
        
        stock.quantity = max(0, stock.quantity)  # Ensure non-negative
        stock.save()


class StockAdjustment(models.Model):
    """
    Stock adjustment records for inventory corrections
    """
    ADJUSTMENT_TYPES = [
        ('count', 'Physical Count'),
        ('damage', 'Damage'),
        ('theft', 'Theft/Loss'),
        ('expired', 'Expired'),
        ('found', 'Found Stock'),
        ('correction', 'Data Correction'),
    ]
    
    reference = models.CharField(max_length=50, unique=True, editable=False)
    adjustment_type = models.CharField(max_length=20, choices=ADJUSTMENT_TYPES)
    warehouse = models.ForeignKey('warehouses.Warehouse', on_delete=models.CASCADE)
    
    reason = models.TextField()
    notes = models.TextField(blank=True, null=True)
    
    # Status
    is_approved = models.BooleanField(default=False)
    approved_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_adjustments')
    approved_at = models.DateTimeField(blank=True, null=True)
    
    created_by = models.ForeignKey('users.User', on_delete=models.SET_NULL, null=True, related_name='created_adjustments')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        if not self.reference:
            self.reference = self.generate_reference()
        super().save(*args, **kwargs)
    
    def generate_reference(self):
        """Generate unique reference number"""
        timestamp = timezone.now().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4())[:8].upper()
        return f"ADJ-{timestamp}-{unique_id}"
    
    def __str__(self):
        return f"{self.reference} - {self.get_adjustment_type_display()}"


class StockAdjustmentLine(models.Model):
    """
    Individual line items for stock adjustments
    """
    adjustment = models.ForeignKey(StockAdjustment, on_delete=models.CASCADE, related_name='lines')
    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    product_variant = models.ForeignKey('products.ProductVariant', on_delete=models.CASCADE, blank=True, null=True)
    location = models.ForeignKey('warehouses.Location', on_delete=models.CASCADE, blank=True, null=True)
    
    expected_quantity = models.IntegerField(default=0)
    actual_quantity = models.IntegerField()
    difference = models.IntegerField(editable=False)
    
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    total_cost_impact = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    
    notes = models.TextField(blank=True, null=True)
    
    def save(self, *args, **kwargs):
        self.difference = self.actual_quantity - self.expected_quantity
        if self.unit_cost:
            self.total_cost_impact = self.difference * self.unit_cost
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.product.name} - Diff: {self.difference}"
