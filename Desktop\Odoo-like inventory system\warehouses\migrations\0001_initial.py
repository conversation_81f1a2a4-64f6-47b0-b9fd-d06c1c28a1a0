# Generated by Django 5.2.4 on 2025-07-17 08:45

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20)),
                ('location_type', models.CharField(choices=[('zone', 'Zone'), ('aisle', 'Aisle'), ('rack', 'Rack'), ('shelf', 'Shelf'), ('bin', 'Bin'), ('floor', 'Floor')], default='bin', max_length=20)),
                ('capacity', models.PositiveIntegerField(blank=True, help_text='Storage capacity', null=True)),
                ('length', models.DecimalField(blank=True, decimal_places=2, help_text='Length in meters', max_digits=8, null=True)),
                ('width', models.DecimalField(blank=True, decimal_places=2, help_text='Width in meters', max_digits=8, null=True)),
                ('height', models.DecimalField(blank=True, decimal_places=2, help_text='Height in meters', max_digits=8, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_pickable', models.BooleanField(default=True, help_text='Can products be picked from this location?')),
                ('is_receivable', models.BooleanField(default=True, help_text='Can products be received into this location?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='warehouses.location')),
            ],
            options={
                'ordering': ['warehouse', 'code'],
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(max_length=10, unique=True, validators=[django.core.validators.RegexValidator('^[A-Z0-9]+$', 'Code must contain only uppercase letters and numbers')])),
                ('warehouse_type', models.CharField(choices=[('main', 'Main Warehouse'), ('branch', 'Branch Warehouse'), ('retail', 'Retail Store'), ('virtual', 'Virtual Warehouse'), ('transit', 'Transit Warehouse')], default='main', max_length=20)),
                ('address_line1', models.CharField(max_length=200)),
                ('address_line2', models.CharField(blank=True, max_length=200, null=True)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('postal_code', models.CharField(max_length=20)),
                ('country', models.CharField(default='USA', max_length=100)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=False)),
                ('capacity', models.PositiveIntegerField(blank=True, help_text='Storage capacity in cubic meters', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(default=0)),
                ('reserved_quantity', models.IntegerField(default=0, help_text='Quantity reserved for orders')),
                ('last_counted', models.DateTimeField(blank=True, null=True)),
                ('last_movement', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='warehouses.location')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product')),
                ('product_variant', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productvariant')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.warehouse')),
            ],
        ),
        migrations.AddField(
            model_name='location',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='warehouses.warehouse'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['code'], name='warehouses__code_d781d4_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['is_active'], name='warehouses__is_acti_0f15f9_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['product', 'warehouse'], name='warehouses__product_d90621_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['warehouse'], name='warehouses__warehou_94aed7_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['quantity'], name='warehouses__quantit_e13578_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stock',
            unique_together={('product', 'product_variant', 'warehouse', 'location')},
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['warehouse', 'code'], name='warehouses__warehou_1f07c9_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['is_active'], name='warehouses__is_acti_44c87f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='location',
            unique_together={('warehouse', 'code')},
        ),
    ]
