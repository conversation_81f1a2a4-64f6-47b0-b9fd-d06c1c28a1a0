{% extends 'base.html' %}

{% block title %}Categories - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Product Categories</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'products:list' %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-left"></i> Back to Products
        </a>
        {% if user.can_manage_inventory %}
        <a href="{% url 'products:category_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Category
        </a>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Categories ({{ categories|length }})</h5>
    </div>
    <div class="card-body">
        {% if categories %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Parent Category</th>
                            <th>Description</th>
                            <th>Products</th>
                            <th>Status</th>
                            <th>Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                            <tr>
                                <td><strong>{{ category.name }}</strong></td>
                                <td>{{ category.parent.name|default:"-" }}</td>
                                <td>{{ category.description|truncatechars:50|default:"-" }}</td>
                                <td>{{ category.products.count }}</td>
                                <td>
                                    {% if category.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>{{ category.created_at|date:"M d, Y" }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No categories found</h5>
                <p class="text-muted">Create your first product category to organize your products.</p>
                {% if user.can_manage_inventory %}
                    <a href="{% url 'products:category_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Category
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}
