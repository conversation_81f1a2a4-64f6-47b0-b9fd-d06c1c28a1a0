# Generated by Django 5.2.4 on 2025-07-17 08:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
        ('products', '0001_initial'),
        ('sales', '0001_initial'),
        ('warehouses', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='stockadjustment',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_adjustments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_adjustments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.warehouse'),
        ),
        migrations.AddField(
            model_name='stockadjustmentline',
            name='adjustment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='inventory.stockadjustment'),
        ),
        migrations.AddField(
            model_name='stockadjustmentline',
            name='location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='warehouses.location'),
        ),
        migrations.AddField(
            model_name='stockadjustmentline',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product'),
        ),
        migrations.AddField(
            model_name='stockadjustmentline',
            name='product_variant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productvariant'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='from_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_movements', to='warehouses.location'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='from_warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_movements', to='warehouses.warehouse'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.product'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='product_variant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productvariant'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='purchase_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.purchaseorder'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='sales_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesorder'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='to_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming_movements', to='warehouses.location'),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='to_warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming_movements', to='warehouses.warehouse'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['product', 'created_at'], name='inventory_s_product_5919a9_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['movement_type'], name='inventory_s_movemen_018f99_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['is_processed'], name='inventory_s_is_proc_d3131b_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['reference'], name='inventory_s_referen_16defd_idx'),
        ),
    ]
