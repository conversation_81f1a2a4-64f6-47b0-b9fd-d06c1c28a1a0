from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import User, UserProfile


@receiver(post_save, sender=User)
def create_or_update_user_profile(sender, instance, created, **kwargs):
    """
    Create or update UserProfile when a User is created or saved
    """
    if created:
        # Only create if it doesn't already exist
        UserProfile.objects.get_or_create(user=instance)
    else:
        # For existing users, ensure profile exists and save it
        if hasattr(instance, 'profile'):
            instance.profile.save()
        else:
            UserProfile.objects.get_or_create(user=instance)
