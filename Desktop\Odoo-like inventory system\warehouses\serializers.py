from rest_framework import serializers
from .models import Warehouse, Location, Stock


class WarehouseSerializer(serializers.ModelSerializer):
    manager_name = serializers.CharField(source='manager.get_full_name', read_only=True)
    total_products = serializers.IntegerField(read_only=True)
    total_stock_value = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    
    class Meta:
        model = Warehouse
        fields = [
            'id', 'name', 'code', 'warehouse_type', 'address_line1', 'address_line2',
            'city', 'state', 'postal_code', 'country', 'phone', 'email',
            'manager', 'manager_name', 'capacity', 'is_active', 'is_default',
            'total_products', 'total_stock_value', 'created_at', 'updated_at'
        ]


class LocationSerializer(serializers.ModelSerializer):
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    current_utilization = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = Location
        fields = [
            'id', 'warehouse', 'warehouse_name', 'name', 'code', 'location_type',
            'parent', 'parent_name', 'capacity', 'length', 'width', 'height',
            'is_active', 'is_pickable', 'is_receivable', 'current_utilization',
            'created_at', 'updated_at'
        ]


class StockSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    available_quantity = serializers.IntegerField(read_only=True)
    stock_value = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    is_low_stock = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Stock
        fields = [
            'id', 'product', 'product_name', 'product_sku', 'product_variant',
            'warehouse', 'warehouse_name', 'location', 'location_name',
            'quantity', 'reserved_quantity', 'available_quantity',
            'stock_value', 'is_low_stock', 'last_counted', 'last_movement',
            'created_at', 'updated_at'
        ]
