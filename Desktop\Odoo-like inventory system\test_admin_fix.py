#!/usr/bin/env python
"""
Test that the admin interface fix is working
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'inventory_system.settings')
django.setup()

from django.test import Client
from users.models import User

def test_admin_interface():
    """Test that the admin interface is working without errors"""
    print("🔧 Testing Admin Interface Fix...\n")
    
    try:
        # Create test client
        client = Client()
        
        # Get kenny user (ID: 3 from the error)
        try:
            kenny = User.objects.get(id=3)
            print(f"✅ Kenny user found:")
            print(f"   Username: {kenny.username}")
            print(f"   ID: {kenny.id}")
            print(f"   Email: {kenny.email}")
            print(f"   Date joined: {kenny.date_joined}")
            print(f"   Last login: {kenny.last_login}")
        except User.DoesNotExist:
            print("❌ User with ID 3 not found")
            return False
        
        # Login as admin to test admin interface
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            print("❌ No admin user found for testing")
            return False
        
        client.force_login(admin_user)
        print(f"✅ Logged in as admin: {admin_user.username}")
        
        # Test admin URLs that were failing
        test_urls = [
            ('/admin/', 'Admin Home'),
            ('/admin/users/user/', 'User List'),
            ('/admin/users/user/3/change/', 'Kenny User Edit'),
        ]
        
        print(f"\n🧪 Testing Admin URLs:")
        
        all_working = True
        
        for url, name in test_urls:
            try:
                response = client.get(url)
                
                if response.status_code == 200:
                    print(f"   ✅ {name}: Working (Status 200)")
                    
                    # Check for specific error messages
                    content = response.content.decode('utf-8')
                    if 'date_joined' in content and 'non-editable field' in content:
                        print(f"      ❌ Still has date_joined error")
                        all_working = False
                    elif 'FieldError' in content:
                        print(f"      ❌ Still has FieldError")
                        all_working = False
                    else:
                        print(f"      ✅ No field errors detected")
                        
                elif response.status_code == 302:
                    print(f"   🔄 {name}: Redirected (Status 302)")
                else:
                    print(f"   ❌ {name}: Error (Status {response.status_code})")
                    all_working = False
                    
            except Exception as e:
                print(f"   ❌ {name}: Exception - {str(e)}")
                all_working = False
        
        # Test that readonly fields are properly configured
        print(f"\n📋 Testing Admin Configuration:")
        
        from users.admin import UserAdmin
        from django.contrib import admin
        
        # Get the registered UserAdmin
        user_admin = admin.site._registry.get(User)
        
        if user_admin:
            print(f"   ✅ UserAdmin is registered")
            
            # Check readonly_fields
            readonly_fields = getattr(user_admin, 'readonly_fields', ())
            if 'date_joined' in readonly_fields:
                print(f"   ✅ date_joined is in readonly_fields")
            else:
                print(f"   ❌ date_joined is NOT in readonly_fields")
                all_working = False
            
            if 'last_login' in readonly_fields:
                print(f"   ✅ last_login is in readonly_fields")
            else:
                print(f"   ⚠️  last_login is not in readonly_fields (optional)")
            
            # Check list_display
            list_display = getattr(user_admin, 'list_display', ())
            if 'date_joined' in list_display:
                print(f"   ✅ date_joined is in list_display (this is OK)")
            
        else:
            print(f"   ❌ UserAdmin is not registered")
            all_working = False
        
        return all_working
        
    except Exception as e:
        print(f"❌ Error during admin testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_admin_solution():
    """Provide solution summary"""
    print(f"\n💡 Admin Interface Fix Summary:")
    print(f"")
    print(f"🔧 What was fixed:")
    print(f"   - Added 'readonly_fields = ('date_joined', 'last_login')' to UserAdmin")
    print(f"   - This tells Django these fields are read-only and not editable")
    print(f"   - Fields can still be displayed in list_display and list_filter")
    print(f"   - But they won't be included in the edit form")
    print(f"")
    print(f"✅ Result:")
    print(f"   - Admin interface now works without FieldError")
    print(f"   - You can edit users in /admin/users/user/")
    print(f"   - date_joined and last_login are visible but not editable")
    print(f"   - All other user fields are editable as normal")
    print(f"")
    print(f"🎯 Test URLs:")
    print(f"   - Admin Home: http://127.0.0.1:8000/admin/")
    print(f"   - User List: http://127.0.0.1:8000/admin/users/user/")
    print(f"   - Edit Kenny: http://127.0.0.1:8000/admin/users/user/3/change/")

if __name__ == "__main__":
    print("🚀 Admin Interface Fix Testing\n")
    
    success = test_admin_interface()
    provide_admin_solution()
    
    print(f"\n📋 Final Results:")
    if success:
        print(f"🎉 Admin interface fix successful!")
        print(f"✅ No more FieldError for date_joined")
        print(f"✅ User admin interface working properly")
        print(f"✅ All admin URLs accessible")
    else:
        print(f"⚠️  Admin interface still has issues")
        print(f"🔧 Review the test results above")
