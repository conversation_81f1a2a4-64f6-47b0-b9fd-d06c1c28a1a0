{% extends 'base.html' %}

{% block title %}{% if object %}Edit Product{% else %}Add Product{% endif %} - Inventory Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">{% if object %}Edit Product{% else %}Add Product{% endif %}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'products:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Products
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">Product Name *</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.sku.id_for_label }}" class="form-label">SKU</label>
                                {{ form.sku }}
                                {% if form.sku.errors %}
                                    <div class="text-danger small">{{ form.sku.errors.0 }}</div>
                                {% endif %}
                                <small class="text-muted">Leave empty to auto-generate</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                                {{ form.category }}
                                {% if form.category.errors %}
                                    <div class="text-danger small">{{ form.category.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.brand.id_for_label }}" class="form-label">Brand</label>
                                {{ form.brand }}
                                {% if form.brand.errors %}
                                    <div class="text-danger small">{{ form.brand.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.product_type.id_for_label }}" class="form-label">Product Type</label>
                                {{ form.product_type }}
                                {% if form.product_type.errors %}
                                    <div class="text-danger small">{{ form.product_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.unit.id_for_label }}" class="form-label">Unit</label>
                                {{ form.unit }}
                                {% if form.unit.errors %}
                                    <div class="text-danger small">{{ form.unit.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.short_description.id_for_label }}" class="form-label">Short Description</label>
                        {{ form.short_description }}
                        {% if form.short_description.errors %}
                            <div class="text-danger small">{{ form.short_description.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="text-danger small">{{ form.description.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.barcode.id_for_label }}" class="form-label">Barcode</label>
                        {{ form.barcode }}
                        {% if form.barcode.errors %}
                            <div class="text-danger small">{{ form.barcode.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Pricing -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Pricing</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.cost_price.id_for_label }}" class="form-label">Cost Price *</label>
                                {{ form.cost_price }}
                                {% if form.cost_price.errors %}
                                    <div class="text-danger small">{{ form.cost_price.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.selling_price.id_for_label }}" class="form-label">Selling Price *</label>
                                {{ form.selling_price }}
                                {% if form.selling_price.errors %}
                                    <div class="text-danger small">{{ form.selling_price.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Physical Properties -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Physical Properties</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.weight.id_for_label }}" class="form-label">Weight (kg)</label>
                                {{ form.weight }}
                                {% if form.weight.errors %}
                                    <div class="text-danger small">{{ form.weight.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.dimensions_length.id_for_label }}" class="form-label">Length (cm)</label>
                                {{ form.dimensions_length }}
                                {% if form.dimensions_length.errors %}
                                    <div class="text-danger small">{{ form.dimensions_length.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.dimensions_width.id_for_label }}" class="form-label">Width (cm)</label>
                                {{ form.dimensions_width }}
                                {% if form.dimensions_width.errors %}
                                    <div class="text-danger small">{{ form.dimensions_width.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.dimensions_height.id_for_label }}" class="form-label">Height (cm)</label>
                                {{ form.dimensions_height }}
                                {% if form.dimensions_height.errors %}
                                    <div class="text-danger small">{{ form.dimensions_height.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inventory Settings -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Inventory Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3 form-check">
                        {{ form.track_inventory }}
                        <label class="form-check-label" for="{{ form.track_inventory.id_for_label }}">
                            Track Inventory
                        </label>
                        {% if form.track_inventory.errors %}
                            <div class="text-danger small">{{ form.track_inventory.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.min_stock_level.id_for_label }}" class="form-label">Minimum Stock Level</label>
                                {{ form.min_stock_level }}
                                {% if form.min_stock_level.errors %}
                                    <div class="text-danger small">{{ form.min_stock_level.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.max_stock_level.id_for_label }}" class="form-label">Maximum Stock Level</label>
                                {{ form.max_stock_level }}
                                {% if form.max_stock_level.errors %}
                                    <div class="text-danger small">{{ form.max_stock_level.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media and Status -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Media and Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="{{ form.image.id_for_label }}" class="form-label">Product Image</label>
                        {{ form.image }}
                        {% if form.image.errors %}
                            <div class="text-danger small">{{ form.image.errors.0 }}</div>
                        {% endif %}
                        {% if object and object.image %}
                            <div class="mt-2">
                                <img src="{{ object.image.url }}" alt="Current image" class="img-thumbnail" style="max-width: 100px;">
                                <small class="text-muted d-block">Current image</small>
                            </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3 form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                                {% if form.is_active.errors %}
                                    <div class="text-danger small">{{ form.is_active.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3 form-check">
                                {{ form.is_featured }}
                                <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                    Featured Product
                                </label>
                                {% if form.is_featured.errors %}
                                    <div class="text-danger small">{{ form.is_featured.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between mt-3">
                <a href="{% url 'products:list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> {% if object %}Update Product{% else %}Create Product{% endif %}
                </button>
            </div>
        </form>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Help</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6>Product Information:</h6>
                    <ul class="mb-0">
                        <li><strong>SKU:</strong> Unique product identifier</li>
                        <li><strong>Cost Price:</strong> What you pay for the product</li>
                        <li><strong>Selling Price:</strong> What you charge customers</li>
                        <li><strong>Track Inventory:</strong> Enable stock tracking</li>
                        <li><strong>Min/Max Levels:</strong> For stock alerts</li>
                    </ul>
                </div>
            </div>
        </div>

        {% if object %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Product Statistics</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td>Created:</td>
                        <td>{{ object.created_at|date:"M d, Y" }}</td>
                    </tr>
                    <tr>
                        <td>Updated:</td>
                        <td>{{ object.updated_at|date:"M d, Y" }}</td>
                    </tr>
                    <tr>
                        <td>Total Stock:</td>
                        <td>{{ object.total_stock }}</td>
                    </tr>
                    <tr>
                        <td>Profit Margin:</td>
                        <td>{{ object.profit_margin|floatformat:2 }}%</td>
                    </tr>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

{% endblock %}
